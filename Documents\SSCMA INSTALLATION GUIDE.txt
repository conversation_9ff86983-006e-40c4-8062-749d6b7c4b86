# 🚀 **Complete SSCMA Setup Guide: From Git to GPU Training**

## 📋 **Step-by-Step Setup Guide**

### **Step 1: Install Git**

**1.1 Download and Install Git:**
- Go to: https://git-scm.com/downloads
- Download Git for Windows (latest version)
- Run the installer with default settings
- Verify installation: Open Command Prompt and run `git --version`

### **Step 2: Install Anaconda/Miniconda**

**2.1 Download Anaconda:**
- Go to: https://www.anaconda.com/download
- Download Anaconda for Windows (Python 3.x)
- Install with default settings
- Add <PERSON>conda to PATH when prompted

### **Step 3: Clone SSCMA Repository**

**3.1 Clone the Repository:**
```bash
# Open Anaconda Prompt or Command Prompt
cd D:\OBJECT_DETECTION
git clone https://github.com/Seeed-Studio/ModelAssistant.git sscma
cd sscma
```

### **Step 4: Create and Setup Conda Environment**

**4.1 Create Environment:**
```bash
# Create conda environment
conda create -n sscma python=3.12 -y
conda activate sscma
```

**4.2 Navigate to ModelAssistant:**
```bash
cd ModelAssistant
```

### **Step 5: Install Requirements**

**5.1 Install Basic Requirements:**
```bash
# Install basic requirements
pip install -r requirements.txt
```

**5.2 Install PyTorch with CUDA Support:**
```bash
# For RTX 5060 Ti - Install CUDA 12.8 nightly build
pip uninstall torch torchvision torchaudio -y
pip install --pre torch torchvision torchaudio --index-url https://download.pytorch.org/whl/nightly/cu128
```

**5.3 Install Additional Dependencies:**
```bash
# Install visualization dependencies
pip install matplotlib seaborn

# Install other ML dependencies
pip install opencv-python pillow numpy pandas scikit-learn
```

### **Step 6: Verify GPU Setup**

**6.1 Test CUDA Installation:**
```python
# Run this Python script to verify CUDA
python -c "
import torch
print('PyTorch version:', torch.__version__)
print('CUDA available:', torch.cuda.is_available())
print('CUDA version:', torch.version.cuda)
print('GPU count:', torch.cuda.device_count())
if torch.cuda.is_available():
    print('GPU name:', torch.cuda.get_device_name(0))
    print('GPU memory:', torch.cuda.get_device_properties(0).total_memory / 1024**3, 'GB')
"
```

**Expected Output:**
```
PyTorch version: 2.8.0.dev20250608+cu128
CUDA available: True
CUDA version: 12.8
GPU count: 1
GPU name: NVIDIA GeForce RTX 5060 Ti
GPU memory: 16.0 GB
```

### **Step 7: Prepare Your Dataset**

**7.1 Dataset Structure:**
```
D:\OBJECT_DETECTION\sscma\converted_datasets\motherboard_led_detection_coco\
├── annotations/
│   ├── instances_train2017.json
│   └── instances_val2017.json
├── train2017/
│   └── [your training images]
└── val2017/
    └── [your validation images]
```

### **Step 8: Configure Training**

**8.1 Check Available Configs:**
```bash
# List available RTMDet configurations
ls configs/rtmdet/
```

**8.2 Create/Use Training Config:**
- Use existing: `configs/rtmdet/rtmdet_nano_config_motherboard_led_192x192.py`
- Or modify for your specific needs

### **Step 9: Start GPU Training**

**9.1 Basic Training Command:**
```bash
# Activate environment first
conda activate sscma

# Run training with GPU
python tools/train.py configs/rtmdet/rtmdet_nano_config_motherboard_led_192x192.py \
  --cfg-options \
  data_root=D:\OBJECT_DETECTION\sscma\converted_datasets\motherboard_led_detection_coco \
  num_classes=2 \
  device='cuda' \
  imgsz='(192,192)' \
  train_ann_file=annotations/instances_train2017.json \
  val_ann_file=annotations/instances_val2017.json \
  test_evaluator.ann_file=D:\OBJECT_DETECTION\sscma\converted_datasets\motherboard_led_detection_coco\annotations\instances_val2017.json \
  val_evaluator.ann_file=D:\OBJECT_DETECTION\sscma\converted_datasets\motherboard_led_detection_coco\annotations\instances_val2017.json
```

### **Step 10: Monitor Training**

**10.1 Training Outputs:**
- **Logs**: Check terminal for training progress
- **Checkpoints**: Saved in `work_dirs/rtmdet_nano_config_motherboard_led_192x192/`
- **Tensorboard**: `tensorboard --logdir work_dirs/`

**10.2 Key Metrics to Watch:**
- **Loss**: Should decrease over time
- **mAP**: Mean Average Precision (validation metric)
- **GPU Utilization**: Should be high (80-95%)

## 🔧 **Troubleshooting Common Issues**

### **Issue 1: CUDA Not Available**
```bash
# Reinstall PyTorch with CUDA
pip uninstall torch torchvision torchaudio -y
pip install --pre torch torchvision torchaudio --index-url https://download.pytorch.org/whl/nightly/cu128
```

### **Issue 2: Missing Dependencies**
```bash
# Install missing packages
pip install matplotlib seaborn opencv-python pillow
```

### **Issue 3: Path Issues**
- Use forward slashes `/` or double backslashes `\\` in paths
- Ensure annotation files have `.json` extension

### **Issue 4: Memory Issues**
```bash
# Reduce batch size in config or command line
--cfg-options batch_size=16
```

## 📊 **Expected Training Performance**

**With RTX 5060 Ti (16GB):**
- **Batch Size**: 32-64 (depending on model)
- **Training Speed**: ~2-5 seconds per iteration
- **Memory Usage**: 8-12GB VRAM
- **Training Time**: 2-6 hours for 300 epochs

## 🎯 **Success Indicators**

✅ **Setup Complete When:**
- Git installed and working
- Conda environment created and activated
- All requirements installed without errors
- CUDA detected and GPU available
- Training starts without errors
- GPU utilization visible in Task Manager

✅ **Training Running Successfully When:**
- Loss values decreasing
- No CUDA out of memory errors
- Regular checkpoint saves
- Validation metrics improving

This complete guide should get you from zero to GPU training! 🚀
