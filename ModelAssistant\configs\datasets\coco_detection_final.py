# Copyright (c) OpenMMLab. All rights reserved.
from mmengine.dataset.sampler import Default<PERSON>ampler
from sscma.datasets import CocoDataset, coco_collate, BatchShapePolicy,CustomFomoCocoDataset
from sscma.datasets.transforms import (
    LoadAnnotations,
    PackDetInputs,
    RandomFlip,
    Resize,
    LoadImageFromFile,
)
from sscma.evaluation import CocoMetric

# dataset settings
dataset_type = CustomFomoCocoDataset
data_root = "../datasets/motherboard_led_detection_coco/"
train_ann_file = "annotations/instances_train2017.json"
val_ann_file = "annotations/instances_val2017.json"
train_img_prefix = "train2017/"
val_img_prefix = "val2017/"

# Example to use different file client
# Method 1: simply set the data root and let the file I/O module
# automatically infer from prefix (not support LMDB and Memcache yet)

# data_root = 's3://openmmlab/datasets/detection/coco/'

# Method 2: Use `backend_args`, `file_client_args` in versions before 3.0.0rc6
# backend_args = dict(
#     backend='petrel',
#     path_mapping=dict({
#         './data/': 's3://openmmlab/datasets/detection/',
#         'data/': 's3://openmmlab/datasets/detection/'
#     }))
backend_args = None


# Config of batch shapes. Only on val.
batch_shapes_cfg = dict(
    type=BatchShapePolicy,
    batch_size=32,
    img_size=640,
    size_divisor=32,
    extra_pad_ratio=0.5,
)


train_pipeline = [
    dict(type=LoadImageFromFile, backend_args=backend_args),
    dict(type=LoadAnnotations, with_bbox=True),
    dict(type=Resize, scale=(1333, 800), keep_ratio=True),
    dict(type=RandomFlip, prob=0.5),
    dict(type=PackDetInputs),
]
test_pipeline = [
    dict(type=LoadImageFromFile, backend_args=backend_args),
    dict(type=Resize, scale=(1333, 800), keep_ratio=True),
    # If you don't have a gt annotation, delete the pipeline
    dict(type=LoadAnnotations, with_bbox=True),
    dict(
        type=PackDetInputs,
        meta_keys=("img_id", "img_path", "ori_shape", "img_shape", "scale_factor"),
    ),
]
train_dataloader = dict(
    batch_size=32,
    num_workers=4,
    persistent_workers=True,
    pin_memory=True,
    collate_fn=dict(type=coco_collate),
    sampler=dict(type=DefaultSampler, shuffle=True),
    # batch_sampler=dict(type=AspectRatioBatchSampler),
    dataset=dict(
        type=dataset_type,
        data_root=data_root,
        ann_file=train_ann_file,
        data_prefix=dict(img=train_img_prefix),
        filter_cfg=dict(filter_empty_gt=True, min_size=32),
        pipeline=train_pipeline,
    ),
)
val_dataloader = dict(
    batch_size=1,
    num_workers=2,
    persistent_workers=True,
    pin_memory=True,
    drop_last=False,
    sampler=dict(type=DefaultSampler, shuffle=False),
    dataset=dict(
        type=dataset_type,
        data_root=data_root,
        ann_file=val_ann_file,
        data_prefix=dict(img=val_img_prefix),
        test_mode=True,
        pipeline=test_pipeline,
        # batch_shapes_cfg=batch_shapes_cfg,
    ),
)
test_dataloader = val_dataloader

val_evaluator = dict(
    type=CocoMetric,
    ann_file=data_root + val_ann_file,
    metric="bbox",
    format_only=False,
    backend_args=backend_args,
    sort_categories=True
)
test_evaluator = val_evaluator

# inference on test dataset and
# format the output results for submission.
# test_dataloader = dict(
#     batch_size=1,
#     num_workers=2,
#     persistent_workers=True,
#     drop_last=False,
#     sampler=dict(type=DefaultSampler, shuffle=False),
#     dataset=dict(
#         type=dataset_type,
#         data_root=data_root,
#         ann_file=data_root + 'annotations/image_info_test-dev2017.json',
#         data_prefix=dict(img='test2017/'),
#         test_mode=True,
#         pipeline=test_pipeline))
# test_evaluator = dict(
#     type=CocoMetric,
#     metric='bbox',
#     format_only=True,
#     ann_file=data_root + 'annotations/image_info_test-dev2017.json',
#     outfile_prefix='./work_dirs/coco_detection/test')
