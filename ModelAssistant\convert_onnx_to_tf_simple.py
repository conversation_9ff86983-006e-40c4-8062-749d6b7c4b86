#!/usr/bin/env python3
"""
Simple ONNX to TensorFlow conversion script
"""

import os
import sys
import subprocess

def convert_onnx_to_tensorflow():
    """Convert ONNX model to TensorFlow using onnx2tf"""
    
    onnx_path = "work_dirs/rtmdet_nano_8xb32_300e_coco_relu_q/epoch_300_2classes.onnx"
    output_dir = "work_dirs/rtmdet_nano_8xb32_300e_coco_relu_q/tensorflow_2classes"
    
    print("🔄 Converting 2-class ONNX to TensorFlow SavedModel...")
    print("=" * 60)
    print(f"📂 Input ONNX: {onnx_path}")
    print(f"📂 Output dir: {output_dir}")
    
    # Check if ONNX file exists
    if not os.path.exists(onnx_path):
        print(f"❌ ONNX file not found: {onnx_path}")
        return False
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    print("\n🚀 Starting conversion using onnx2tf...")
    
    try:
        # Use onnx2tf command line tool
        cmd = [
            "python", "-c",
            f"""
import onnx2tf
import os

print("Loading ONNX model...")
onnx2tf.convert(
    input_onnx_file_path='{onnx_path}',
    output_folder_path='{output_dir}',
    output_signaturedefs=True,
    non_verbose=True
)
print("✅ Conversion completed!")
"""
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✅ Conversion completed successfully!")
            print(result.stdout)
            
            # Check if SavedModel was created
            saved_model_path = os.path.join(output_dir, "saved_model.pb")
            if os.path.exists(saved_model_path):
                print(f"📁 SavedModel created: {saved_model_path}")
                return True
            else:
                print("⚠️ SavedModel file not found, but conversion completed")
                return True
                
        else:
            print(f"❌ Conversion failed with return code: {result.returncode}")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Conversion timed out after 5 minutes")
        return False
    except Exception as e:
        print(f"❌ Conversion failed with exception: {str(e)}")
        return False

def verify_tensorflow_model(model_dir):
    """Verify the converted TensorFlow model"""
    print(f"\n🧪 Verifying TensorFlow model in: {model_dir}")
    
    try:
        import tensorflow as tf
        
        # Load the SavedModel
        model = tf.saved_model.load(model_dir)
        print("✅ TensorFlow SavedModel loaded successfully!")
        
        # Get model signatures
        signatures = list(model.signatures.keys())
        print(f"📋 Available signatures: {signatures}")
        
        if 'serving_default' in signatures:
            serving_fn = model.signatures['serving_default']
            print("📊 Serving signature info:")
            print(f"   Inputs: {list(serving_fn.structured_input_signature[1].keys())}")
            print(f"   Outputs: {list(serving_fn.structured_outputs.keys())}")
        
        return True
        
    except Exception as e:
        print(f"❌ Verification failed: {str(e)}")
        return False

def main():
    """Main conversion function"""
    print("🚀 ONNX to TensorFlow Conversion Tool")
    print("=" * 50)
    
    # Convert ONNX to TensorFlow
    success = convert_onnx_to_tensorflow()
    
    if success:
        output_dir = "work_dirs/rtmdet_nano_8xb32_300e_coco_relu_q/tensorflow_2classes"
        
        # Verify the converted model
        verify_success = verify_tensorflow_model(output_dir)
        
        if verify_success:
            print("\n🎉 Conversion Summary:")
            print("✅ Successfully converted 2-class ONNX to TensorFlow")
            print("✅ TensorFlow SavedModel is ready for deployment")
            print(f"📁 Location: {output_dir}")
            
            print("\n📝 Usage example:")
            print(f"import tensorflow as tf")
            print(f"model = tf.saved_model.load('{output_dir}')")
            print(f"# Use model for inference")
            
        else:
            print("\n⚠️ Conversion completed but verification failed")
            print("The model might still be usable")
    else:
        print("\n❌ Conversion failed!")
        print("You can still use the ONNX model for deployment")
    
    return success

if __name__ == "__main__":
    main()
