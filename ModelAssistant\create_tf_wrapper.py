#!/usr/bin/env python3
"""
Create a TensorFlow-compatible wrapper for the ONNX model
This approach uses ONNX Runtime within a TensorFlow SavedModel
"""

import os
import numpy as np
import tensorflow as tf
import onnxruntime as ort

class ONNXTensorFlowWrapper(tf.Module):
    """TensorFlow Module that wraps an ONNX model using ONNX Runtime"""
    
    def __init__(self, onnx_path):
        super().__init__()
        self.onnx_path = onnx_path
        
        # Load ONNX session
        self.session = ort.InferenceSession(onnx_path)
        
        # Get input/output info
        self.input_name = self.session.get_inputs()[0].name
        self.output_names = [output.name for output in self.session.get_outputs()]
        
        # Get input shape
        input_shape = self.session.get_inputs()[0].shape
        self.input_shape = [dim if isinstance(dim, int) else 1 for dim in input_shape]
        
        print(f"✅ ONNX model loaded: {onnx_path}")
        print(f"📊 Input: {self.input_name} {self.input_shape}")
        print(f"📊 Outputs: {len(self.output_names)} tensors")
    
    @tf.function(input_signature=[tf.TensorSpec(shape=[1, 3, 192, 192], dtype=tf.float32)])
    def __call__(self, images):
        """Run inference on the ONNX model"""
        
        def onnx_inference(x):
            # Convert to numpy
            x_np = x.numpy().astype(np.float32)
            
            # Run ONNX inference
            outputs = self.session.run(self.output_names, {self.input_name: x_np})
            
            # Convert back to TensorFlow tensors
            tf_outputs = []
            for output in outputs:
                tf_outputs.append(tf.convert_to_tensor(output, dtype=tf.float32))
            
            return tf_outputs
        
        # Use tf.py_function to call ONNX Runtime
        outputs = tf.py_function(
            func=onnx_inference,
            inp=[images],
            Tout=[tf.float32] * len(self.output_names)
        )
        
        # Set output shapes based on ONNX model
        for i, output in enumerate(outputs):
            onnx_output_shape = self.session.get_outputs()[i].shape
            # Convert ONNX shape to TensorFlow shape (handle dynamic dimensions)
            tf_shape = []
            for dim in onnx_output_shape:
                if isinstance(dim, int):
                    tf_shape.append(dim)
                else:
                    tf_shape.append(None)  # Dynamic dimension
            output.set_shape(tf_shape)
        
        # Return as dictionary for easier access
        output_dict = {}
        for i, output in enumerate(outputs):
            output_dict[f'output_{i}'] = output
        
        return output_dict

def create_tensorflow_savedmodel(onnx_path, output_dir):
    """Create a TensorFlow SavedModel that wraps the ONNX model"""
    
    print("🔄 Creating TensorFlow SavedModel wrapper...")
    print("=" * 60)
    print(f"📂 ONNX model: {onnx_path}")
    print(f"📂 Output dir: {output_dir}")
    
    try:
        # Create the wrapper
        wrapper = ONNXTensorFlowWrapper(onnx_path)
        
        # Create output directory
        os.makedirs(output_dir, exist_ok=True)
        
        # Save as TensorFlow SavedModel
        print("\n💾 Saving TensorFlow SavedModel...")
        tf.saved_model.save(
            wrapper,
            output_dir,
            signatures={
                'serving_default': wrapper.__call__.get_concrete_function()
            }
        )
        
        print("✅ TensorFlow SavedModel created successfully!")
        
        # Test the saved model
        print("\n🧪 Testing the saved model...")
        loaded_model = tf.saved_model.load(output_dir)
        
        # Create test input
        test_input = tf.random.normal([1, 3, 192, 192])
        print(f"📊 Test input shape: {test_input.shape}")
        
        # Run inference
        outputs = loaded_model(test_input)
        print(f"✅ Test inference successful!")
        print(f"📊 Output keys: {list(outputs.keys())}")
        
        for key, value in outputs.items():
            print(f"   {key}: shape {value.shape}, dtype {value.dtype}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating TensorFlow SavedModel: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def verify_model_compatibility(savedmodel_dir):
    """Verify that the SavedModel works correctly"""
    
    print(f"\n🔍 Verifying SavedModel: {savedmodel_dir}")
    
    try:
        # Load the model
        model = tf.saved_model.load(savedmodel_dir)
        
        # Check signatures
        signatures = list(model.signatures.keys())
        print(f"📋 Available signatures: {signatures}")
        
        if 'serving_default' in signatures:
            serving_fn = model.signatures['serving_default']
            print("📊 Serving signature:")
            
            # Get input spec
            input_spec = serving_fn.structured_input_signature[1]
            print(f"   Inputs: {list(input_spec.keys())}")
            for name, spec in input_spec.items():
                print(f"     {name}: {spec}")
            
            # Get output spec
            output_spec = serving_fn.structured_outputs
            print(f"   Outputs: {list(output_spec.keys())}")
            for name, spec in output_spec.items():
                print(f"     {name}: {spec}")
        
        # Test inference with serving signature
        print("\n🧪 Testing serving signature...")
        test_input = tf.random.normal([1, 3, 192, 192])
        
        if 'serving_default' in signatures:
            serving_fn = model.signatures['serving_default']
            outputs = serving_fn(images=test_input)
            print("✅ Serving signature test successful!")
            print(f"📊 Outputs: {list(outputs.keys())}")
        
        return True
        
    except Exception as e:
        print(f"❌ Verification failed: {str(e)}")
        return False

def main():
    """Main function"""
    print("🚀 ONNX to TensorFlow Wrapper Creator")
    print("=" * 50)
    
    # Paths
    onnx_path = "work_dirs/rtmdet_nano_8xb32_300e_coco_relu_q/epoch_300_2classes.onnx"
    output_dir = "work_dirs/rtmdet_nano_8xb32_300e_coco_relu_q/tensorflow_2classes_wrapper"
    
    # Check if ONNX file exists
    if not os.path.exists(onnx_path):
        print(f"❌ ONNX file not found: {onnx_path}")
        return False
    
    # Create TensorFlow SavedModel wrapper
    success = create_tensorflow_savedmodel(onnx_path, output_dir)
    
    if success:
        # Verify the model
        verify_success = verify_model_compatibility(output_dir)
        
        if verify_success:
            print("\n🎉 Success Summary:")
            print("✅ TensorFlow SavedModel wrapper created successfully")
            print("✅ Model is compatible with TensorFlow Serving")
            print("✅ Ready for deployment")
            print(f"📁 Location: {output_dir}")
            
            print("\n📝 Usage Example:")
            print(f"import tensorflow as tf")
            print(f"model = tf.saved_model.load('{output_dir}')")
            print(f"outputs = model(tf.random.normal([1, 3, 192, 192]))")
            print(f"# or use serving signature:")
            print(f"serving_fn = model.signatures['serving_default']")
            print(f"outputs = serving_fn(images=input_tensor)")
            
        else:
            print("\n⚠️ Model created but verification failed")
    else:
        print("\n❌ Failed to create TensorFlow SavedModel wrapper")
    
    return success

if __name__ == "__main__":
    main()
