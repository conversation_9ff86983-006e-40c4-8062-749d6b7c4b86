#!/usr/bin/env python3
"""
Convert RTMDet model from 80 classes to 2 classes
Maintains the same layer structure but adapts classification heads
"""

import torch
import numpy as np
from mmengine.config import Config
from mmengine.runner import Runner
import os

def convert_model_to_2_classes(input_model_path, output_model_path, config_path):
    """
    Convert a trained RTMDet model from 80 classes to 2 classes
    
    Args:
        input_model_path (str): Path to the original 80-class model
        output_model_path (str): Path to save the 2-class model
        config_path (str): Path to the config file (should be set to 2 classes)
    """
    print("🔄 Converting RTMDet model from 80 classes to 2 classes...")
    print(f"📂 Input model: {input_model_path}")
    print(f"📂 Output model: {output_model_path}")
    print(f"📂 Config: {config_path}")
    
    # Load the original checkpoint
    print("\n📥 Loading original checkpoint...")
    checkpoint = torch.load(input_model_path, map_location='cpu')
    
    # Load config to build new model
    print("📥 Loading config...")
    cfg = Config.fromfile(config_path)
    
    # Build the new model with 2 classes
    print("🏗️ Building new model with 2 classes...")
    runner = Runner.from_cfg(cfg)
    new_model = runner.model
    
    # Get the new model's state dict
    new_state_dict = new_model.state_dict()
    old_state_dict = checkpoint['state_dict']
    
    print("\n🔧 Converting model weights...")
    
    # Copy all weights except classification heads
    converted_count = 0
    classification_layers = []
    
    for key in new_state_dict.keys():
        if key in old_state_dict:
            if 'rtm_cls' in key:
                # This is a classification layer - needs special handling
                classification_layers.append(key)
                print(f"   📋 Found classification layer: {key}")
            else:
                # Copy directly for non-classification layers
                new_state_dict[key] = old_state_dict[key]
                converted_count += 1
    
    print(f"✅ Copied {converted_count} layers directly")
    
    # Handle classification layers
    print(f"\n🎯 Converting {len(classification_layers)} classification layers...")
    
    for cls_key in classification_layers:
        old_tensor = old_state_dict[cls_key]
        new_tensor = new_state_dict[cls_key]
        
        print(f"   🔄 {cls_key}: {old_tensor.shape} → {new_tensor.shape}")
        
        if 'weight' in cls_key:
            # For weights: take first 2 classes from the 80 classes
            # This preserves some learned features from the original model
            new_state_dict[cls_key] = old_tensor[:2].clone()
            
        elif 'bias' in cls_key:
            # For biases: take first 2 classes
            new_state_dict[cls_key] = old_tensor[:2].clone()
    
    # Update the checkpoint
    checkpoint['state_dict'] = new_state_dict
    
    # Update metadata
    if 'meta' in checkpoint:
        checkpoint['meta']['config'] = cfg.pretty_text
    
    # Save the converted model
    print(f"\n💾 Saving converted model to: {output_model_path}")
    os.makedirs(os.path.dirname(output_model_path), exist_ok=True)
    torch.save(checkpoint, output_model_path)
    
    print("✅ Model conversion completed successfully!")
    
    # Verify the conversion
    print("\n🧪 Verifying converted model...")
    verify_model(output_model_path, config_path)
    
    return True

def verify_model(model_path, config_path):
    """Verify that the converted model works correctly"""
    try:
        # Load config and model
        cfg = Config.fromfile(config_path)
        runner = Runner.from_cfg(cfg)
        runner.load_checkpoint(model_path)
        
        # Test inference
        print("   🔍 Testing model inference...")
        dummy_input = torch.randn(1, 3, 192, 192)
        
        with torch.no_grad():
            outputs = runner.model(dummy_input)
        
        print(f"   ✅ Model inference successful!")
        print(f"   📊 Output shapes: {[out.shape for out in outputs]}")
        
        # Check classification outputs
        for i, output in enumerate(outputs):
            if len(output.shape) == 4 and output.shape[1] == 2:  # Classification output
                print(f"   🎯 Classification head {i}: {output.shape} (2 classes ✓)")
            elif len(output.shape) == 4 and output.shape[1] == 4:  # Regression output
                print(f"   📦 Regression head {i}: {output.shape} (4 coordinates ✓)")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Verification failed: {str(e)}")
        return False

def analyze_model_differences(original_path, converted_path):
    """Analyze the differences between original and converted models"""
    print("\n📊 Analyzing model differences...")
    
    orig_checkpoint = torch.load(original_path, map_location='cpu')
    conv_checkpoint = torch.load(converted_path, map_location='cpu')
    
    orig_state = orig_checkpoint['state_dict']
    conv_state = conv_checkpoint['state_dict']
    
    print(f"Original model layers: {len(orig_state)}")
    print(f"Converted model layers: {len(conv_state)}")
    
    # Check classification layers
    print("\nClassification layer changes:")
    for key in conv_state.keys():
        if 'rtm_cls' in key:
            orig_shape = orig_state[key].shape if key in orig_state else "N/A"
            conv_shape = conv_state[key].shape
            print(f"  {key}: {orig_shape} → {conv_shape}")

def main():
    """Main conversion function"""
    print("🚀 RTMDet Model Class Conversion Tool")
    print("=" * 60)
    
    # Paths
    input_model = "work_dirs/rtmdet_nano_8xb32_300e_coco_relu_q/epoch_300.pth"
    output_model = "work_dirs/rtmdet_nano_8xb32_300e_coco_relu_q/epoch_300_2classes.pth"
    config_file = "configs/rtmdet/rtmdet_nano_8xb32_300e_coco_relu_q.py"
    
    # Check if input model exists
    if not os.path.exists(input_model):
        print(f"❌ Input model not found: {input_model}")
        return False
    
    # Convert the model
    success = convert_model_to_2_classes(input_model, output_model, config_file)
    
    if success:
        print("\n🎉 Conversion Summary:")
        print("✅ Successfully converted model from 80 to 2 classes")
        print("✅ Maintained all layer structures")
        print("✅ Preserved learned features for first 2 classes")
        print(f"📁 New model saved at: {output_model}")
        
        # Analyze differences
        analyze_model_differences(input_model, output_model)
        
        print("\n📝 Next steps:")
        print("1. Use the new 2-class model for inference")
        print("2. Export the 2-class model to ONNX/TensorFlow")
        print("3. Fine-tune on your specific 2-class dataset if needed")
        
    else:
        print("\n❌ Conversion failed!")
    
    return success

if __name__ == "__main__":
    main()
