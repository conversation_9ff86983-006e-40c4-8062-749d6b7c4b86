#!/usr/bin/env python3
"""
Create a TensorFlow model wrapper for the ONNX model
"""

import os
import numpy as np
import tensorflow as tf
import onnxruntime as ort

class ONNXModelWrapper:
    """Wrapper class to use ONNX model with TensorFlow-like interface"""
    
    def __init__(self, onnx_path):
        self.session = ort.InferenceSession(onnx_path)
        self.input_name = self.session.get_inputs()[0].name
        self.output_names = [output.name for output in self.session.get_outputs()]
        
        # Get input shape
        input_shape = self.session.get_inputs()[0].shape
        self.input_shape = [dim if isinstance(dim, int) else 1 for dim in input_shape]
        
        print(f"Model loaded successfully!")
        print(f"Input name: {self.input_name}")
        print(f"Input shape: {self.input_shape}")
        print(f"Output names: {self.output_names}")
    
    def predict(self, input_data):
        """Run inference on the ONNX model"""
        if isinstance(input_data, tf.Tensor):
            input_data = input_data.numpy()
        
        # Ensure input is float32
        input_data = input_data.astype(np.float32)
        
        # Run inference
        outputs = self.session.run(self.output_names, {self.input_name: input_data})
        
        return outputs

def create_tensorflow_savedmodel(onnx_path, output_dir):
    """
    Create a TensorFlow SavedModel that wraps the ONNX model
    """
    try:
        print(f"Creating TensorFlow SavedModel wrapper for: {onnx_path}")
        
        # Load ONNX model
        wrapper = ONNXModelWrapper(onnx_path)
        
        # Create TensorFlow function
        @tf.function
        def model_fn(images):
            # Convert to numpy for ONNX inference
            def onnx_inference(x):
                # This will be called in eager mode
                outputs = wrapper.predict(x.numpy())
                # Convert outputs back to tensors
                return [tf.convert_to_tensor(out, dtype=tf.float32) for out in outputs]
            
            # Use tf.py_function to call the ONNX model
            outputs = tf.py_function(
                func=lambda x: onnx_inference(x),
                inp=[images],
                Tout=[tf.float32] * len(wrapper.output_names)
            )
            
            # Set shapes for the outputs
            for i, output in enumerate(outputs):
                # Get output shape from ONNX model
                onnx_output_shape = wrapper.session.get_outputs()[i].shape
                tf_shape = [dim if isinstance(dim, int) else None for dim in onnx_output_shape]
                output.set_shape(tf_shape)
            
            return {f'output_{i}': output for i, output in enumerate(outputs)}
        
        # Create concrete function with input signature
        input_signature = tf.TensorSpec(shape=wrapper.input_shape, dtype=tf.float32, name='images')
        concrete_function = model_fn.get_concrete_function(input_signature)
        
        # Create SavedModel
        os.makedirs(output_dir, exist_ok=True)
        
        # Save the model
        tf.saved_model.save(
            obj=concrete_function,
            export_dir=output_dir,
            signatures={'serving_default': concrete_function}
        )
        
        print(f"✅ TensorFlow SavedModel created successfully!")
        print(f"📁 Saved at: {output_dir}")
        
        # Test the saved model
        print("\n🧪 Testing the saved model...")
        loaded_model = tf.saved_model.load(output_dir)
        
        # Create test input
        test_input = tf.random.normal(wrapper.input_shape)
        print(f"Test input shape: {test_input.shape}")
        
        # Run inference
        outputs = loaded_model(test_input)
        print(f"Test outputs: {list(outputs.keys())}")
        for key, value in outputs.items():
            print(f"  {key}: shape {value.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating TensorFlow SavedModel: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    # Paths
    onnx_path = "work_dirs/rtmdet_nano_8xb32_300e_coco_relu_q/epoch_300.onnx"
    output_dir = "work_dirs/rtmdet_nano_8xb32_300e_coco_relu_q/tensorflow_savedmodel_v2"
    
    # Check if ONNX file exists
    if not os.path.exists(onnx_path):
        print(f"❌ ONNX file not found: {onnx_path}")
        return False
    
    # Create TensorFlow SavedModel
    success = create_tensorflow_savedmodel(onnx_path, output_dir)
    
    if success:
        print("\n🎉 TensorFlow SavedModel creation completed!")
        print(f"📁 Location: {output_dir}")
        print("\n📝 Usage example:")
        print(f"import tensorflow as tf")
        print(f"model = tf.saved_model.load('{output_dir}')")
        print(f"outputs = model(tf.random.normal([1, 3, 192, 192]))")
    else:
        print("\n❌ TensorFlow SavedModel creation failed!")
    
    return success

if __name__ == "__main__":
    main()
