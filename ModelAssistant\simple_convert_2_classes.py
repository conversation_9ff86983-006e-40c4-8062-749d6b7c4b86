#!/usr/bin/env python3
"""
Simple script to convert RTMDet model from 80 classes to 2 classes
"""

import torch
import os

def convert_model_weights():
    """Convert model weights from 80 to 2 classes"""
    
    input_path = "work_dirs/rtmdet_nano_8xb32_300e_coco_relu_q/epoch_300.pth"
    output_path = "work_dirs/rtmdet_nano_8xb32_300e_coco_relu_q/epoch_300_2classes.pth"
    
    print("🔄 Converting RTMDet model from 80 classes to 2 classes...")
    print(f"📂 Input: {input_path}")
    print(f"📂 Output: {output_path}")
    
    # Load checkpoint
    print("📥 Loading checkpoint...")
    checkpoint = torch.load(input_path, map_location='cpu')
    state_dict = checkpoint['state_dict']
    
    print("🔧 Converting classification layers...")
    
    # Find and convert classification layers
    cls_layers_converted = 0
    
    for key in list(state_dict.keys()):
        if 'rtm_cls' in key:
            old_tensor = state_dict[key]
            print(f"   Converting {key}: {old_tensor.shape}", end="")
            
            if 'weight' in key:
                # Take first 2 classes from 80 classes
                new_tensor = old_tensor[:2].clone()
            elif 'bias' in key:
                # Take first 2 classes from 80 classes
                new_tensor = old_tensor[:2].clone()
            else:
                new_tensor = old_tensor
            
            state_dict[key] = new_tensor
            print(f" → {new_tensor.shape}")
            cls_layers_converted += 1
    
    print(f"✅ Converted {cls_layers_converted} classification layers")
    
    # Update checkpoint
    checkpoint['state_dict'] = state_dict
    
    # Save converted model
    print("💾 Saving converted model...")
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    torch.save(checkpoint, output_path)
    
    print("✅ Conversion completed!")
    
    # Verify file was created
    if os.path.exists(output_path):
        file_size = os.path.getsize(output_path) / (1024 * 1024)  # MB
        print(f"📁 Output file: {output_path} ({file_size:.1f} MB)")
        return True
    else:
        print("❌ Output file was not created!")
        return False

def verify_conversion():
    """Verify the conversion worked correctly"""
    
    original_path = "work_dirs/rtmdet_nano_8xb32_300e_coco_relu_q/epoch_300.pth"
    converted_path = "work_dirs/rtmdet_nano_8xb32_300e_coco_relu_q/epoch_300_2classes.pth"
    
    print("\n🧪 Verifying conversion...")
    
    # Load both models
    orig_checkpoint = torch.load(original_path, map_location='cpu')
    conv_checkpoint = torch.load(converted_path, map_location='cpu')
    
    orig_state = orig_checkpoint['state_dict']
    conv_state = conv_checkpoint['state_dict']
    
    print("📊 Comparison:")
    print(f"   Original model layers: {len(orig_state)}")
    print(f"   Converted model layers: {len(conv_state)}")
    
    # Check classification layers
    cls_layers_found = 0
    for key in conv_state.keys():
        if 'rtm_cls' in key:
            orig_shape = orig_state[key].shape
            conv_shape = conv_state[key].shape
            print(f"   {key}: {orig_shape} → {conv_shape}")
            cls_layers_found += 1
    
    print(f"✅ Found {cls_layers_found} converted classification layers")
    
    # Check that regression layers are unchanged
    reg_layers_unchanged = 0
    for key in conv_state.keys():
        if 'rtm_reg' in key:
            if torch.equal(orig_state[key], conv_state[key]):
                reg_layers_unchanged += 1
    
    print(f"✅ {reg_layers_unchanged} regression layers unchanged (as expected)")
    
    return True

def main():
    """Main function"""
    print("🚀 Simple RTMDet Class Conversion")
    print("=" * 50)
    
    # Convert the model
    success = convert_model_weights()
    
    if success:
        # Verify the conversion
        verify_conversion()
        
        print("\n🎉 Summary:")
        print("✅ Model successfully converted from 80 to 2 classes")
        print("✅ Classification layers resized to 2 classes")
        print("✅ Regression layers preserved")
        print("✅ Model ready for 2-class inference")
        
        print("\n📝 Usage:")
        print("1. Use epoch_300_2classes.pth for inference")
        print("2. Export to ONNX with the 2-class config")
        print("3. The model now outputs 2 classes instead of 80")
        
    else:
        print("\n❌ Conversion failed!")
    
    return success

if __name__ == "__main__":
    main()
