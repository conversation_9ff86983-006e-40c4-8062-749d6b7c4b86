#!/usr/bin/env python3
"""
Wrapper script to run export.py with torchvision compatibility fixes
"""

import os
import sys
import subprocess

def main():
    # Set environment variables to help with compatibility
    os.environ['TF_ENABLE_ONEDNN_OPTS'] = '0'
    os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'
    
    # Try to patch the torchvision issue by temporarily modifying the import
    try:
        # Import torch first to initialize it properly
        import torch
        print(f"PyTorch version: {torch.__version__}")
        
        # Try to import torchvision and handle the error
        try:
            import torchvision
            print(f"Torchvision version: {torchvision.__version__}")
        except Exception as e:
            print(f"Torchvision import issue (continuing anyway): {e}")
        
        # Now run the actual export script
        export_args = [
            sys.executable,
            "ModelAssistant/tools/export.py",
            "ModelAssistant/work_dirs/rtmdet_nano_motherboard_final/rtmdet_nano_motherboard_final.py",
            "ModelAssistant/work_dirs/rtmdet_nano_motherboard_final/best_coco_bbox_mAP_epoch_270.pth",
            "--format", "tflite",
            "--img-size", "192", "192",
            "--image_path", "datasets/motherboard_led_detection_coco/train2017",
            "--device", "cpu"
        ]
        
        print("Running export command:")
        print(" ".join(export_args))
        
        # Run the export script
        result = subprocess.run(export_args, capture_output=True, text=True)
        
        print("STDOUT:")
        print(result.stdout)
        
        if result.stderr:
            print("STDERR:")
            print(result.stderr)
        
        print(f"Return code: {result.returncode}")
        
        if result.returncode == 0:
            print("✅ Export completed successfully!")
        else:
            print("❌ Export failed")
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
