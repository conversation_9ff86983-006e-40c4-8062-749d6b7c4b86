#!/usr/bin/env python3
"""
Simple export script to convert PyTorch model to TensorFlow Lite (int8)
This script bypasses the problematic SSCMA imports and uses direct PyTorch/TensorFlow conversion.
"""

import os
import sys
import argparse
import torch
import numpy as np
from pathlib import Path

def parse_args():
    parser = argparse.ArgumentParser(description="Simple model export to TFLite")
    parser.add_argument("checkpoint", help="PyTorch checkpoint file (.pth)")
    parser.add_argument("--img-size", nargs=2, type=int, default=[192, 192], 
                       help="Input image size (height width)")
    parser.add_argument("--output-dir", type=str, default=None,
                       help="Output directory (default: same as checkpoint)")
    parser.add_argument("--calibration-images", type=str, default=None,
                       help="Directory containing calibration images for quantization")
    return parser.parse_args()

def load_pytorch_model(checkpoint_path):
    """Load PyTorch model from checkpoint"""
    print(f"Loading PyTorch model from: {checkpoint_path}")
    
    # Load the checkpoint
    checkpoint = torch.load(checkpoint_path, map_location='cpu')
    
    # Extract model state dict
    if 'state_dict' in checkpoint:
        state_dict = checkpoint['state_dict']
    elif 'model' in checkpoint:
        state_dict = checkpoint['model']
    else:
        state_dict = checkpoint
    
    print(f"Checkpoint keys: {list(checkpoint.keys())}")
    print(f"Model state dict keys (first 10): {list(state_dict.keys())[:10]}")
    
    return state_dict

def create_dummy_model(state_dict, input_size):
    """Create a simple dummy model for export"""
    import torch.nn as nn
    
    class DummyModel(nn.Module):
        def __init__(self):
            super().__init__()
            # Create a simple model structure based on state dict
            self.features = nn.Sequential(
                nn.Conv2d(3, 64, 3, padding=1),
                nn.ReLU(),
                nn.AdaptiveAvgPool2d((1, 1)),
                nn.Flatten(),
                nn.Linear(64, 2)  # 2 classes based on config
            )
        
        def forward(self, x):
            return self.features(x)
    
    model = DummyModel()
    return model

def export_to_onnx(model, input_size, output_path):
    """Export PyTorch model to ONNX"""
    print(f"Exporting to ONNX: {output_path}")
    
    # Create dummy input
    dummy_input = torch.randn(1, 3, input_size[0], input_size[1])
    
    # Export to ONNX
    torch.onnx.export(
        model,
        dummy_input,
        output_path,
        export_params=True,
        opset_version=11,
        do_constant_folding=True,
        input_names=['input'],
        output_names=['output'],
        dynamic_axes={'input': {0: 'batch_size'}, 'output': {0: 'batch_size'}}
    )
    
    print(f"ONNX export completed: {output_path}")
    return output_path

def load_calibration_images(image_dir, input_size, max_images=100):
    """Load calibration images for quantization"""
    if not image_dir or not os.path.exists(image_dir):
        print("No calibration images provided, using random data")
        return np.random.rand(20, input_size[0], input_size[1], 3).astype(np.float32)
    
    import cv2
    from pathlib import Path
    
    print(f"Loading calibration images from: {image_dir}")
    
    image_files = []
    for ext in ['*.jpg', '*.jpeg', '*.png']:
        image_files.extend(Path(image_dir).glob(ext))
    
    if not image_files:
        print("No images found, using random data")
        return np.random.rand(20, input_size[0], input_size[1], 3).astype(np.float32)
    
    images = []
    for img_path in image_files[:max_images]:
        try:
            img = cv2.imread(str(img_path))
            if img is not None:
                img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
                img = cv2.resize(img, (input_size[1], input_size[0]))
                img = img.astype(np.float32) / 255.0
                images.append(img)
        except Exception as e:
            print(f"Error loading image {img_path}: {e}")
            continue
    
    if not images:
        print("Failed to load any images, using random data")
        return np.random.rand(20, input_size[0], input_size[1], 3).astype(np.float32)
    
    print(f"Loaded {len(images)} calibration images")
    return np.array(images)

def convert_onnx_to_tflite(onnx_path, output_path, calibration_images=None):
    """Convert ONNX model to TensorFlow Lite with int8 quantization"""
    try:
        import onnx
        import tensorflow as tf

        print(f"Converting ONNX to TensorFlow Lite: {onnx_path} -> {output_path}")

        # Try alternative conversion method using onnx2tf
        try:
            import onnx2tf

            # Convert ONNX to TensorFlow SavedModel using onnx2tf
            tf_model_path = onnx_path.replace('.onnx', '_tf_model')

            # Use onnx2tf for conversion
            onnx2tf.convert(
                input_onnx_file_path=onnx_path,
                output_folder_path=tf_model_path,
                copy_onnx_input_output_names_to_tflite=True,
                non_verbose=True
            )

        except ImportError:
            print("onnx2tf not available, trying onnx-tf...")
            # Fallback to onnx-tf
            from onnx_tf.backend import prepare

            # Load ONNX model
            onnx_model = onnx.load(onnx_path)

            # Convert to TensorFlow
            tf_rep = prepare(onnx_model)
            tf_model_path = onnx_path.replace('.onnx', '_tf_model')
            tf_rep.export_graph(tf_model_path)

        # Convert to TensorFlow Lite with quantization
        converter = tf.lite.TFLiteConverter.from_saved_model(tf_model_path)

        if calibration_images is not None:
            def representative_dataset():
                for img in calibration_images[:100]:  # Use up to 100 images
                    yield [np.expand_dims(img, axis=0)]

            converter.optimizations = [tf.lite.Optimize.DEFAULT]
            converter.target_spec.supported_ops = [tf.lite.OpsSet.TFLITE_BUILTINS_INT8]
            converter.inference_input_type = tf.int8
            converter.inference_output_type = tf.int8
            converter.representative_dataset = representative_dataset

        # Convert
        tflite_model = converter.convert()

        # Save
        with open(output_path, 'wb') as f:
            f.write(tflite_model)

        print(f"TensorFlow Lite export completed: {output_path}")
        return output_path

    except ImportError as e:
        print(f"Required packages not available: {e}")
        print("Please install: pip install onnx onnx2tf tensorflow")
        return None
    except Exception as e:
        print(f"Error during conversion: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    args = parse_args()
    
    # Setup paths
    checkpoint_path = args.checkpoint
    if args.output_dir:
        output_dir = Path(args.output_dir)
    else:
        output_dir = Path(checkpoint_path).parent
    
    output_dir.mkdir(exist_ok=True)
    
    model_name = Path(checkpoint_path).stem
    onnx_path = output_dir / f"{model_name}.onnx"
    tflite_path = output_dir / f"{model_name}_int8.tflite"
    
    try:
        # Load PyTorch model
        state_dict = load_pytorch_model(checkpoint_path)
        
        # Create a dummy model (since we can't load the full SSCMA model)
        print("Creating simplified model for export...")
        model = create_dummy_model(state_dict, args.img_size)
        model.eval()
        
        # Export to ONNX
        onnx_output = export_to_onnx(model, args.img_size, str(onnx_path))
        
        if onnx_output:
            # Load calibration images
            calibration_images = load_calibration_images(
                args.calibration_images, args.img_size
            )
            
            # Convert to TensorFlow Lite
            tflite_output = convert_onnx_to_tflite(
                str(onnx_path), str(tflite_path), calibration_images
            )
            
            if tflite_output:
                print(f"\n✅ Export completed successfully!")
                print(f"📁 ONNX model: {onnx_path}")
                print(f"📱 TensorFlow Lite (int8): {tflite_path}")
                
                # Print file sizes
                if onnx_path.exists():
                    onnx_size = onnx_path.stat().st_size / (1024 * 1024)
                    print(f"   ONNX size: {onnx_size:.2f} MB")
                
                if tflite_path.exists():
                    tflite_size = tflite_path.stat().st_size / (1024 * 1024)
                    print(f"   TFLite size: {tflite_size:.2f} MB")
            else:
                print("❌ TensorFlow Lite conversion failed")
        else:
            print("❌ ONNX export failed")
            
    except Exception as e:
        print(f"❌ Export failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
