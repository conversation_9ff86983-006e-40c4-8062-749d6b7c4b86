2025/06/23 12:25:50 - mmengine - INFO - 
------------------------------------------------------------
System environment:
    sys.platform: win32
    Python: 3.12.11 | packaged by Anaconda, Inc. | (main, Jun  5 2025, 12:58:53) [MSC v.1929 64 bit (AMD64)]
    CUDA available: True
    MUSA available: False
    numpy_random_seed: 1746506154
    GPU 0: NVIDIA GeForce RTX 5060 Ti
    CUDA_HOME: None
    MSVC: Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35209 for x64
    GCC: n/a
    PyTorch: 2.8.0.dev20250608+cu128
    PyTorch compiling details: PyTorch built with:
  - C++ Version: 201703
  - MSVC 193833144
  - Intel(R) oneAPI Math Kernel Library Version 2025.1-Product Build 20250306 for Intel(R) 64 architecture applications
  - Intel(R) MKL-DNN v3.7.1 (Git Hash 8d263e693366ef8db40acc569cc7d8edf644556d)
  - OpenMP 2019
  - LAPACK is enabled (usually provided by MKL)
  - CPU capability usage: AVX512
  - CUDA Runtime 12.8
  - NVCC architecture flags: -gencode;arch=compute_61,code=sm_61;-gencode;arch=compute_70,code=sm_70;-gencode;arch=compute_75,code=sm_75;-gencode;arch=compute_80,code=sm_80;-gencode;arch=compute_86,code=sm_86;-gencode;arch=compute_90,code=sm_90;-gencode;arch=compute_100,code=sm_100;-gencode;arch=compute_120,code=sm_120
  - CuDNN 90.7.1
  - Magma 2.5.4
  - Build settings: BLAS_INFO=mkl, BUILD_TYPE=Release, COMMIT_SHA=843156205ec57cf78d5cf10bf982656b3db51dfd, CUDA_VERSION=12.8, CUDNN_VERSION=9.7.1, CXX_COMPILER=C:/actions-runner/_work/pytorch/pytorch/pytorch/.ci/pytorch/windows/tmp_bin/sccache-cl.exe, CXX_FLAGS=/DWIN32 /D_WINDOWS /GR /EHsc /Zc:__cplusplus /bigobj /FS /utf-8 -DUSE_PTHREADPOOL -DNDEBUG -DUSE_KINETO -DLIBKINETO_NOROCTRACER -DLIBKINETO_NOXPUPTI=ON -DUSE_FBGEMM -DUSE_XNNPACK -DSYMBOLICATE_MOBILE_DEBUG_HANDLE /wd4624 /wd4068 /wd4067 /wd4267 /wd4661 /wd4717 /wd4244 /wd4804 /wd4273, LAPACK_INFO=mkl, PERF_WITH_AVX=1, PERF_WITH_AVX2=1, TORCH_VERSION=2.8.0, USE_CUDA=ON, USE_CUDNN=ON, USE_CUSPARSELT=OFF, USE_GFLAGS=OFF, USE_GLOG=OFF, USE_GLOO=ON, USE_MKL=ON, USE_MKLDNN=ON, USE_MPI=OFF, USE_NCCL=OFF, USE_NNPACK=OFF, USE_OPENMP=ON, USE_ROCM=OFF, USE_ROCM_KERNEL_ASSERT=OFF, USE_XCCL=OFF, USE_XPU=OFF, 

    TorchVision: 0.23.0.dev20250609+cu128
    OpenCV: 4.11.0
    MMEngine: 0.10.4

Runtime environment:
    cudnn_benchmark: True
    mp_cfg: {'mp_start_method': 'fork', 'opencv_num_threads': 0}
    dist_cfg: {'backend': 'nccl'}
    seed: 1746506154
    deterministic: False
    Distributed launcher: none
    Distributed training: False
    GPU number: 1
------------------------------------------------------------

2025/06/23 12:25:57 - mmengine - INFO - Distributed training is not used, all SyncBatchNorm (SyncBN) layers in the model will be automatically reverted to BatchNormXd layers if they are used.
2025/06/23 12:25:58 - mmengine - WARNING - The prefix is not set in metric class FomoMetric.
Name of parameter - Initialization information

backbone.conv1.conv.weight - torch.Size([16, 3, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.conv1.norm.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.conv1.norm.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer1.0.conv.0.conv.weight - torch.Size([16, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer1.0.conv.0.norm.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer1.0.conv.0.norm.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer1.0.conv.1.weight - torch.Size([8, 16, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer1.0.conv.2.weight - torch.Size([8]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer1.0.conv.2.bias - torch.Size([8]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.0.conv.0.conv.weight - torch.Size([48, 8, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer2.0.conv.0.norm.weight - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.0.conv.0.norm.bias - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.0.conv.1.conv.weight - torch.Size([48, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer2.0.conv.1.norm.weight - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.0.conv.1.norm.bias - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.0.conv.2.weight - torch.Size([8, 48, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer2.0.conv.3.weight - torch.Size([8]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.0.conv.3.bias - torch.Size([8]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.1.conv.0.conv.weight - torch.Size([48, 8, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer2.1.conv.0.norm.weight - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.1.conv.0.norm.bias - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.1.conv.1.conv.weight - torch.Size([48, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer2.1.conv.1.norm.weight - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.1.conv.1.norm.bias - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.1.conv.2.weight - torch.Size([8, 48, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer2.1.conv.3.weight - torch.Size([8]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.1.conv.3.bias - torch.Size([8]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.0.conv.0.conv.weight - torch.Size([48, 8, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer3.0.conv.0.norm.weight - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.0.conv.0.norm.bias - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.0.conv.1.conv.weight - torch.Size([48, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer3.0.conv.1.norm.weight - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.0.conv.1.norm.bias - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.0.conv.2.weight - torch.Size([16, 48, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer3.0.conv.3.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.0.conv.3.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.1.conv.0.conv.weight - torch.Size([96, 16, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer3.1.conv.0.norm.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.1.conv.0.norm.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.1.conv.1.conv.weight - torch.Size([96, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer3.1.conv.1.norm.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.1.conv.1.norm.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.1.conv.2.weight - torch.Size([16, 96, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer3.1.conv.3.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.1.conv.3.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.2.conv.0.conv.weight - torch.Size([96, 16, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer3.2.conv.0.norm.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.2.conv.0.norm.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.2.conv.1.conv.weight - torch.Size([96, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer3.2.conv.1.norm.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.2.conv.1.norm.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.2.conv.2.weight - torch.Size([16, 96, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer3.2.conv.3.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.2.conv.3.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.0.conv.0.conv.weight - torch.Size([96, 16, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.0.conv.0.norm.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.0.conv.0.norm.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.0.conv.1.conv.weight - torch.Size([96, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.0.conv.1.norm.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.0.conv.1.norm.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.0.conv.2.weight - torch.Size([24, 96, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.0.conv.3.weight - torch.Size([24]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.0.conv.3.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.1.conv.0.conv.weight - torch.Size([144, 24, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.1.conv.0.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.1.conv.0.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.1.conv.1.conv.weight - torch.Size([144, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.1.conv.1.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.1.conv.1.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.1.conv.2.weight - torch.Size([24, 144, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.1.conv.3.weight - torch.Size([24]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.1.conv.3.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.2.conv.0.conv.weight - torch.Size([144, 24, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.2.conv.0.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.2.conv.0.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.2.conv.1.conv.weight - torch.Size([144, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.2.conv.1.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.2.conv.1.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.2.conv.2.weight - torch.Size([24, 144, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.2.conv.3.weight - torch.Size([24]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.2.conv.3.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.3.conv.0.conv.weight - torch.Size([144, 24, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.3.conv.0.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.3.conv.0.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.3.conv.1.conv.weight - torch.Size([144, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.3.conv.1.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.3.conv.1.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.3.conv.2.weight - torch.Size([24, 144, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.3.conv.3.weight - torch.Size([24]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.3.conv.3.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.0.conv.0.conv.weight - torch.Size([144, 24, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer5.0.conv.0.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.0.conv.0.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.0.conv.1.conv.weight - torch.Size([144, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer5.0.conv.1.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.0.conv.1.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.0.conv.2.weight - torch.Size([32, 144, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer5.0.conv.3.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.0.conv.3.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.1.conv.0.conv.weight - torch.Size([192, 32, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer5.1.conv.0.norm.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.1.conv.0.norm.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.1.conv.1.conv.weight - torch.Size([192, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer5.1.conv.1.norm.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.1.conv.1.norm.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.1.conv.2.weight - torch.Size([32, 192, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer5.1.conv.3.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.1.conv.3.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.2.conv.0.conv.weight - torch.Size([192, 32, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer5.2.conv.0.norm.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.2.conv.0.norm.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.2.conv.1.conv.weight - torch.Size([192, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer5.2.conv.1.norm.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.2.conv.1.norm.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.2.conv.2.weight - torch.Size([32, 192, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer5.2.conv.3.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.2.conv.3.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.0.conv.0.conv.weight - torch.Size([192, 32, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer6.0.conv.0.norm.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.0.conv.0.norm.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.0.conv.1.conv.weight - torch.Size([192, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer6.0.conv.1.norm.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.0.conv.1.norm.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.0.conv.2.weight - torch.Size([56, 192, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer6.0.conv.3.weight - torch.Size([56]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.0.conv.3.bias - torch.Size([56]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.1.conv.0.conv.weight - torch.Size([336, 56, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer6.1.conv.0.norm.weight - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.1.conv.0.norm.bias - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.1.conv.1.conv.weight - torch.Size([336, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer6.1.conv.1.norm.weight - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.1.conv.1.norm.bias - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.1.conv.2.weight - torch.Size([56, 336, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer6.1.conv.3.weight - torch.Size([56]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.1.conv.3.bias - torch.Size([56]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.2.conv.0.conv.weight - torch.Size([336, 56, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer6.2.conv.0.norm.weight - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.2.conv.0.norm.bias - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.2.conv.1.conv.weight - torch.Size([336, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer6.2.conv.1.norm.weight - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.2.conv.1.norm.bias - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.2.conv.2.weight - torch.Size([56, 336, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer6.2.conv.3.weight - torch.Size([56]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.2.conv.3.bias - torch.Size([56]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer7.0.conv.0.conv.weight - torch.Size([336, 56, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer7.0.conv.0.norm.weight - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer7.0.conv.0.norm.bias - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer7.0.conv.1.conv.weight - torch.Size([336, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer7.0.conv.1.norm.weight - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer7.0.conv.1.norm.bias - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer7.0.conv.2.weight - torch.Size([112, 336, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer7.0.conv.3.weight - torch.Size([112]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer7.0.conv.3.bias - torch.Size([112]): 
The value is the same before and after calling `init_weights` of Fomo  

bbox_head.convs_bridge.0.0.weight - torch.Size([48, 16, 1, 1]): 
Initialized by user-defined `init_weights` in FomoHead  

bbox_head.convs_bridge.0.1.weight - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

bbox_head.convs_bridge.0.1.bias - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

bbox_head.convs_pred.0.weight - torch.Size([3, 48, 1, 1]): 
Initialized by user-defined `init_weights` in FomoHead  

bbox_head.convs_pred.0.bias - torch.Size([3]): 
Initialized by user-defined `init_weights` in FomoHead  
2025/06/23 12:25:58 - mmengine - WARNING - "FileClient" will be deprecated in future. Please use io functions in https://mmengine.readthedocs.io/en/latest/api/fileio.html#file-io
2025/06/23 12:25:58 - mmengine - WARNING - "HardDiskBackend" is the alias of "LocalBackend" and the former will be deprecated in future.
2025/06/23 12:25:58 - mmengine - INFO - Checkpoints will be saved to D:\OBJECT_DETECTION\sscma\ModelAssistant\work_dirs\fomo_mobnetv2_0.35_x8_coco.
2025/06/23 12:26:10 - mmengine - INFO - Epoch(train)   [1][ 10/123]  lr: 6.2207e-03  eta: 3:55:16  time: 1.1486  data_time: 1.0522  memory: 161  loss: 1.1164  fgnd: 0.8380  bgnd: 0.2008  P: 0.2000  R: 0.0488  F1: 0.0784
2025/06/23 12:26:12 - mmengine - INFO - Epoch(train)   [1][ 20/123]  lr: 1.3110e-02  eta: 2:20:30  time: 0.6865  data_time: 0.6263  memory: 147  loss: 1.0810  fgnd: 0.8868  bgnd: 0.0942  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:26:15 - mmengine - INFO - Epoch(train)   [1][ 30/123]  lr: 2.0000e-02  eta: 1:49:30  time: 0.5355  data_time: 0.4876  memory: 147  loss: 0.9984  fgnd: 0.6490  bgnd: 0.0325  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:26:15 - mmengine - INFO - Epoch(train)   [1][ 40/123]  lr: 2.0000e-02  eta: 1:24:13  time: 0.4122  data_time: 0.3730  memory: 147  loss: 0.8689  fgnd: 0.3318  bgnd: 0.0195  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:26:15 - mmengine - INFO - Epoch(train)   [1][ 50/123]  lr: 2.0000e-02  eta: 1:08:13  time: 0.3342  data_time: 0.3002  memory: 147  loss: 0.7640  fgnd: 0.3270  bgnd: 0.0161  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:26:15 - mmengine - INFO - Epoch(train)   [1][ 60/123]  lr: 2.0000e-02  eta: 0:57:35  time: 0.1090  data_time: 0.0920  memory: 147  loss: 0.5987  fgnd: 0.2464  bgnd: 0.0174  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:26:17 - mmengine - INFO - Epoch(train)   [1][ 70/123]  lr: 2.0000e-02  eta: 0:54:02  time: 0.0966  data_time: 0.0806  memory: 147  loss: 0.6673  fgnd: 1.8926  bgnd: 0.0452  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:26:18 - mmengine - INFO - Epoch(train)   [1][ 80/123]  lr: 2.0000e-02  eta: 0:50:54  time: 0.0787  data_time: 0.0633  memory: 147  loss: 0.6967  fgnd: 0.7353  bgnd: 0.0548  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:26:20 - mmengine - INFO - Epoch(train)   [1][ 90/123]  lr: 2.0000e-02  eta: 0:49:16  time: 0.1061  data_time: 0.0887  memory: 147  loss: 0.7545  fgnd: 0.6394  bgnd: 0.0463  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:26:21 - mmengine - INFO - Epoch(train)   [1][100/123]  lr: 2.0000e-02  eta: 0:46:16  time: 0.1210  data_time: 0.1038  memory: 147  loss: 0.8410  fgnd: 0.6374  bgnd: 0.0386  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:26:21 - mmengine - INFO - Epoch(train)   [1][110/123]  lr: 2.0000e-02  eta: 0:42:28  time: 0.1212  data_time: 0.1039  memory: 147  loss: 0.8934  fgnd: 0.4522  bgnd: 0.0395  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:26:22 - mmengine - INFO - Epoch(train)   [1][120/123]  lr: 2.0000e-02  eta: 0:39:17  time: 0.0933  data_time: 0.0774  memory: 147  loss: 0.7058  fgnd: 0.3892  bgnd: 0.0324  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:26:22 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_122545
2025/06/23 12:26:22 - mmengine - INFO - Saving checkpoint at 1 epochs
2025/06/23 12:26:31 - mmengine - INFO - Epoch(val)   [1][10/17]    eta: 0:00:06  time: 0.9499  data_time: 0.9301  memory: 147  
2025/06/23 12:26:32 - mmengine - INFO - Epoch(val) [1][17/17]    P: 0.0000  R: 0.0000  F1: 0.0000  data_time: 0.5581  time: 0.5729
2025/06/23 12:26:34 - mmengine - INFO - Epoch(train)   [2][ 10/123]  lr: 2.0000e-02  eta: 0:38:28  time: 0.0930  data_time: 0.0768  memory: 150  loss: 0.6439  fgnd: 0.9185  bgnd: 0.0360  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:26:36 - mmengine - INFO - Epoch(train)   [2][ 20/123]  lr: 2.0000e-02  eta: 0:38:39  time: 0.0948  data_time: 0.0785  memory: 149  loss: 0.6594  fgnd: 0.7368  bgnd: 0.0372  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:26:38 - mmengine - INFO - Epoch(train)   [2][ 30/123]  lr: 2.0000e-02  eta: 0:38:54  time: 0.1310  data_time: 0.1124  memory: 149  loss: 0.6603  fgnd: 0.5197  bgnd: 0.0272  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:26:38 - mmengine - INFO - Epoch(train)   [2][ 40/123]  lr: 2.0000e-02  eta: 0:37:00  time: 0.1349  data_time: 0.1160  memory: 149  loss: 0.6623  fgnd: 0.4971  bgnd: 0.0244  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:26:38 - mmengine - INFO - Epoch(train)   [2][ 50/123]  lr: 2.0000e-02  eta: 0:35:06  time: 0.1341  data_time: 0.1164  memory: 149  loss: 0.6710  fgnd: 0.4055  bgnd: 0.0224  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:26:39 - mmengine - INFO - Epoch(train)   [2][ 60/123]  lr: 2.0000e-02  eta: 0:33:25  time: 0.1012  data_time: 0.0844  memory: 149  loss: 0.5862  fgnd: 0.3210  bgnd: 0.0196  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:26:40 - mmengine - INFO - Epoch(train)   [2][ 70/123]  lr: 2.0000e-02  eta: 0:33:21  time: 0.0925  data_time: 0.0765  memory: 149  loss: 0.5312  fgnd: 0.8270  bgnd: 0.0314  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:26:42 - mmengine - INFO - Epoch(train)   [2][ 80/123]  lr: 2.0000e-02  eta: 0:33:07  time: 0.0791  data_time: 0.0637  memory: 149  loss: 0.5763  fgnd: 0.7899  bgnd: 0.0344  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:26:44 - mmengine - INFO - Epoch(train)   [2][ 90/123]  lr: 2.0000e-02  eta: 0:33:19  time: 0.1084  data_time: 0.0910  memory: 149  loss: 0.6001  fgnd: 0.5040  bgnd: 0.0256  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:26:45 - mmengine - INFO - Epoch(train)   [2][100/123]  lr: 2.0000e-02  eta: 0:32:42  time: 0.1235  data_time: 0.1061  memory: 149  loss: 0.5998  fgnd: 0.4523  bgnd: 0.0202  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:26:45 - mmengine - INFO - Epoch(train)   [2][110/123]  lr: 2.0000e-02  eta: 0:31:30  time: 0.1241  data_time: 0.1069  memory: 149  loss: 0.6124  fgnd: 0.4211  bgnd: 0.0196  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:26:45 - mmengine - INFO - Epoch(train)   [2][120/123]  lr: 2.0000e-02  eta: 0:30:22  time: 0.0964  data_time: 0.0802  memory: 149  loss: 0.5654  fgnd: 0.3305  bgnd: 0.0184  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:26:45 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_122545
2025/06/23 12:26:45 - mmengine - INFO - Saving checkpoint at 2 epochs
2025/06/23 12:26:46 - mmengine - INFO - Epoch(val)   [2][10/17]    eta: 0:00:00  time: 0.3808  data_time: 0.3680  memory: 149  
2025/06/23 12:26:46 - mmengine - INFO - Epoch(val) [2][17/17]    P: 0.0000  R: 0.0000  F1: 0.0000  data_time: 0.0348  time: 0.0425
2025/06/23 12:26:48 - mmengine - INFO - Epoch(train)   [3][ 10/123]  lr: 2.0000e-02  eta: 0:30:06  time: 0.0890  data_time: 0.0737  memory: 149  loss: 0.4941  fgnd: 0.8505  bgnd: 0.0313  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:26:49 - mmengine - INFO - Epoch(train)   [3][ 20/123]  lr: 2.0000e-02  eta: 0:30:17  time: 0.0839  data_time: 0.0682  memory: 149  loss: 0.5337  fgnd: 0.7042  bgnd: 0.0316  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:26:51 - mmengine - INFO - Epoch(train)   [3][ 30/123]  lr: 2.0000e-02  eta: 0:30:36  time: 0.1167  data_time: 0.0998  memory: 149  loss: 0.5437  fgnd: 0.4030  bgnd: 0.0192  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:26:52 - mmengine - INFO - Epoch(train)   [3][ 40/123]  lr: 2.0000e-02  eta: 0:29:48  time: 0.1202  data_time: 0.1028  memory: 149  loss: 0.5387  fgnd: 0.3746  bgnd: 0.0170  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:26:52 - mmengine - INFO - Epoch(train)   [3][ 50/123]  lr: 2.0000e-02  eta: 0:28:56  time: 0.1210  data_time: 0.1034  memory: 149  loss: 0.5405  fgnd: 0.3111  bgnd: 0.0164  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:26:52 - mmengine - INFO - Epoch(train)   [3][ 60/123]  lr: 2.0000e-02  eta: 0:28:08  time: 0.0935  data_time: 0.0769  memory: 149  loss: 0.4772  fgnd: 0.2859  bgnd: 0.0150  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:26:54 - mmengine - INFO - Epoch(train)   [3][ 70/123]  lr: 2.0000e-02  eta: 0:28:12  time: 0.0889  data_time: 0.0735  memory: 149  loss: 0.4395  fgnd: 0.9395  bgnd: 0.0339  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:26:55 - mmengine - INFO - Epoch(train)   [3][ 80/123]  lr: 2.0000e-02  eta: 0:28:13  time: 0.0793  data_time: 0.0635  memory: 149  loss: 0.4816  fgnd: 0.6217  bgnd: 0.0294  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:26:57 - mmengine - INFO - Epoch(train)   [3][ 90/123]  lr: 2.0000e-02  eta: 0:28:26  time: 0.1068  data_time: 0.0894  memory: 149  loss: 0.5074  fgnd: 0.3590  bgnd: 0.0167  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:26:58 - mmengine - INFO - Epoch(train)   [3][100/123]  lr: 2.0000e-02  eta: 0:28:09  time: 0.1215  data_time: 0.1040  memory: 149  loss: 0.5104  fgnd: 0.3663  bgnd: 0.0157  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:26:58 - mmengine - INFO - Epoch(train)   [3][110/123]  lr: 2.0000e-02  eta: 0:27:28  time: 0.1214  data_time: 0.1042  memory: 149  loss: 0.5187  fgnd: 0.3184  bgnd: 0.0154  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:26:59 - mmengine - INFO - Epoch(train)   [3][120/123]  lr: 2.0000e-02  eta: 0:26:49  time: 0.0949  data_time: 0.0786  memory: 149  loss: 0.4680  fgnd: 0.2738  bgnd: 0.0148  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:26:59 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_122545
2025/06/23 12:26:59 - mmengine - INFO - Saving checkpoint at 3 epochs
2025/06/23 12:27:00 - mmengine - INFO - Epoch(val)   [3][10/17]    eta: 0:00:00  time: 0.2508  data_time: 0.2401  memory: 149  
2025/06/23 12:27:00 - mmengine - INFO - Epoch(val) [3][17/17]    P: 0.0000  R: 0.0000  F1: 0.0000  data_time: 0.0345  time: 0.0420
2025/06/23 12:27:01 - mmengine - INFO - Epoch(train)   [4][ 10/123]  lr: 2.0000e-02  eta: 0:26:45  time: 0.0871  data_time: 0.0716  memory: 149  loss: 0.4212  fgnd: 0.6531  bgnd: 0.0268  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:27:03 - mmengine - INFO - Epoch(train)   [4][ 20/123]  lr: 2.0000e-02  eta: 0:26:56  time: 0.0829  data_time: 0.0671  memory: 149  loss: 0.4774  fgnd: 0.6039  bgnd: 0.0288  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:27:05 - mmengine - INFO - Epoch(train)   [4][ 30/123]  lr: 2.0000e-02  eta: 0:27:10  time: 0.1137  data_time: 0.0960  memory: 149  loss: 0.5001  fgnd: 0.4139  bgnd: 0.0198  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:27:05 - mmengine - INFO - Epoch(train)   [4][ 40/123]  lr: 2.0000e-02  eta: 0:26:41  time: 0.1171  data_time: 0.0994  memory: 149  loss: 0.5066  fgnd: 0.3743  bgnd: 0.0158  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:27:06 - mmengine - INFO - Epoch(train)   [4][ 50/123]  lr: 2.0000e-02  eta: 0:26:08  time: 0.1176  data_time: 0.0998  memory: 149  loss: 0.5162  fgnd: 0.3180  bgnd: 0.0152  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:27:06 - mmengine - INFO - Epoch(train)   [4][ 60/123]  lr: 2.0000e-02  eta: 0:25:36  time: 0.0894  data_time: 0.0728  memory: 149  loss: 0.4576  fgnd: 0.2968  bgnd: 0.0146  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:27:07 - mmengine - INFO - Epoch(train)   [4][ 70/123]  lr: 2.0000e-02  eta: 0:25:40  time: 0.0842  data_time: 0.0685  memory: 149  loss: 0.4146  fgnd: 0.6593  bgnd: 0.0272  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:27:09 - mmengine - INFO - Epoch(train)   [4][ 80/123]  lr: 2.0000e-02  eta: 0:25:43  time: 0.0764  data_time: 0.0615  memory: 149  loss: 0.4551  fgnd: 0.6092  bgnd: 0.0301  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:27:11 - mmengine - INFO - Epoch(train)   [4][ 90/123]  lr: 2.0000e-02  eta: 0:25:56  time: 0.1054  data_time: 0.0894  memory: 149  loss: 0.4894  fgnd: 0.4329  bgnd: 0.0193  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:27:12 - mmengine - INFO - Epoch(train)   [4][100/123]  lr: 2.0000e-02  eta: 0:25:47  time: 0.1206  data_time: 0.1037  memory: 149  loss: 0.4918  fgnd: 0.3639  bgnd: 0.0151  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:27:12 - mmengine - INFO - Epoch(train)   [4][110/123]  lr: 2.0000e-02  eta: 0:25:19  time: 0.1208  data_time: 0.1039  memory: 149  loss: 0.4996  fgnd: 0.3125  bgnd: 0.0143  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:27:12 - mmengine - INFO - Epoch(train)   [4][120/123]  lr: 2.0000e-02  eta: 0:24:52  time: 0.0956  data_time: 0.0796  memory: 149  loss: 0.4596  fgnd: 0.2751  bgnd: 0.0136  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:27:12 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_122545
2025/06/23 12:27:12 - mmengine - INFO - Saving checkpoint at 4 epochs
2025/06/23 12:27:13 - mmengine - INFO - Epoch(val)   [4][10/17]    eta: 0:00:00  time: 0.0436  data_time: 0.0349  memory: 149  
