2025/06/23 12:28:45 - mmengine - INFO - 
------------------------------------------------------------
System environment:
    sys.platform: win32
    Python: 3.12.11 | packaged by Anaconda, Inc. | (main, Jun  5 2025, 12:58:53) [MSC v.1929 64 bit (AMD64)]
    CUDA available: True
    MUSA available: False
    numpy_random_seed: 612450448
    GPU 0: NVIDIA GeForce RTX 5060 Ti
    CUDA_HOME: None
    MSVC: Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35209 for x64
    GCC: n/a
    PyTorch: 2.8.0.dev20250608+cu128
    PyTorch compiling details: PyTorch built with:
  - C++ Version: 201703
  - MSVC 193833144
  - Intel(R) oneAPI Math Kernel Library Version 2025.1-Product Build 20250306 for Intel(R) 64 architecture applications
  - Intel(R) MKL-DNN v3.7.1 (Git Hash 8d263e693366ef8db40acc569cc7d8edf644556d)
  - OpenMP 2019
  - LAPACK is enabled (usually provided by MKL)
  - CPU capability usage: AVX512
  - CUDA Runtime 12.8
  - NVCC architecture flags: -gencode;arch=compute_61,code=sm_61;-gencode;arch=compute_70,code=sm_70;-gencode;arch=compute_75,code=sm_75;-gencode;arch=compute_80,code=sm_80;-gencode;arch=compute_86,code=sm_86;-gencode;arch=compute_90,code=sm_90;-gencode;arch=compute_100,code=sm_100;-gencode;arch=compute_120,code=sm_120
  - CuDNN 90.7.1
  - Magma 2.5.4
  - Build settings: BLAS_INFO=mkl, BUILD_TYPE=Release, COMMIT_SHA=843156205ec57cf78d5cf10bf982656b3db51dfd, CUDA_VERSION=12.8, CUDNN_VERSION=9.7.1, CXX_COMPILER=C:/actions-runner/_work/pytorch/pytorch/pytorch/.ci/pytorch/windows/tmp_bin/sccache-cl.exe, CXX_FLAGS=/DWIN32 /D_WINDOWS /GR /EHsc /Zc:__cplusplus /bigobj /FS /utf-8 -DUSE_PTHREADPOOL -DNDEBUG -DUSE_KINETO -DLIBKINETO_NOROCTRACER -DLIBKINETO_NOXPUPTI=ON -DUSE_FBGEMM -DUSE_XNNPACK -DSYMBOLICATE_MOBILE_DEBUG_HANDLE /wd4624 /wd4068 /wd4067 /wd4267 /wd4661 /wd4717 /wd4244 /wd4804 /wd4273, LAPACK_INFO=mkl, PERF_WITH_AVX=1, PERF_WITH_AVX2=1, TORCH_VERSION=2.8.0, USE_CUDA=ON, USE_CUDNN=ON, USE_CUSPARSELT=OFF, USE_GFLAGS=OFF, USE_GLOG=OFF, USE_GLOO=ON, USE_MKL=ON, USE_MKLDNN=ON, USE_MPI=OFF, USE_NCCL=OFF, USE_NNPACK=OFF, USE_OPENMP=ON, USE_ROCM=OFF, USE_ROCM_KERNEL_ASSERT=OFF, USE_XCCL=OFF, USE_XPU=OFF, 

    TorchVision: 0.23.0.dev20250609+cu128
    OpenCV: 4.11.0
    MMEngine: 0.10.4

Runtime environment:
    cudnn_benchmark: True
    mp_cfg: {'mp_start_method': 'fork', 'opencv_num_threads': 0}
    dist_cfg: {'backend': 'nccl'}
    seed: 612450448
    deterministic: False
    Distributed launcher: none
    Distributed training: False
    GPU number: 1
------------------------------------------------------------

2025/06/23 12:28:47 - mmengine - INFO - Distributed training is not used, all SyncBatchNorm (SyncBN) layers in the model will be automatically reverted to BatchNormXd layers if they are used.
2025/06/23 12:28:48 - mmengine - WARNING - The prefix is not set in metric class FomoMetric.
Name of parameter - Initialization information

backbone.conv1.conv.weight - torch.Size([16, 3, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.conv1.norm.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.conv1.norm.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer1.0.conv.0.conv.weight - torch.Size([16, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer1.0.conv.0.norm.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer1.0.conv.0.norm.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer1.0.conv.1.weight - torch.Size([8, 16, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer1.0.conv.2.weight - torch.Size([8]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer1.0.conv.2.bias - torch.Size([8]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.0.conv.0.conv.weight - torch.Size([48, 8, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer2.0.conv.0.norm.weight - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.0.conv.0.norm.bias - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.0.conv.1.conv.weight - torch.Size([48, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer2.0.conv.1.norm.weight - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.0.conv.1.norm.bias - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.0.conv.2.weight - torch.Size([8, 48, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer2.0.conv.3.weight - torch.Size([8]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.0.conv.3.bias - torch.Size([8]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.1.conv.0.conv.weight - torch.Size([48, 8, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer2.1.conv.0.norm.weight - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.1.conv.0.norm.bias - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.1.conv.1.conv.weight - torch.Size([48, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer2.1.conv.1.norm.weight - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.1.conv.1.norm.bias - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.1.conv.2.weight - torch.Size([8, 48, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer2.1.conv.3.weight - torch.Size([8]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.1.conv.3.bias - torch.Size([8]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.0.conv.0.conv.weight - torch.Size([48, 8, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer3.0.conv.0.norm.weight - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.0.conv.0.norm.bias - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.0.conv.1.conv.weight - torch.Size([48, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer3.0.conv.1.norm.weight - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.0.conv.1.norm.bias - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.0.conv.2.weight - torch.Size([16, 48, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer3.0.conv.3.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.0.conv.3.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.1.conv.0.conv.weight - torch.Size([96, 16, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer3.1.conv.0.norm.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.1.conv.0.norm.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.1.conv.1.conv.weight - torch.Size([96, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer3.1.conv.1.norm.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.1.conv.1.norm.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.1.conv.2.weight - torch.Size([16, 96, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer3.1.conv.3.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.1.conv.3.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.2.conv.0.conv.weight - torch.Size([96, 16, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer3.2.conv.0.norm.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.2.conv.0.norm.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.2.conv.1.conv.weight - torch.Size([96, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer3.2.conv.1.norm.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.2.conv.1.norm.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.2.conv.2.weight - torch.Size([16, 96, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer3.2.conv.3.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.2.conv.3.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.0.conv.0.conv.weight - torch.Size([96, 16, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.0.conv.0.norm.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.0.conv.0.norm.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.0.conv.1.conv.weight - torch.Size([96, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.0.conv.1.norm.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.0.conv.1.norm.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.0.conv.2.weight - torch.Size([24, 96, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.0.conv.3.weight - torch.Size([24]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.0.conv.3.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.1.conv.0.conv.weight - torch.Size([144, 24, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.1.conv.0.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.1.conv.0.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.1.conv.1.conv.weight - torch.Size([144, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.1.conv.1.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.1.conv.1.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.1.conv.2.weight - torch.Size([24, 144, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.1.conv.3.weight - torch.Size([24]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.1.conv.3.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.2.conv.0.conv.weight - torch.Size([144, 24, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.2.conv.0.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.2.conv.0.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.2.conv.1.conv.weight - torch.Size([144, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.2.conv.1.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.2.conv.1.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.2.conv.2.weight - torch.Size([24, 144, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.2.conv.3.weight - torch.Size([24]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.2.conv.3.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.3.conv.0.conv.weight - torch.Size([144, 24, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.3.conv.0.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.3.conv.0.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.3.conv.1.conv.weight - torch.Size([144, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.3.conv.1.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.3.conv.1.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.3.conv.2.weight - torch.Size([24, 144, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.3.conv.3.weight - torch.Size([24]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.3.conv.3.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.0.conv.0.conv.weight - torch.Size([144, 24, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer5.0.conv.0.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.0.conv.0.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.0.conv.1.conv.weight - torch.Size([144, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer5.0.conv.1.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.0.conv.1.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.0.conv.2.weight - torch.Size([32, 144, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer5.0.conv.3.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.0.conv.3.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.1.conv.0.conv.weight - torch.Size([192, 32, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer5.1.conv.0.norm.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.1.conv.0.norm.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.1.conv.1.conv.weight - torch.Size([192, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer5.1.conv.1.norm.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.1.conv.1.norm.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.1.conv.2.weight - torch.Size([32, 192, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer5.1.conv.3.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.1.conv.3.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.2.conv.0.conv.weight - torch.Size([192, 32, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer5.2.conv.0.norm.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.2.conv.0.norm.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.2.conv.1.conv.weight - torch.Size([192, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer5.2.conv.1.norm.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.2.conv.1.norm.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.2.conv.2.weight - torch.Size([32, 192, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer5.2.conv.3.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.2.conv.3.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.0.conv.0.conv.weight - torch.Size([192, 32, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer6.0.conv.0.norm.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.0.conv.0.norm.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.0.conv.1.conv.weight - torch.Size([192, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer6.0.conv.1.norm.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.0.conv.1.norm.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.0.conv.2.weight - torch.Size([56, 192, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer6.0.conv.3.weight - torch.Size([56]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.0.conv.3.bias - torch.Size([56]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.1.conv.0.conv.weight - torch.Size([336, 56, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer6.1.conv.0.norm.weight - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.1.conv.0.norm.bias - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.1.conv.1.conv.weight - torch.Size([336, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer6.1.conv.1.norm.weight - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.1.conv.1.norm.bias - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.1.conv.2.weight - torch.Size([56, 336, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer6.1.conv.3.weight - torch.Size([56]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.1.conv.3.bias - torch.Size([56]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.2.conv.0.conv.weight - torch.Size([336, 56, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer6.2.conv.0.norm.weight - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.2.conv.0.norm.bias - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.2.conv.1.conv.weight - torch.Size([336, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer6.2.conv.1.norm.weight - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.2.conv.1.norm.bias - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.2.conv.2.weight - torch.Size([56, 336, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer6.2.conv.3.weight - torch.Size([56]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.2.conv.3.bias - torch.Size([56]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer7.0.conv.0.conv.weight - torch.Size([336, 56, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer7.0.conv.0.norm.weight - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer7.0.conv.0.norm.bias - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer7.0.conv.1.conv.weight - torch.Size([336, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer7.0.conv.1.norm.weight - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer7.0.conv.1.norm.bias - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer7.0.conv.2.weight - torch.Size([112, 336, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer7.0.conv.3.weight - torch.Size([112]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer7.0.conv.3.bias - torch.Size([112]): 
The value is the same before and after calling `init_weights` of Fomo  

bbox_head.convs_bridge.0.0.weight - torch.Size([48, 16, 1, 1]): 
Initialized by user-defined `init_weights` in FomoHead  

bbox_head.convs_bridge.0.1.weight - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

bbox_head.convs_bridge.0.1.bias - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

bbox_head.convs_pred.0.weight - torch.Size([3, 48, 1, 1]): 
Initialized by user-defined `init_weights` in FomoHead  

bbox_head.convs_pred.0.bias - torch.Size([3]): 
Initialized by user-defined `init_weights` in FomoHead  
2025/06/23 12:28:48 - mmengine - WARNING - "FileClient" will be deprecated in future. Please use io functions in https://mmengine.readthedocs.io/en/latest/api/fileio.html#file-io
2025/06/23 12:28:48 - mmengine - WARNING - "HardDiskBackend" is the alias of "LocalBackend" and the former will be deprecated in future.
2025/06/23 12:28:48 - mmengine - INFO - Checkpoints will be saved to D:\OBJECT_DETECTION\sscma\ModelAssistant\work_dirs\fomo_mobnetv2_0.35_x8_coco.
2025/06/23 12:28:59 - mmengine - INFO - Epoch(train)   [1][ 10/123]  lr: 6.2207e-03  eta: 3:37:09  time: 1.0602  data_time: 0.9859  memory: 161  loss: 1.1127  fgnd: 0.8985  bgnd: 0.2042  P: 0.0859  R: 0.1359  F1: 0.1053
2025/06/23 12:29:01 - mmengine - INFO - Epoch(train)   [1][ 20/123]  lr: 1.3110e-02  eta: 2:07:28  time: 0.6228  data_time: 0.5698  memory: 148  loss: 1.0889  fgnd: 0.9263  bgnd: 0.1012  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:29:02 - mmengine - INFO - Epoch(train)   [1][ 30/123]  lr: 2.0000e-02  eta: 1:37:08  time: 0.4750  data_time: 0.4325  memory: 148  loss: 1.0110  fgnd: 0.6433  bgnd: 0.0341  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:29:03 - mmengine - INFO - Epoch(train)   [1][ 40/123]  lr: 2.0000e-02  eta: 1:14:49  time: 0.3662  data_time: 0.3310  memory: 148  loss: 0.8765  fgnd: 0.3386  bgnd: 0.0172  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:29:03 - mmengine - INFO - Epoch(train)   [1][ 50/123]  lr: 2.0000e-02  eta: 1:00:47  time: 0.2977  data_time: 0.2670  memory: 148  loss: 0.7666  fgnd: 0.2790  bgnd: 0.0164  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:29:03 - mmengine - INFO - Epoch(train)   [1][ 60/123]  lr: 2.0000e-02  eta: 0:51:23  time: 0.0903  data_time: 0.0718  memory: 148  loss: 0.6000  fgnd: 0.2358  bgnd: 0.0189  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:29:05 - mmengine - INFO - Epoch(train)   [1][ 70/123]  lr: 2.0000e-02  eta: 0:48:25  time: 0.0835  data_time: 0.0679  memory: 148  loss: 0.6527  fgnd: 1.6322  bgnd: 0.0405  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:29:06 - mmengine - INFO - Epoch(train)   [1][ 80/123]  lr: 2.0000e-02  eta: 0:45:58  time: 0.0762  data_time: 0.0608  memory: 148  loss: 0.6858  fgnd: 0.7411  bgnd: 0.0608  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:29:08 - mmengine - INFO - Epoch(train)   [1][ 90/123]  lr: 2.0000e-02  eta: 0:44:57  time: 0.1047  data_time: 0.0874  memory: 148  loss: 0.7404  fgnd: 0.6158  bgnd: 0.0508  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:29:09 - mmengine - INFO - Epoch(train)   [1][100/123]  lr: 2.0000e-02  eta: 0:42:24  time: 0.1194  data_time: 0.1021  memory: 148  loss: 0.8038  fgnd: 0.6437  bgnd: 0.0436  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:29:09 - mmengine - INFO - Epoch(train)   [1][110/123]  lr: 2.0000e-02  eta: 0:38:59  time: 0.1199  data_time: 0.1025  memory: 148  loss: 0.8780  fgnd: 0.5040  bgnd: 0.0407  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:29:10 - mmengine - INFO - Epoch(train)   [1][120/123]  lr: 2.0000e-02  eta: 0:36:05  time: 0.0941  data_time: 0.0777  memory: 148  loss: 0.7122  fgnd: 0.4579  bgnd: 0.0341  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:29:10 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_122843
2025/06/23 12:29:10 - mmengine - INFO - Saving checkpoint at 1 epochs
2025/06/23 12:29:19 - mmengine - INFO - Epoch(val)   [1][10/17]    eta: 0:00:06  time: 0.9344  data_time: 0.9032  memory: 148  
2025/06/23 12:29:19 - mmengine - INFO - Epoch(val) [1][17/17]    P: 0.0000  R: 0.0000  F1: 0.0000  data_time: 0.5414  time: 0.5627
2025/06/23 12:29:21 - mmengine - INFO - Epoch(train)   [2][ 10/123]  lr: 2.0000e-02  eta: 0:35:12  time: 0.0892  data_time: 0.0725  memory: 150  loss: 0.6308  fgnd: 0.7714  bgnd: 0.0386  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:29:23 - mmengine - INFO - Epoch(train)   [2][ 20/123]  lr: 2.0000e-02  eta: 0:35:15  time: 0.0852  data_time: 0.0688  memory: 149  loss: 0.6509  fgnd: 0.7107  bgnd: 0.0387  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:29:25 - mmengine - INFO - Epoch(train)   [2][ 30/123]  lr: 2.0000e-02  eta: 0:35:36  time: 0.1192  data_time: 0.1015  memory: 149  loss: 0.6475  fgnd: 0.5398  bgnd: 0.0301  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:29:25 - mmengine - INFO - Epoch(train)   [2][ 40/123]  lr: 2.0000e-02  eta: 0:33:51  time: 0.1218  data_time: 0.1037  memory: 149  loss: 0.6175  fgnd: 0.3926  bgnd: 0.0252  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:29:25 - mmengine - INFO - Epoch(train)   [2][ 50/123]  lr: 2.0000e-02  eta: 0:32:08  time: 0.1212  data_time: 0.1042  memory: 149  loss: 0.6000  fgnd: 0.3532  bgnd: 0.0221  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:29:26 - mmengine - INFO - Epoch(train)   [2][ 60/123]  lr: 2.0000e-02  eta: 0:30:37  time: 0.0932  data_time: 0.0771  memory: 149  loss: 0.5218  fgnd: 0.2831  bgnd: 0.0198  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:29:27 - mmengine - INFO - Epoch(train)   [2][ 70/123]  lr: 2.0000e-02  eta: 0:30:37  time: 0.0882  data_time: 0.0726  memory: 149  loss: 0.4993  fgnd: 0.9731  bgnd: 0.0361  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:29:29 - mmengine - INFO - Epoch(train)   [2][ 80/123]  lr: 2.0000e-02  eta: 0:30:37  time: 0.0783  data_time: 0.0622  memory: 149  loss: 0.5388  fgnd: 0.6226  bgnd: 0.0309  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:29:31 - mmengine - INFO - Epoch(train)   [2][ 90/123]  lr: 2.0000e-02  eta: 0:30:52  time: 0.1074  data_time: 0.0899  memory: 149  loss: 0.5724  fgnd: 0.4602  bgnd: 0.0248  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:29:32 - mmengine - INFO - Epoch(train)   [2][100/123]  lr: 2.0000e-02  eta: 0:30:21  time: 0.1225  data_time: 0.1048  memory: 149  loss: 0.5887  fgnd: 0.4112  bgnd: 0.0221  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:29:32 - mmengine - INFO - Epoch(train)   [2][110/123]  lr: 2.0000e-02  eta: 0:29:14  time: 0.1225  data_time: 0.1050  memory: 149  loss: 0.6026  fgnd: 0.3754  bgnd: 0.0197  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:29:32 - mmengine - INFO - Epoch(train)   [2][120/123]  lr: 2.0000e-02  eta: 0:28:12  time: 0.0964  data_time: 0.0797  memory: 149  loss: 0.5444  fgnd: 0.3271  bgnd: 0.0182  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:29:32 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_122843
2025/06/23 12:29:32 - mmengine - INFO - Saving checkpoint at 2 epochs
2025/06/23 12:29:33 - mmengine - INFO - Epoch(val)   [2][10/17]    eta: 0:00:00  time: 0.3751  data_time: 0.3586  memory: 149  
2025/06/23 12:29:33 - mmengine - INFO - Epoch(val) [2][17/17]    P: 0.0000  R: 0.0000  F1: 0.0000  data_time: 0.0365  time: 0.0437
2025/06/23 12:29:35 - mmengine - INFO - Epoch(train)   [3][ 10/123]  lr: 2.0000e-02  eta: 0:28:04  time: 0.0873  data_time: 0.0722  memory: 149  loss: 0.4748  fgnd: 0.7018  bgnd: 0.0281  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:29:36 - mmengine - INFO - Epoch(train)   [3][ 20/123]  lr: 2.0000e-02  eta: 0:28:19  time: 0.0830  data_time: 0.0669  memory: 149  loss: 0.5185  fgnd: 0.6964  bgnd: 0.0320  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:29:38 - mmengine - INFO - Epoch(train)   [3][ 30/123]  lr: 2.0000e-02  eta: 0:28:38  time: 0.1142  data_time: 0.0968  memory: 149  loss: 0.5369  fgnd: 0.3798  bgnd: 0.0201  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:29:39 - mmengine - INFO - Epoch(train)   [3][ 40/123]  lr: 2.0000e-02  eta: 0:27:52  time: 0.1173  data_time: 0.0991  memory: 149  loss: 0.5345  fgnd: 0.3579  bgnd: 0.0183  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:29:39 - mmengine - INFO - Epoch(train)   [3][ 50/123]  lr: 2.0000e-02  eta: 0:27:04  time: 0.1178  data_time: 0.0996  memory: 149  loss: 0.5368  fgnd: 0.3217  bgnd: 0.0174  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:29:39 - mmengine - INFO - Epoch(train)   [3][ 60/123]  lr: 2.0000e-02  eta: 0:26:19  time: 0.0899  data_time: 0.0726  memory: 149  loss: 0.4757  fgnd: 0.2860  bgnd: 0.0167  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:29:41 - mmengine - INFO - Epoch(train)   [3][ 70/123]  lr: 2.0000e-02  eta: 0:26:27  time: 0.0858  data_time: 0.0696  memory: 149  loss: 0.4384  fgnd: 1.0049  bgnd: 0.0379  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:29:42 - mmengine - INFO - Epoch(train)   [3][ 80/123]  lr: 2.0000e-02  eta: 0:26:30  time: 0.0770  data_time: 0.0605  memory: 149  loss: 0.4870  fgnd: 0.8131  bgnd: 0.0362  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:29:44 - mmengine - INFO - Epoch(train)   [3][ 90/123]  lr: 2.0000e-02  eta: 0:26:47  time: 0.1064  data_time: 0.0887  memory: 149  loss: 0.5144  fgnd: 0.3847  bgnd: 0.0202  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:29:45 - mmengine - INFO - Epoch(train)   [3][100/123]  lr: 2.0000e-02  eta: 0:26:32  time: 0.1210  data_time: 0.1031  memory: 149  loss: 0.5198  fgnd: 0.3789  bgnd: 0.0167  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:29:45 - mmengine - INFO - Epoch(train)   [3][110/123]  lr: 2.0000e-02  eta: 0:25:54  time: 0.1211  data_time: 0.1034  memory: 149  loss: 0.5371  fgnd: 0.3563  bgnd: 0.0171  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:29:45 - mmengine - INFO - Epoch(train)   [3][120/123]  lr: 2.0000e-02  eta: 0:25:18  time: 0.0945  data_time: 0.0778  memory: 149  loss: 0.4874  fgnd: 0.3112  bgnd: 0.0163  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:29:45 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_122843
2025/06/23 12:29:45 - mmengine - INFO - Saving checkpoint at 3 epochs
2025/06/23 12:29:46 - mmengine - INFO - Epoch(val)   [3][10/17]    eta: 0:00:00  time: 0.2477  data_time: 0.2345  memory: 149  
2025/06/23 12:29:47 - mmengine - INFO - Epoch(val) [3][17/17]    P: 0.0000  R: 0.0000  F1: 0.0000  data_time: 0.0355  time: 0.0431
2025/06/23 12:29:48 - mmengine - INFO - Epoch(train)   [4][ 10/123]  lr: 2.0000e-02  eta: 0:25:18  time: 0.0877  data_time: 0.0720  memory: 149  loss: 0.4296  fgnd: 0.6410  bgnd: 0.0267  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:29:50 - mmengine - INFO - Epoch(train)   [4][ 20/123]  lr: 2.0000e-02  eta: 0:25:32  time: 0.0837  data_time: 0.0664  memory: 149  loss: 0.4886  fgnd: 0.7679  bgnd: 0.0347  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:29:52 - mmengine - INFO - Epoch(train)   [4][ 30/123]  lr: 2.0000e-02  eta: 0:25:49  time: 0.1153  data_time: 0.0963  memory: 149  loss: 0.5121  fgnd: 0.3808  bgnd: 0.0189  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:29:52 - mmengine - INFO - Epoch(train)   [4][ 40/123]  lr: 2.0000e-02  eta: 0:25:22  time: 0.1189  data_time: 0.0997  memory: 149  loss: 0.5100  fgnd: 0.3695  bgnd: 0.0167  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:29:53 - mmengine - INFO - Epoch(train)   [4][ 50/123]  lr: 2.0000e-02  eta: 0:24:51  time: 0.1195  data_time: 0.1002  memory: 149  loss: 0.5184  fgnd: 0.3140  bgnd: 0.0156  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:29:53 - mmengine - INFO - Epoch(train)   [4][ 60/123]  lr: 2.0000e-02  eta: 0:24:22  time: 0.0913  data_time: 0.0732  memory: 149  loss: 0.4567  fgnd: 0.2628  bgnd: 0.0154  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:29:54 - mmengine - INFO - Epoch(train)   [4][ 70/123]  lr: 2.0000e-02  eta: 0:24:29  time: 0.0863  data_time: 0.0704  memory: 149  loss: 0.4123  fgnd: 0.6815  bgnd: 0.0310  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:29:56 - mmengine - INFO - Epoch(train)   [4][ 80/123]  lr: 2.0000e-02  eta: 0:24:33  time: 0.0775  data_time: 0.0613  memory: 149  loss: 0.4404  fgnd: 0.6560  bgnd: 0.0302  P: 0.5000  R: 0.0109  F1: 0.0213
2025/06/23 12:29:58 - mmengine - INFO - Epoch(train)   [4][ 90/123]  lr: 2.0000e-02  eta: 0:24:48  time: 0.1063  data_time: 0.0875  memory: 149  loss: 0.4683  fgnd: 0.3841  bgnd: 0.0191  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:29:59 - mmengine - INFO - Epoch(train)   [4][100/123]  lr: 2.0000e-02  eta: 0:24:39  time: 0.1208  data_time: 0.1017  memory: 149  loss: 0.4655  fgnd: 0.3263  bgnd: 0.0153  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:29:59 - mmengine - INFO - Epoch(train)   [4][110/123]  lr: 2.0000e-02  eta: 0:24:13  time: 0.1212  data_time: 0.1020  memory: 149  loss: 0.4685  fgnd: 0.3153  bgnd: 0.0149  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:29:59 - mmengine - INFO - Epoch(train)   [4][120/123]  lr: 2.0000e-02  eta: 0:23:48  time: 0.0951  data_time: 0.0770  memory: 149  loss: 0.4247  fgnd: 0.2690  bgnd: 0.0142  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:29:59 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_122843
2025/06/23 12:29:59 - mmengine - INFO - Saving checkpoint at 4 epochs
2025/06/23 12:30:00 - mmengine - INFO - Epoch(val)   [4][10/17]    eta: 0:00:00  time: 0.0435  data_time: 0.0358  memory: 149  
2025/06/23 12:30:00 - mmengine - INFO - Epoch(val) [4][17/17]    P: 0.0000  R: 0.0000  F1: 0.0000  data_time: 0.0363  time: 0.0435
2025/06/23 12:30:02 - mmengine - INFO - Epoch(train)   [5][ 10/123]  lr: 2.0000e-02  eta: 0:23:50  time: 0.0879  data_time: 0.0722  memory: 149  loss: 0.3796  fgnd: 0.7368  bgnd: 0.0287  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:30:04 - mmengine - INFO - Epoch(train)   [5][ 20/123]  lr: 2.0000e-02  eta: 0:24:01  time: 0.0839  data_time: 0.0656  memory: 149  loss: 0.4332  fgnd: 0.5559  bgnd: 0.0287  P: 0.2500  R: 0.0208  F1: 0.0385
2025/06/23 12:30:05 - mmengine - INFO - Epoch(train)   [5][ 30/123]  lr: 2.0000e-02  eta: 0:24:14  time: 0.1147  data_time: 0.0943  memory: 149  loss: 0.4573  fgnd: 0.3742  bgnd: 0.0173  P: 0.4000  R: 0.0435  F1: 0.0784
2025/06/23 12:30:06 - mmengine - INFO - Epoch(train)   [5][ 40/123]  lr: 2.0000e-02  eta: 0:23:55  time: 0.1185  data_time: 0.0975  memory: 149  loss: 0.4610  fgnd: 0.3265  bgnd: 0.0148  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:30:06 - mmengine - INFO - Epoch(train)   [5][ 50/123]  lr: 2.0000e-02  eta: 0:23:32  time: 0.1190  data_time: 0.0979  memory: 149  loss: 0.4698  fgnd: 0.2740  bgnd: 0.0147  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:30:06 - mmengine - INFO - Epoch(train)   [5][ 60/123]  lr: 2.0000e-02  eta: 0:23:10  time: 0.0897  data_time: 0.0698  memory: 149  loss: 0.4178  fgnd: 0.2620  bgnd: 0.0142  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:30:08 - mmengine - INFO - Epoch(train)   [5][ 70/123]  lr: 2.0000e-02  eta: 0:23:17  time: 0.0861  data_time: 0.0683  memory: 149  loss: 0.3783  fgnd: 0.7783  bgnd: 0.0325  P: 0.8571  R: 0.0556  F1: 0.1043
2025/06/23 12:30:09 - mmengine - INFO - Epoch(train)   [5][ 80/123]  lr: 2.0000e-02  eta: 0:23:22  time: 0.0786  data_time: 0.0596  memory: 149  loss: 0.4156  fgnd: 0.5496  bgnd: 0.0280  P: 0.6471  R: 0.1100  F1: 0.1880
2025/06/23 12:30:11 - mmengine - INFO - Epoch(train)   [5][ 90/123]  lr: 2.0000e-02  eta: 0:23:34  time: 0.1071  data_time: 0.0849  memory: 149  loss: 0.4510  fgnd: 0.3737  bgnd: 0.0183  P: 0.8000  R: 0.0755  F1: 0.1379
2025/06/23 12:30:12 - mmengine - INFO - Epoch(train)   [5][100/123]  lr: 2.0000e-02  eta: 0:23:29  time: 0.1229  data_time: 0.0995  memory: 149  loss: 0.4525  fgnd: 0.2980  bgnd: 0.0143  P: 0.7143  R: 0.1471  F1: 0.2439
2025/06/23 12:30:12 - mmengine - INFO - Epoch(train)   [5][110/123]  lr: 2.0000e-02  eta: 0:23:09  time: 0.1231  data_time: 0.0994  memory: 149  loss: 0.4613  fgnd: 0.2897  bgnd: 0.0143  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:30:13 - mmengine - INFO - Epoch(train)   [5][120/123]  lr: 2.0000e-02  eta: 0:22:50  time: 0.0964  data_time: 0.0743  memory: 149  loss: 0.4243  fgnd: 0.2466  bgnd: 0.0141  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:30:13 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_122843
2025/06/23 12:30:13 - mmengine - INFO - Saving checkpoint at 5 epochs
2025/06/23 12:30:13 - mmengine - INFO - Epoch(val)   [5][10/17]    eta: 0:00:00  time: 0.0432  data_time: 0.0356  memory: 149  
2025/06/23 12:30:14 - mmengine - INFO - Epoch(val) [5][17/17]    P: 0.0123  R: 0.0043  F1: 0.0064  data_time: 0.0350  time: 0.0428
2025/06/23 12:30:15 - mmengine - INFO - Epoch(train)   [6][ 10/123]  lr: 2.0000e-02  eta: 0:22:52  time: 0.0899  data_time: 0.0714  memory: 149  loss: 0.3928  fgnd: 0.7904  bgnd: 0.0328  P: 0.5000  R: 0.0364  F1: 0.0678
2025/06/23 12:30:17 - mmengine - INFO - Epoch(train)   [6][ 20/123]  lr: 2.0000e-02  eta: 0:23:02  time: 0.0848  data_time: 0.0631  memory: 149  loss: 0.4402  fgnd: 0.4741  bgnd: 0.0249  P: 0.8667  R: 0.1444  F1: 0.2476
