2025/06/23 12:33:02 - mmengine - INFO - 
------------------------------------------------------------
System environment:
    sys.platform: win32
    Python: 3.12.11 | packaged by Anaconda, Inc. | (main, Jun  5 2025, 12:58:53) [MSC v.1929 64 bit (AMD64)]
    CUDA available: True
    MUSA available: False
    numpy_random_seed: 1855340551
    GPU 0: NVIDIA GeForce RTX 5060 Ti
    CUDA_HOME: None
    MSVC: Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35209 for x64
    GCC: n/a
    PyTorch: 2.8.0.dev20250608+cu128
    PyTorch compiling details: PyTorch built with:
  - C++ Version: 201703
  - MSVC 193833144
  - Intel(R) oneAPI Math Kernel Library Version 2025.1-Product Build 20250306 for Intel(R) 64 architecture applications
  - Intel(R) MKL-DNN v3.7.1 (Git Hash 8d263e693366ef8db40acc569cc7d8edf644556d)
  - OpenMP 2019
  - LAPACK is enabled (usually provided by MKL)
  - CPU capability usage: AVX512
  - CUDA Runtime 12.8
  - NVCC architecture flags: -gencode;arch=compute_61,code=sm_61;-gencode;arch=compute_70,code=sm_70;-gencode;arch=compute_75,code=sm_75;-gencode;arch=compute_80,code=sm_80;-gencode;arch=compute_86,code=sm_86;-gencode;arch=compute_90,code=sm_90;-gencode;arch=compute_100,code=sm_100;-gencode;arch=compute_120,code=sm_120
  - CuDNN 90.7.1
  - Magma 2.5.4
  - Build settings: BLAS_INFO=mkl, BUILD_TYPE=Release, COMMIT_SHA=843156205ec57cf78d5cf10bf982656b3db51dfd, CUDA_VERSION=12.8, CUDNN_VERSION=9.7.1, CXX_COMPILER=C:/actions-runner/_work/pytorch/pytorch/pytorch/.ci/pytorch/windows/tmp_bin/sccache-cl.exe, CXX_FLAGS=/DWIN32 /D_WINDOWS /GR /EHsc /Zc:__cplusplus /bigobj /FS /utf-8 -DUSE_PTHREADPOOL -DNDEBUG -DUSE_KINETO -DLIBKINETO_NOROCTRACER -DLIBKINETO_NOXPUPTI=ON -DUSE_FBGEMM -DUSE_XNNPACK -DSYMBOLICATE_MOBILE_DEBUG_HANDLE /wd4624 /wd4068 /wd4067 /wd4267 /wd4661 /wd4717 /wd4244 /wd4804 /wd4273, LAPACK_INFO=mkl, PERF_WITH_AVX=1, PERF_WITH_AVX2=1, TORCH_VERSION=2.8.0, USE_CUDA=ON, USE_CUDNN=ON, USE_CUSPARSELT=OFF, USE_GFLAGS=OFF, USE_GLOG=OFF, USE_GLOO=ON, USE_MKL=ON, USE_MKLDNN=ON, USE_MPI=OFF, USE_NCCL=OFF, USE_NNPACK=OFF, USE_OPENMP=ON, USE_ROCM=OFF, USE_ROCM_KERNEL_ASSERT=OFF, USE_XCCL=OFF, USE_XPU=OFF, 

    TorchVision: 0.23.0.dev20250609+cu128
    OpenCV: 4.11.0
    MMEngine: 0.10.4

Runtime environment:
    cudnn_benchmark: True
    mp_cfg: {'mp_start_method': 'fork', 'opencv_num_threads': 0}
    dist_cfg: {'backend': 'nccl'}
    seed: 1855340551
    deterministic: False
    Distributed launcher: none
    Distributed training: False
    GPU number: 1
------------------------------------------------------------

2025/06/23 12:33:04 - mmengine - INFO - Distributed training is not used, all SyncBatchNorm (SyncBN) layers in the model will be automatically reverted to BatchNormXd layers if they are used.
2025/06/23 12:33:05 - mmengine - WARNING - The prefix is not set in metric class FomoMetric.
Name of parameter - Initialization information

backbone.conv1.conv.weight - torch.Size([16, 3, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.conv1.norm.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.conv1.norm.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer1.0.conv.0.conv.weight - torch.Size([16, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer1.0.conv.0.norm.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer1.0.conv.0.norm.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer1.0.conv.1.weight - torch.Size([8, 16, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer1.0.conv.2.weight - torch.Size([8]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer1.0.conv.2.bias - torch.Size([8]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.0.conv.0.conv.weight - torch.Size([48, 8, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer2.0.conv.0.norm.weight - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.0.conv.0.norm.bias - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.0.conv.1.conv.weight - torch.Size([48, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer2.0.conv.1.norm.weight - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.0.conv.1.norm.bias - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.0.conv.2.weight - torch.Size([8, 48, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer2.0.conv.3.weight - torch.Size([8]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.0.conv.3.bias - torch.Size([8]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.1.conv.0.conv.weight - torch.Size([48, 8, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer2.1.conv.0.norm.weight - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.1.conv.0.norm.bias - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.1.conv.1.conv.weight - torch.Size([48, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer2.1.conv.1.norm.weight - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.1.conv.1.norm.bias - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.1.conv.2.weight - torch.Size([8, 48, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer2.1.conv.3.weight - torch.Size([8]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.1.conv.3.bias - torch.Size([8]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.0.conv.0.conv.weight - torch.Size([48, 8, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer3.0.conv.0.norm.weight - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.0.conv.0.norm.bias - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.0.conv.1.conv.weight - torch.Size([48, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer3.0.conv.1.norm.weight - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.0.conv.1.norm.bias - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.0.conv.2.weight - torch.Size([16, 48, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer3.0.conv.3.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.0.conv.3.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.1.conv.0.conv.weight - torch.Size([96, 16, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer3.1.conv.0.norm.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.1.conv.0.norm.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.1.conv.1.conv.weight - torch.Size([96, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer3.1.conv.1.norm.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.1.conv.1.norm.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.1.conv.2.weight - torch.Size([16, 96, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer3.1.conv.3.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.1.conv.3.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.2.conv.0.conv.weight - torch.Size([96, 16, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer3.2.conv.0.norm.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.2.conv.0.norm.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.2.conv.1.conv.weight - torch.Size([96, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer3.2.conv.1.norm.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.2.conv.1.norm.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.2.conv.2.weight - torch.Size([16, 96, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer3.2.conv.3.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.2.conv.3.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.0.conv.0.conv.weight - torch.Size([96, 16, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.0.conv.0.norm.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.0.conv.0.norm.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.0.conv.1.conv.weight - torch.Size([96, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.0.conv.1.norm.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.0.conv.1.norm.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.0.conv.2.weight - torch.Size([24, 96, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.0.conv.3.weight - torch.Size([24]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.0.conv.3.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.1.conv.0.conv.weight - torch.Size([144, 24, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.1.conv.0.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.1.conv.0.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.1.conv.1.conv.weight - torch.Size([144, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.1.conv.1.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.1.conv.1.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.1.conv.2.weight - torch.Size([24, 144, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.1.conv.3.weight - torch.Size([24]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.1.conv.3.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.2.conv.0.conv.weight - torch.Size([144, 24, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.2.conv.0.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.2.conv.0.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.2.conv.1.conv.weight - torch.Size([144, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.2.conv.1.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.2.conv.1.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.2.conv.2.weight - torch.Size([24, 144, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.2.conv.3.weight - torch.Size([24]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.2.conv.3.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.3.conv.0.conv.weight - torch.Size([144, 24, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.3.conv.0.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.3.conv.0.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.3.conv.1.conv.weight - torch.Size([144, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.3.conv.1.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.3.conv.1.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.3.conv.2.weight - torch.Size([24, 144, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.3.conv.3.weight - torch.Size([24]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.3.conv.3.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.0.conv.0.conv.weight - torch.Size([144, 24, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer5.0.conv.0.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.0.conv.0.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.0.conv.1.conv.weight - torch.Size([144, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer5.0.conv.1.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.0.conv.1.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.0.conv.2.weight - torch.Size([32, 144, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer5.0.conv.3.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.0.conv.3.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.1.conv.0.conv.weight - torch.Size([192, 32, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer5.1.conv.0.norm.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.1.conv.0.norm.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.1.conv.1.conv.weight - torch.Size([192, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer5.1.conv.1.norm.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.1.conv.1.norm.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.1.conv.2.weight - torch.Size([32, 192, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer5.1.conv.3.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.1.conv.3.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.2.conv.0.conv.weight - torch.Size([192, 32, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer5.2.conv.0.norm.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.2.conv.0.norm.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.2.conv.1.conv.weight - torch.Size([192, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer5.2.conv.1.norm.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.2.conv.1.norm.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.2.conv.2.weight - torch.Size([32, 192, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer5.2.conv.3.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.2.conv.3.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.0.conv.0.conv.weight - torch.Size([192, 32, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer6.0.conv.0.norm.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.0.conv.0.norm.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.0.conv.1.conv.weight - torch.Size([192, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer6.0.conv.1.norm.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.0.conv.1.norm.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.0.conv.2.weight - torch.Size([56, 192, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer6.0.conv.3.weight - torch.Size([56]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.0.conv.3.bias - torch.Size([56]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.1.conv.0.conv.weight - torch.Size([336, 56, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer6.1.conv.0.norm.weight - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.1.conv.0.norm.bias - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.1.conv.1.conv.weight - torch.Size([336, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer6.1.conv.1.norm.weight - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.1.conv.1.norm.bias - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.1.conv.2.weight - torch.Size([56, 336, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer6.1.conv.3.weight - torch.Size([56]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.1.conv.3.bias - torch.Size([56]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.2.conv.0.conv.weight - torch.Size([336, 56, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer6.2.conv.0.norm.weight - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.2.conv.0.norm.bias - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.2.conv.1.conv.weight - torch.Size([336, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer6.2.conv.1.norm.weight - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.2.conv.1.norm.bias - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.2.conv.2.weight - torch.Size([56, 336, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer6.2.conv.3.weight - torch.Size([56]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.2.conv.3.bias - torch.Size([56]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer7.0.conv.0.conv.weight - torch.Size([336, 56, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer7.0.conv.0.norm.weight - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer7.0.conv.0.norm.bias - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer7.0.conv.1.conv.weight - torch.Size([336, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer7.0.conv.1.norm.weight - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer7.0.conv.1.norm.bias - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer7.0.conv.2.weight - torch.Size([112, 336, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer7.0.conv.3.weight - torch.Size([112]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer7.0.conv.3.bias - torch.Size([112]): 
The value is the same before and after calling `init_weights` of Fomo  

bbox_head.convs_bridge.0.0.weight - torch.Size([48, 16, 1, 1]): 
Initialized by user-defined `init_weights` in FomoHead  

bbox_head.convs_bridge.0.1.weight - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

bbox_head.convs_bridge.0.1.bias - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

bbox_head.convs_pred.0.weight - torch.Size([3, 48, 1, 1]): 
Initialized by user-defined `init_weights` in FomoHead  

bbox_head.convs_pred.0.bias - torch.Size([3]): 
Initialized by user-defined `init_weights` in FomoHead  
2025/06/23 12:33:05 - mmengine - WARNING - "FileClient" will be deprecated in future. Please use io functions in https://mmengine.readthedocs.io/en/latest/api/fileio.html#file-io
2025/06/23 12:33:05 - mmengine - WARNING - "HardDiskBackend" is the alias of "LocalBackend" and the former will be deprecated in future.
2025/06/23 12:33:05 - mmengine - INFO - Checkpoints will be saved to D:\OBJECT_DETECTION\sscma\ModelAssistant\work_dirs\fomo_mobnetv2_0.35_x8_coco.
2025/06/23 12:33:15 - mmengine - INFO - Epoch(train)   [1][ 10/123]  lr: 6.2207e-03  eta: 3:33:38  time: 1.0430  data_time: 0.9680  memory: 161  loss: 1.1490  fgnd: 0.9214  bgnd: 0.2107  P: 0.1453  R: 0.5587  F1: 0.2307
2025/06/23 12:33:17 - mmengine - INFO - Epoch(train)   [1][ 20/123]  lr: 1.3110e-02  eta: 2:04:30  time: 0.6083  data_time: 0.5515  memory: 148  loss: 1.0971  fgnd: 0.9502  bgnd: 0.0974  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:33:19 - mmengine - INFO - Epoch(train)   [1][ 30/123]  lr: 2.0000e-02  eta: 1:35:28  time: 0.4669  data_time: 0.4233  memory: 148  loss: 1.0056  fgnd: 0.6459  bgnd: 0.0340  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:33:19 - mmengine - INFO - Epoch(train)   [1][ 40/123]  lr: 2.0000e-02  eta: 1:13:33  time: 0.3600  data_time: 0.3239  memory: 148  loss: 0.8750  fgnd: 0.3682  bgnd: 0.0180  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:33:20 - mmengine - INFO - Epoch(train)   [1][ 50/123]  lr: 2.0000e-02  eta: 0:59:45  time: 0.2927  data_time: 0.2612  memory: 148  loss: 0.7689  fgnd: 0.3041  bgnd: 0.0168  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:33:20 - mmengine - INFO - Epoch(train)   [1][ 60/123]  lr: 2.0000e-02  eta: 0:50:32  time: 0.0887  data_time: 0.0695  memory: 148  loss: 0.5992  fgnd: 0.2868  bgnd: 0.0196  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:33:21 - mmengine - INFO - Epoch(train)   [1][ 70/123]  lr: 2.0000e-02  eta: 0:47:40  time: 0.0841  data_time: 0.0690  memory: 148  loss: 0.6531  fgnd: 1.4401  bgnd: 0.0346  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:33:23 - mmengine - INFO - Epoch(train)   [1][ 80/123]  lr: 2.0000e-02  eta: 0:45:24  time: 0.0766  data_time: 0.0609  memory: 148  loss: 0.6936  fgnd: 0.7916  bgnd: 0.0608  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:33:25 - mmengine - INFO - Epoch(train)   [1][ 90/123]  lr: 2.0000e-02  eta: 0:44:25  time: 0.1050  data_time: 0.0872  memory: 148  loss: 0.7436  fgnd: 0.5824  bgnd: 0.0531  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:33:26 - mmengine - INFO - Epoch(train)   [1][100/123]  lr: 2.0000e-02  eta: 0:42:00  time: 0.1205  data_time: 0.1027  memory: 148  loss: 0.7972  fgnd: 0.5712  bgnd: 0.0482  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:33:26 - mmengine - INFO - Epoch(train)   [1][110/123]  lr: 2.0000e-02  eta: 0:38:37  time: 0.1209  data_time: 0.1032  memory: 148  loss: 0.8497  fgnd: 0.4776  bgnd: 0.0403  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:33:26 - mmengine - INFO - Epoch(train)   [1][120/123]  lr: 2.0000e-02  eta: 0:35:46  time: 0.0955  data_time: 0.0788  memory: 148  loss: 0.6818  fgnd: 0.4014  bgnd: 0.0332  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:33:26 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_123300
2025/06/23 12:33:26 - mmengine - INFO - Saving checkpoint at 1 epochs
2025/06/23 12:33:36 - mmengine - INFO - Epoch(val)   [1][10/17]    eta: 0:00:06  time: 0.9381  data_time: 0.9183  memory: 148  
2025/06/23 12:33:36 - mmengine - INFO - Epoch(val) [1][17/17]    P: 0.0000  R: 0.0000  F1: 0.0000  data_time: 0.5501  time: 0.5644
2025/06/23 12:33:38 - mmengine - INFO - Epoch(train)   [2][ 10/123]  lr: 2.0000e-02  eta: 0:34:57  time: 0.0904  data_time: 0.0733  memory: 150  loss: 0.5996  fgnd: 0.7795  bgnd: 0.0371  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:33:39 - mmengine - INFO - Epoch(train)   [2][ 20/123]  lr: 2.0000e-02  eta: 0:35:03  time: 0.0868  data_time: 0.0698  memory: 149  loss: 0.6301  fgnd: 0.6735  bgnd: 0.0360  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:33:42 - mmengine - INFO - Epoch(train)   [2][ 30/123]  lr: 2.0000e-02  eta: 0:35:27  time: 0.1208  data_time: 0.1032  memory: 149  loss: 0.6228  fgnd: 0.4584  bgnd: 0.0254  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:33:42 - mmengine - INFO - Epoch(train)   [2][ 40/123]  lr: 2.0000e-02  eta: 0:33:45  time: 0.1244  data_time: 0.1065  memory: 149  loss: 0.6061  fgnd: 0.4077  bgnd: 0.0227  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:33:42 - mmengine - INFO - Epoch(train)   [2][ 50/123]  lr: 2.0000e-02  eta: 0:32:03  time: 0.1235  data_time: 0.1068  memory: 149  loss: 0.5969  fgnd: 0.3438  bgnd: 0.0197  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:33:42 - mmengine - INFO - Epoch(train)   [2][ 60/123]  lr: 2.0000e-02  eta: 0:30:32  time: 0.0950  data_time: 0.0794  memory: 149  loss: 0.5171  fgnd: 0.2992  bgnd: 0.0212  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:33:44 - mmengine - INFO - Epoch(train)   [2][ 70/123]  lr: 2.0000e-02  eta: 0:30:31  time: 0.0891  data_time: 0.0743  memory: 149  loss: 0.4808  fgnd: 0.8749  bgnd: 0.0322  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:33:45 - mmengine - INFO - Epoch(train)   [2][ 80/123]  lr: 2.0000e-02  eta: 0:30:30  time: 0.0784  data_time: 0.0627  memory: 149  loss: 0.5316  fgnd: 0.6082  bgnd: 0.0290  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:33:47 - mmengine - INFO - Epoch(train)   [2][ 90/123]  lr: 2.0000e-02  eta: 0:30:49  time: 0.1077  data_time: 0.0905  memory: 149  loss: 0.5636  fgnd: 0.4280  bgnd: 0.0210  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:33:48 - mmengine - INFO - Epoch(train)   [2][100/123]  lr: 2.0000e-02  eta: 0:30:19  time: 0.1230  data_time: 0.1058  memory: 149  loss: 0.5748  fgnd: 0.4427  bgnd: 0.0196  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:33:49 - mmengine - INFO - Epoch(train)   [2][110/123]  lr: 2.0000e-02  eta: 0:29:12  time: 0.1232  data_time: 0.1061  memory: 149  loss: 0.5899  fgnd: 0.3977  bgnd: 0.0196  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:33:49 - mmengine - INFO - Epoch(train)   [2][120/123]  lr: 2.0000e-02  eta: 0:28:09  time: 0.0972  data_time: 0.0809  memory: 149  loss: 0.5342  fgnd: 0.3118  bgnd: 0.0176  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:33:49 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_123300
2025/06/23 12:33:49 - mmengine - INFO - Saving checkpoint at 2 epochs
2025/06/23 12:33:50 - mmengine - INFO - Epoch(val)   [2][10/17]    eta: 0:00:00  time: 0.3765  data_time: 0.3642  memory: 149  
2025/06/23 12:33:50 - mmengine - INFO - Epoch(val) [2][17/17]    P: 0.0000  R: 0.0000  F1: 0.0000  data_time: 0.0365  time: 0.0454
2025/06/23 12:33:52 - mmengine - INFO - Epoch(train)   [3][ 10/123]  lr: 2.0000e-02  eta: 0:28:05  time: 0.0906  data_time: 0.0758  memory: 149  loss: 0.4557  fgnd: 0.7708  bgnd: 0.0304  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:33:53 - mmengine - INFO - Epoch(train)   [3][ 20/123]  lr: 2.0000e-02  eta: 0:28:23  time: 0.0865  data_time: 0.0713  memory: 149  loss: 0.5122  fgnd: 0.7128  bgnd: 0.0320  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:33:55 - mmengine - INFO - Epoch(train)   [3][ 30/123]  lr: 2.0000e-02  eta: 0:28:42  time: 0.1173  data_time: 0.1002  memory: 149  loss: 0.5285  fgnd: 0.4432  bgnd: 0.0203  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:33:56 - mmengine - INFO - Epoch(train)   [3][ 40/123]  lr: 2.0000e-02  eta: 0:27:58  time: 0.1208  data_time: 0.1037  memory: 149  loss: 0.5322  fgnd: 0.3874  bgnd: 0.0165  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:33:56 - mmengine - INFO - Epoch(train)   [3][ 50/123]  lr: 2.0000e-02  eta: 0:27:09  time: 0.1213  data_time: 0.1041  memory: 149  loss: 0.5426  fgnd: 0.3513  bgnd: 0.0174  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:33:56 - mmengine - INFO - Epoch(train)   [3][ 60/123]  lr: 2.0000e-02  eta: 0:26:23  time: 0.0915  data_time: 0.0751  memory: 149  loss: 0.4890  fgnd: 0.2816  bgnd: 0.0169  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:33:58 - mmengine - INFO - Epoch(train)   [3][ 70/123]  lr: 2.0000e-02  eta: 0:26:29  time: 0.0851  data_time: 0.0696  memory: 149  loss: 0.4459  fgnd: 0.7285  bgnd: 0.0301  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:33:59 - mmengine - INFO - Epoch(train)   [3][ 80/123]  lr: 2.0000e-02  eta: 0:26:32  time: 0.0763  data_time: 0.0614  memory: 149  loss: 0.4748  fgnd: 0.5795  bgnd: 0.0279  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:34:01 - mmengine - INFO - Epoch(train)   [3][ 90/123]  lr: 2.0000e-02  eta: 0:26:50  time: 0.1053  data_time: 0.0893  memory: 149  loss: 0.4994  fgnd: 0.3859  bgnd: 0.0196  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:34:02 - mmengine - INFO - Epoch(train)   [3][100/123]  lr: 2.0000e-02  eta: 0:26:35  time: 0.1201  data_time: 0.1038  memory: 149  loss: 0.4975  fgnd: 0.4040  bgnd: 0.0164  P: 1.0000  R: 0.0303  F1: 0.0588
2025/06/23 12:34:02 - mmengine - INFO - Epoch(train)   [3][110/123]  lr: 2.0000e-02  eta: 0:25:57  time: 0.1200  data_time: 0.1040  memory: 149  loss: 0.5025  fgnd: 0.3170  bgnd: 0.0157  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:34:02 - mmengine - INFO - Epoch(train)   [3][120/123]  lr: 2.0000e-02  eta: 0:25:20  time: 0.0945  data_time: 0.0791  memory: 149  loss: 0.4593  fgnd: 0.3144  bgnd: 0.0154  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:34:02 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_123300
2025/06/23 12:34:02 - mmengine - INFO - Saving checkpoint at 3 epochs
2025/06/23 12:34:03 - mmengine - INFO - Epoch(val)   [3][10/17]    eta: 0:00:00  time: 0.2490  data_time: 0.2377  memory: 149  
2025/06/23 12:34:03 - mmengine - INFO - Epoch(val) [3][17/17]    P: 0.0000  R: 0.0000  F1: 0.0000  data_time: 0.0348  time: 0.0427
2025/06/23 12:34:05 - mmengine - INFO - Epoch(train)   [4][ 10/123]  lr: 2.0000e-02  eta: 0:25:19  time: 0.0865  data_time: 0.0719  memory: 149  loss: 0.4193  fgnd: 0.6855  bgnd: 0.0291  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:34:07 - mmengine - INFO - Epoch(train)   [4][ 20/123]  lr: 2.0000e-02  eta: 0:25:32  time: 0.0818  data_time: 0.0663  memory: 149  loss: 0.4767  fgnd: 0.7122  bgnd: 0.0339  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:34:09 - mmengine - INFO - Epoch(train)   [4][ 30/123]  lr: 2.0000e-02  eta: 0:25:49  time: 0.1136  data_time: 0.0963  memory: 149  loss: 0.5023  fgnd: 0.3632  bgnd: 0.0202  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:34:09 - mmengine - INFO - Epoch(train)   [4][ 40/123]  lr: 2.0000e-02  eta: 0:25:22  time: 0.1174  data_time: 0.1001  memory: 149  loss: 0.5076  fgnd: 0.3912  bgnd: 0.0171  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:34:09 - mmengine - INFO - Epoch(train)   [4][ 50/123]  lr: 2.0000e-02  eta: 0:24:51  time: 0.1178  data_time: 0.1003  memory: 149  loss: 0.5159  fgnd: 0.3337  bgnd: 0.0160  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:34:10 - mmengine - INFO - Epoch(train)   [4][ 60/123]  lr: 2.0000e-02  eta: 0:24:21  time: 0.0904  data_time: 0.0741  memory: 149  loss: 0.4557  fgnd: 0.2545  bgnd: 0.0154  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:34:11 - mmengine - INFO - Epoch(train)   [4][ 70/123]  lr: 2.0000e-02  eta: 0:24:27  time: 0.0855  data_time: 0.0701  memory: 149  loss: 0.4050  fgnd: 0.6635  bgnd: 0.0283  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:34:13 - mmengine - INFO - Epoch(train)   [4][ 80/123]  lr: 2.0000e-02  eta: 0:24:35  time: 0.0788  data_time: 0.0631  memory: 149  loss: 0.4316  fgnd: 0.5837  bgnd: 0.0290  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:34:15 - mmengine - INFO - Epoch(train)   [4][ 90/123]  lr: 2.0000e-02  eta: 0:24:51  time: 0.1088  data_time: 0.0918  memory: 149  loss: 0.4564  fgnd: 0.3729  bgnd: 0.0194  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:34:16 - mmengine - INFO - Epoch(train)   [4][100/123]  lr: 2.0000e-02  eta: 0:24:44  time: 0.1253  data_time: 0.1079  memory: 149  loss: 0.4514  fgnd: 0.3257  bgnd: 0.0151  P: 0.5000  R: 0.0303  F1: 0.0571
2025/06/23 12:34:16 - mmengine - INFO - Epoch(train)   [4][110/123]  lr: 2.0000e-02  eta: 0:24:18  time: 0.1258  data_time: 0.1080  memory: 149  loss: 0.4545  fgnd: 0.2931  bgnd: 0.0152  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:34:16 - mmengine - INFO - Epoch(train)   [4][120/123]  lr: 2.0000e-02  eta: 0:23:53  time: 0.1003  data_time: 0.0833  memory: 149  loss: 0.4200  fgnd: 0.2719  bgnd: 0.0141  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:34:16 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_123300
2025/06/23 12:34:16 - mmengine - INFO - Saving checkpoint at 4 epochs
2025/06/23 12:34:17 - mmengine - INFO - Epoch(val)   [4][10/17]    eta: 0:00:00  time: 0.0446  data_time: 0.0360  memory: 149  
2025/06/23 12:34:17 - mmengine - INFO - Epoch(val) [4][17/17]    P: 0.0000  R: 0.0000  F1: 0.0000  data_time: 0.0363  time: 0.0448
2025/06/23 12:34:19 - mmengine - INFO - Epoch(train)   [5][ 10/123]  lr: 2.0000e-02  eta: 0:23:56  time: 0.0923  data_time: 0.0765  memory: 149  loss: 0.3686  fgnd: 0.6108  bgnd: 0.0254  P: 1.0000  R: 0.0253  F1: 0.0494
2025/06/23 12:34:21 - mmengine - INFO - Epoch(train)   [5][ 20/123]  lr: 2.0000e-02  eta: 0:24:07  time: 0.0856  data_time: 0.0680  memory: 149  loss: 0.4273  fgnd: 0.6186  bgnd: 0.0295  P: 1.0000  R: 0.0211  F1: 0.0412
2025/06/23 12:34:23 - mmengine - INFO - Epoch(train)   [5][ 30/123]  lr: 2.0000e-02  eta: 0:24:21  time: 0.1168  data_time: 0.0982  memory: 149  loss: 0.4592  fgnd: 0.3615  bgnd: 0.0179  P: 0.3333  R: 0.0385  F1: 0.0690
2025/06/23 12:34:23 - mmengine - INFO - Epoch(train)   [5][ 40/123]  lr: 2.0000e-02  eta: 0:24:01  time: 0.1199  data_time: 0.1011  memory: 149  loss: 0.4648  fgnd: 0.3338  bgnd: 0.0147  P: 0.6667  R: 0.0606  F1: 0.1111
2025/06/23 12:34:23 - mmengine - INFO - Epoch(train)   [5][ 50/123]  lr: 2.0000e-02  eta: 0:23:38  time: 0.1201  data_time: 0.1012  memory: 149  loss: 0.4790  fgnd: 0.3116  bgnd: 0.0148  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:34:23 - mmengine - INFO - Epoch(train)   [5][ 60/123]  lr: 2.0000e-02  eta: 0:23:16  time: 0.0906  data_time: 0.0734  memory: 149  loss: 0.4350  fgnd: 0.2693  bgnd: 0.0142  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:34:25 - mmengine - INFO - Epoch(train)   [5][ 70/123]  lr: 2.0000e-02  eta: 0:23:22  time: 0.0851  data_time: 0.0693  memory: 149  loss: 0.3967  fgnd: 0.6564  bgnd: 0.0287  P: 1.0000  R: 0.0204  F1: 0.0400
2025/06/23 12:34:26 - mmengine - INFO - Epoch(train)   [5][ 80/123]  lr: 2.0000e-02  eta: 0:23:25  time: 0.0750  data_time: 0.0577  memory: 149  loss: 0.4343  fgnd: 0.5721  bgnd: 0.0286  P: 1.0000  R: 0.0594  F1: 0.1121
2025/06/23 12:34:28 - mmengine - INFO - Epoch(train)   [5][ 90/123]  lr: 2.0000e-02  eta: 0:23:36  time: 0.1032  data_time: 0.0839  memory: 149  loss: 0.4677  fgnd: 0.4541  bgnd: 0.0215  P: 0.7059  R: 0.1818  F1: 0.2892
2025/06/23 12:34:29 - mmengine - INFO - Epoch(train)   [5][100/123]  lr: 2.0000e-02  eta: 0:23:29  time: 0.1177  data_time: 0.0961  memory: 149  loss: 0.4691  fgnd: 0.3510  bgnd: 0.0150  P: 0.5333  R: 0.2162  F1: 0.3077
2025/06/23 12:34:29 - mmengine - INFO - Epoch(train)   [5][110/123]  lr: 2.0000e-02  eta: 0:23:10  time: 0.1180  data_time: 0.0955  memory: 149  loss: 0.4822  fgnd: 0.3099  bgnd: 0.0148  P: 0.9167  R: 0.2619  F1: 0.4074
2025/06/23 12:34:29 - mmengine - INFO - Epoch(train)   [5][120/123]  lr: 2.0000e-02  eta: 0:22:50  time: 0.0925  data_time: 0.0706  memory: 149  loss: 0.4475  fgnd: 0.2756  bgnd: 0.0141  P: 1.0000  R: 0.0303  F1: 0.0588
2025/06/23 12:34:30 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_123300
2025/06/23 12:34:30 - mmengine - INFO - Saving checkpoint at 5 epochs
2025/06/23 12:34:30 - mmengine - INFO - Epoch(val)   [5][10/17]    eta: 0:00:00  time: 0.0436  data_time: 0.0350  memory: 149  
2025/06/23 12:34:30 - mmengine - INFO - Epoch(val) [5][17/17]    P: 0.0541  R: 0.0085  F1: 0.0148  data_time: 0.0344  time: 0.0416
2025/06/23 12:34:32 - mmengine - INFO - Epoch(train)   [6][ 10/123]  lr: 2.0000e-02  eta: 0:22:50  time: 0.0856  data_time: 0.0657  memory: 149  loss: 0.3965  fgnd: 0.5201  bgnd: 0.0235  P: 1.0000  R: 0.0633  F1: 0.1190
2025/06/23 12:34:34 - mmengine - INFO - Epoch(train)   [6][ 20/123]  lr: 2.0000e-02  eta: 0:22:59  time: 0.0806  data_time: 0.0594  memory: 149  loss: 0.4326  fgnd: 0.6121  bgnd: 0.0275  P: 0.5000  R: 0.0312  F1: 0.0588
2025/06/23 12:34:36 - mmengine - INFO - Epoch(train)   [6][ 30/123]  lr: 2.0000e-02  eta: 0:23:09  time: 0.1107  data_time: 0.0884  memory: 149  loss: 0.4472  fgnd: 0.3284  bgnd: 0.0175  P: 0.8000  R: 0.2308  F1: 0.3582
2025/06/23 12:34:36 - mmengine - INFO - Epoch(train)   [6][ 40/123]  lr: 2.0000e-02  eta: 0:22:54  time: 0.1137  data_time: 0.0914  memory: 149  loss: 0.4391  fgnd: 0.3181  bgnd: 0.0139  P: 0.7500  R: 0.0938  F1: 0.1667
2025/06/23 12:34:36 - mmengine - INFO - Epoch(train)   [6][ 50/123]  lr: 2.0000e-02  eta: 0:22:36  time: 0.1142  data_time: 0.0914  memory: 149  loss: 0.4389  fgnd: 0.3170  bgnd: 0.0135  P: 0.5000  R: 0.1176  F1: 0.1905
2025/06/23 12:34:36 - mmengine - INFO - Epoch(train)   [6][ 60/123]  lr: 2.0000e-02  eta: 0:22:18  time: 0.0872  data_time: 0.0658  memory: 149  loss: 0.3913  fgnd: 0.2275  bgnd: 0.0129  P: 0.6667  R: 0.1143  F1: 0.1951
2025/06/23 12:34:38 - mmengine - INFO - Epoch(train)   [6][ 70/123]  lr: 2.0000e-02  eta: 0:22:23  time: 0.0833  data_time: 0.0634  memory: 149  loss: 0.3762  fgnd: 0.7934  bgnd: 0.0307  P: 0.8571  R: 0.1121  F1: 0.1983
2025/06/23 12:34:39 - mmengine - INFO - Epoch(train)   [6][ 80/123]  lr: 2.0000e-02  eta: 0:22:28  time: 0.0771  data_time: 0.0553  memory: 149  loss: 0.4167  fgnd: 0.6486  bgnd: 0.0312  P: 0.6250  R: 0.0431  F1: 0.0806
2025/06/23 12:34:41 - mmengine - INFO - Epoch(train)   [6][ 90/123]  lr: 2.0000e-02  eta: 0:22:38  time: 0.1057  data_time: 0.0812  memory: 149  loss: 0.4445  fgnd: 0.4051  bgnd: 0.0188  P: 0.7500  R: 0.0588  F1: 0.1091
2025/06/23 12:34:42 - mmengine - INFO - Epoch(train)   [6][100/123]  lr: 2.0000e-02  eta: 0:22:33  time: 0.1206  data_time: 0.0955  memory: 149  loss: 0.4552  fgnd: 0.3587  bgnd: 0.0141  P: 0.5000  R: 0.1351  F1: 0.2128
2025/06/23 12:34:42 - mmengine - INFO - Epoch(train)   [6][110/123]  lr: 2.0000e-02  eta: 0:22:17  time: 0.1208  data_time: 0.0955  memory: 149  loss: 0.4662  fgnd: 0.3008  bgnd: 0.0140  P: 0.7500  R: 0.1622  F1: 0.2667
2025/06/23 12:34:43 - mmengine - INFO - Epoch(train)   [6][120/123]  lr: 2.0000e-02  eta: 0:22:01  time: 0.0949  data_time: 0.0718  memory: 149  loss: 0.4157  fgnd: 0.2712  bgnd: 0.0136  P: 1.0000  R: 0.0588  F1: 0.1111
2025/06/23 12:34:43 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_123300
2025/06/23 12:34:43 - mmengine - INFO - Saving checkpoint at 6 epochs
2025/06/23 12:34:44 - mmengine - INFO - Epoch(val)   [6][10/17]    eta: 0:00:00  time: 0.0428  data_time: 0.0349  memory: 149  
2025/06/23 12:34:44 - mmengine - INFO - Epoch(val) [6][17/17]    P: 0.0396  R: 0.0169  F1: 0.0237  data_time: 0.0344  time: 0.0420
2025/06/23 12:34:45 - mmengine - INFO - Epoch(train)   [7][ 10/123]  lr: 2.0000e-02  eta: 0:22:03  time: 0.0878  data_time: 0.0665  memory: 149  loss: 0.3761  fgnd: 0.5340  bgnd: 0.0224  P: 0.8333  R: 0.1786  F1: 0.2941
2025/06/23 12:34:47 - mmengine - INFO - Epoch(train)   [7][ 20/123]  lr: 2.0000e-02  eta: 0:22:12  time: 0.0837  data_time: 0.0598  memory: 149  loss: 0.4288  fgnd: 0.6022  bgnd: 0.0282  P: 0.8571  R: 0.1176  F1: 0.2069
2025/06/23 12:34:49 - mmengine - INFO - Epoch(train)   [7][ 30/123]  lr: 2.0000e-02  eta: 0:22:22  time: 0.1150  data_time: 0.0892  memory: 149  loss: 0.4437  fgnd: 0.3651  bgnd: 0.0182  P: 0.6364  R: 0.1250  F1: 0.2090
2025/06/23 12:34:49 - mmengine - INFO - Epoch(train)   [7][ 40/123]  lr: 2.0000e-02  eta: 0:22:09  time: 0.1182  data_time: 0.0922  memory: 149  loss: 0.4442  fgnd: 0.3139  bgnd: 0.0138  P: 0.6923  R: 0.2432  F1: 0.3600
2025/06/23 12:34:50 - mmengine - INFO - Epoch(train)   [7][ 50/123]  lr: 2.0000e-02  eta: 0:21:54  time: 0.1189  data_time: 0.0922  memory: 149  loss: 0.4492  fgnd: 0.2611  bgnd: 0.0137  P: 0.8889  R: 0.4898  F1: 0.6316
2025/06/23 12:34:50 - mmengine - INFO - Epoch(train)   [7][ 60/123]  lr: 2.0000e-02  eta: 0:21:40  time: 0.0900  data_time: 0.0657  memory: 149  loss: 0.4018  fgnd: 0.2204  bgnd: 0.0130  P: 0.7895  R: 0.3488  F1: 0.4839
2025/06/23 12:34:51 - mmengine - INFO - Epoch(train)   [7][ 70/123]  lr: 2.0000e-02  eta: 0:21:44  time: 0.0848  data_time: 0.0631  memory: 149  loss: 0.3617  fgnd: 0.7151  bgnd: 0.0320  P: 0.9032  R: 0.2121  F1: 0.3436
2025/06/23 12:34:53 - mmengine - INFO - Epoch(train)   [7][ 80/123]  lr: 2.0000e-02  eta: 0:21:47  time: 0.0760  data_time: 0.0531  memory: 149  loss: 0.4015  fgnd: 0.6862  bgnd: 0.0322  P: 0.6250  R: 0.1230  F1: 0.2055
2025/06/23 12:34:55 - mmengine - INFO - Epoch(train)   [7][ 90/123]  lr: 2.0000e-02  eta: 0:21:57  time: 0.1059  data_time: 0.0799  memory: 149  loss: 0.4412  fgnd: 0.3761  bgnd: 0.0183  P: 0.6296  R: 0.2464  F1: 0.3542
2025/06/23 12:34:56 - mmengine - INFO - Epoch(train)   [7][100/123]  lr: 2.0000e-02  eta: 0:21:53  time: 0.1213  data_time: 0.0938  memory: 149  loss: 0.4467  fgnd: 0.3080  bgnd: 0.0135  P: 0.6538  R: 0.3953  F1: 0.4928
2025/06/23 12:34:56 - mmengine - INFO - Epoch(train)   [7][110/123]  lr: 2.0000e-02  eta: 0:21:40  time: 0.1219  data_time: 0.0942  memory: 149  loss: 0.4610  fgnd: 0.3128  bgnd: 0.0140  P: 0.6061  R: 0.4255  F1: 0.5000
2025/06/23 12:34:56 - mmengine - INFO - Epoch(train)   [7][120/123]  lr: 2.0000e-02  eta: 0:21:27  time: 0.0962  data_time: 0.0704  memory: 149  loss: 0.4311  fgnd: 0.2726  bgnd: 0.0131  P: 0.6842  R: 0.3250  F1: 0.4407
2025/06/23 12:34:56 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_123300
2025/06/23 12:34:56 - mmengine - INFO - Saving checkpoint at 7 epochs
2025/06/23 12:34:57 - mmengine - INFO - Epoch(val)   [7][10/17]    eta: 0:00:00  time: 0.0427  data_time: 0.0348  memory: 149  
2025/06/23 12:34:57 - mmengine - INFO - Epoch(val) [7][17/17]    P: 0.0180  R: 0.0086  F1: 0.0116  data_time: 0.0366  time: 0.0448
2025/06/23 12:34:59 - mmengine - INFO - Epoch(train)   [8][ 10/123]  lr: 2.0000e-02  eta: 0:21:30  time: 0.0922  data_time: 0.0687  memory: 149  loss: 0.3815  fgnd: 0.5802  bgnd: 0.0263  P: 0.7895  R: 0.1546  F1: 0.2586
2025/06/23 12:35:01 - mmengine - INFO - Epoch(train)   [8][ 20/123]  lr: 2.0000e-02  eta: 0:21:37  time: 0.0865  data_time: 0.0629  memory: 149  loss: 0.4205  fgnd: 0.5588  bgnd: 0.0265  P: 0.9286  R: 0.1327  F1: 0.2321
2025/06/23 12:35:03 - mmengine - INFO - Epoch(train)   [8][ 30/123]  lr: 2.0000e-02  eta: 0:21:45  time: 0.1164  data_time: 0.0910  memory: 149  loss: 0.4418  fgnd: 0.3769  bgnd: 0.0188  P: 0.6250  R: 0.2542  F1: 0.3614
2025/06/23 12:35:03 - mmengine - INFO - Epoch(train)   [8][ 40/123]  lr: 2.0000e-02  eta: 0:21:35  time: 0.1195  data_time: 0.0936  memory: 149  loss: 0.4334  fgnd: 0.2868  bgnd: 0.0134  P: 0.7586  R: 0.4583  F1: 0.5714
2025/06/23 12:35:03 - mmengine - INFO - Epoch(train)   [8][ 50/123]  lr: 2.0000e-02  eta: 0:21:22  time: 0.1196  data_time: 0.0935  memory: 149  loss: 0.4363  fgnd: 0.3428  bgnd: 0.0132  P: 0.5806  R: 0.4000  F1: 0.4737
2025/06/23 12:35:03 - mmengine - INFO - Epoch(train)   [8][ 60/123]  lr: 2.0000e-02  eta: 0:21:10  time: 0.0894  data_time: 0.0658  memory: 149  loss: 0.3923  fgnd: 0.2304  bgnd: 0.0123  P: 0.8750  R: 0.4667  F1: 0.6087
2025/06/23 12:35:05 - mmengine - INFO - Epoch(train)   [8][ 70/123]  lr: 2.0000e-02  eta: 0:21:14  time: 0.0842  data_time: 0.0621  memory: 149  loss: 0.3548  fgnd: 0.5807  bgnd: 0.0251  P: 0.7647  R: 0.1398  F1: 0.2364
2025/06/23 12:35:07 - mmengine - INFO - Epoch(train)   [8][ 80/123]  lr: 2.0000e-02  eta: 0:21:18  time: 0.0787  data_time: 0.0544  memory: 149  loss: 0.3873  fgnd: 0.4784  bgnd: 0.0236  P: 0.7059  R: 0.2400  F1: 0.3582
2025/06/23 12:35:09 - mmengine - INFO - Epoch(train)   [8][ 90/123]  lr: 2.0000e-02  eta: 0:21:27  time: 0.1097  data_time: 0.0824  memory: 149  loss: 0.4236  fgnd: 0.3533  bgnd: 0.0164  P: 0.6364  R: 0.1429  F1: 0.2333
2025/06/23 12:35:10 - mmengine - INFO - Epoch(train)   [8][100/123]  lr: 2.0000e-02  eta: 0:21:24  time: 0.1263  data_time: 0.0985  memory: 149  loss: 0.4260  fgnd: 0.3126  bgnd: 0.0131  P: 0.4000  R: 0.2051  F1: 0.2712
2025/06/23 12:35:10 - mmengine - INFO - Epoch(train)   [8][110/123]  lr: 2.0000e-02  eta: 0:21:13  time: 0.1264  data_time: 0.0986  memory: 149  loss: 0.4330  fgnd: 0.2756  bgnd: 0.0132  P: 0.6957  R: 0.3721  F1: 0.4848
2025/06/23 12:35:10 - mmengine - INFO - Epoch(train)   [8][120/123]  lr: 2.0000e-02  eta: 0:21:02  time: 0.1007  data_time: 0.0743  memory: 149  loss: 0.4050  fgnd: 0.2419  bgnd: 0.0129  P: 0.6744  R: 0.5577  F1: 0.6105
2025/06/23 12:35:10 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_123300
2025/06/23 12:35:10 - mmengine - INFO - Saving checkpoint at 8 epochs
2025/06/23 12:35:11 - mmengine - INFO - Epoch(val)   [8][10/17]    eta: 0:00:00  time: 0.0437  data_time: 0.0355  memory: 149  
2025/06/23 12:35:11 - mmengine - INFO - Epoch(val) [8][17/17]    P: 0.0200  R: 0.0293  F1: 0.0238  data_time: 0.0361  time: 0.0445
2025/06/23 12:35:13 - mmengine - INFO - Epoch(train)   [9][ 10/123]  lr: 2.0000e-02  eta: 0:21:03  time: 0.0919  data_time: 0.0687  memory: 149  loss: 0.3573  fgnd: 0.5716  bgnd: 0.0272  P: 0.6765  R: 0.2072  F1: 0.3172
2025/06/23 12:35:14 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_123300
2025/06/23 12:35:15 - mmengine - INFO - Epoch(train)   [9][ 20/123]  lr: 2.0000e-02  eta: 0:21:10  time: 0.0858  data_time: 0.0610  memory: 149  loss: 0.3986  fgnd: 0.6131  bgnd: 0.0296  P: 0.7000  R: 0.2276  F1: 0.3436
2025/06/23 12:35:16 - mmengine - INFO - Epoch(train)   [9][ 30/123]  lr: 2.0000e-02  eta: 0:21:17  time: 0.1157  data_time: 0.0889  memory: 149  loss: 0.4198  fgnd: 0.3064  bgnd: 0.0150  P: 0.6792  R: 0.5217  F1: 0.5902
2025/06/23 12:35:17 - mmengine - INFO - Epoch(train)   [9][ 40/123]  lr: 2.0000e-02  eta: 0:21:08  time: 0.1190  data_time: 0.0921  memory: 149  loss: 0.4175  fgnd: 0.2875  bgnd: 0.0125  P: 0.7826  R: 0.6000  F1: 0.6792
2025/06/23 12:35:17 - mmengine - INFO - Epoch(train)   [9][ 50/123]  lr: 2.0000e-02  eta: 0:20:57  time: 0.1192  data_time: 0.0923  memory: 149  loss: 0.4211  fgnd: 0.2829  bgnd: 0.0126  P: 0.7879  R: 0.5098  F1: 0.6190
2025/06/23 12:35:17 - mmengine - INFO - Epoch(train)   [9][ 60/123]  lr: 2.0000e-02  eta: 0:20:46  time: 0.0901  data_time: 0.0657  memory: 149  loss: 0.3763  fgnd: 0.2095  bgnd: 0.0123  P: 0.6757  R: 0.5319  F1: 0.5952
2025/06/23 12:35:19 - mmengine - INFO - Epoch(train)   [9][ 70/123]  lr: 2.0000e-02  eta: 0:20:50  time: 0.0846  data_time: 0.0620  memory: 149  loss: 0.3562  fgnd: 0.7610  bgnd: 0.0319  P: 0.9394  R: 0.2331  F1: 0.3735
2025/06/23 12:35:20 - mmengine - INFO - Epoch(train)   [9][ 80/123]  lr: 2.0000e-02  eta: 0:20:52  time: 0.0770  data_time: 0.0534  memory: 149  loss: 0.3860  fgnd: 0.6113  bgnd: 0.0277  P: 0.6944  R: 0.2193  F1: 0.3333
2025/06/23 12:35:22 - mmengine - INFO - Epoch(train)   [9][ 90/123]  lr: 2.0000e-02  eta: 0:20:58  time: 0.1053  data_time: 0.0797  memory: 149  loss: 0.4208  fgnd: 0.3018  bgnd: 0.0143  P: 0.4737  R: 0.2000  F1: 0.2812
2025/06/23 12:35:23 - mmengine - INFO - Epoch(train)   [9][100/123]  lr: 2.0000e-02  eta: 0:20:56  time: 0.1202  data_time: 0.0923  memory: 149  loss: 0.4191  fgnd: 0.2845  bgnd: 0.0130  P: 0.8462  R: 0.5789  F1: 0.6875
2025/06/23 12:35:23 - mmengine - INFO - Epoch(train)   [9][110/123]  lr: 2.0000e-02  eta: 0:20:46  time: 0.1205  data_time: 0.0927  memory: 149  loss: 0.4244  fgnd: 0.2763  bgnd: 0.0126  P: 0.6379  R: 0.6379  F1: 0.6379
2025/06/23 12:35:24 - mmengine - INFO - Epoch(train)   [9][120/123]  lr: 2.0000e-02  eta: 0:20:36  time: 0.0945  data_time: 0.0689  memory: 149  loss: 0.3794  fgnd: 0.2216  bgnd: 0.0124  P: 0.6400  R: 0.6154  F1: 0.6275
2025/06/23 12:35:24 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_123300
2025/06/23 12:35:24 - mmengine - INFO - Saving checkpoint at 9 epochs
2025/06/23 12:35:24 - mmengine - INFO - Epoch(val)   [9][10/17]    eta: 0:00:00  time: 0.0445  data_time: 0.0359  memory: 149  
2025/06/23 12:35:25 - mmengine - INFO - Epoch(val) [9][17/17]    P: 0.0142  R: 0.0211  F1: 0.0170  data_time: 0.0349  time: 0.0436
2025/06/23 12:35:26 - mmengine - INFO - Epoch(train)  [10][ 10/123]  lr: 2.0000e-02  eta: 0:20:37  time: 0.0888  data_time: 0.0652  memory: 149  loss: 0.3448  fgnd: 0.6388  bgnd: 0.0291  P: 0.7872  R: 0.2937  F1: 0.4277
2025/06/23 12:35:28 - mmengine - INFO - Epoch(train)  [10][ 20/123]  lr: 2.0000e-02  eta: 0:20:43  time: 0.0843  data_time: 0.0588  memory: 149  loss: 0.3852  fgnd: 0.5651  bgnd: 0.0275  P: 0.8182  R: 0.3025  F1: 0.4417
2025/06/23 12:35:30 - mmengine - INFO - Epoch(train)  [10][ 30/123]  lr: 2.0000e-02  eta: 0:20:49  time: 0.1154  data_time: 0.0897  memory: 149  loss: 0.4042  fgnd: 0.3165  bgnd: 0.0163  P: 0.7586  R: 0.3548  F1: 0.4835
2025/06/23 12:35:30 - mmengine - INFO - Epoch(train)  [10][ 40/123]  lr: 2.0000e-02  eta: 0:20:41  time: 0.1191  data_time: 0.0914  memory: 149  loss: 0.4023  fgnd: 0.2734  bgnd: 0.0129  P: 0.3333  R: 0.2308  F1: 0.2727
2025/06/23 12:35:31 - mmengine - INFO - Epoch(train)  [10][ 50/123]  lr: 2.0000e-02  eta: 0:20:31  time: 0.1195  data_time: 0.0915  memory: 149  loss: 0.4076  fgnd: 0.2456  bgnd: 0.0127  P: 0.8667  R: 0.5306  F1: 0.6582
2025/06/23 12:35:31 - mmengine - INFO - Epoch(train)  [10][ 60/123]  lr: 2.0000e-02  eta: 0:20:22  time: 0.0905  data_time: 0.0648  memory: 149  loss: 0.3566  fgnd: 0.2057  bgnd: 0.0128  P: 1.0000  R: 0.2308  F1: 0.3750
2025/06/23 12:35:32 - mmengine - INFO - Epoch(train)  [10][ 70/123]  lr: 2.0000e-02  eta: 0:20:25  time: 0.0853  data_time: 0.0619  memory: 149  loss: 0.3327  fgnd: 0.5963  bgnd: 0.0243  P: 0.7500  R: 0.0723  F1: 0.1319
2025/06/23 12:35:34 - mmengine - INFO - Epoch(train)  [10][ 80/123]  lr: 2.0000e-02  eta: 0:20:27  time: 0.0771  data_time: 0.0524  memory: 149  loss: 0.3724  fgnd: 0.4713  bgnd: 0.0244  P: 0.2812  R: 0.1023  F1: 0.1500
2025/06/23 12:35:36 - mmengine - INFO - Epoch(train)  [10][ 90/123]  lr: 2.0000e-02  eta: 0:20:33  time: 0.1062  data_time: 0.0811  memory: 149  loss: 0.4084  fgnd: 0.3225  bgnd: 0.0162  P: 0.8364  R: 0.5610  F1: 0.6715
2025/06/23 12:35:37 - mmengine - INFO - Epoch(train)  [10][100/123]  lr: 2.0000e-02  eta: 0:20:31  time: 0.1208  data_time: 0.0957  memory: 149  loss: 0.4109  fgnd: 0.2982  bgnd: 0.0125  P: 0.6628  R: 0.7600  F1: 0.7081
2025/06/23 12:35:37 - mmengine - INFO - Epoch(train)  [10][110/123]  lr: 2.0000e-02  eta: 0:20:23  time: 0.1245  data_time: 0.0993  memory: 149  loss: 0.4223  fgnd: 0.2611  bgnd: 0.0123  P: 0.6023  R: 0.7260  F1: 0.6584
2025/06/23 12:35:37 - mmengine - INFO - Epoch(train)  [10][120/123]  lr: 2.0000e-02  eta: 0:20:14  time: 0.0988  data_time: 0.0748  memory: 149  loss: 0.3864  fgnd: 0.2218  bgnd: 0.0120  P: 0.7015  R: 0.6812  F1: 0.6912
2025/06/23 12:35:37 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_123300
2025/06/23 12:35:37 - mmengine - INFO - Saving checkpoint at 10 epochs
2025/06/23 12:35:38 - mmengine - INFO - Epoch(val)  [10][10/17]    eta: 0:00:00  time: 0.0441  data_time: 0.0352  memory: 149  
2025/06/23 12:35:38 - mmengine - INFO - Epoch(val) [10][17/17]    P: 0.0278  R: 0.0732  F1: 0.0403  data_time: 0.0341  time: 0.0432
2025/06/23 12:35:40 - mmengine - INFO - Epoch(train)  [11][ 10/123]  lr: 2.0000e-02  eta: 0:20:15  time: 0.0913  data_time: 0.0690  memory: 149  loss: 0.3409  fgnd: 0.5159  bgnd: 0.0254  P: 0.7500  R: 0.3750  F1: 0.5000
2025/06/23 12:35:42 - mmengine - INFO - Epoch(train)  [11][ 20/123]  lr: 2.0000e-02  eta: 0:20:20  time: 0.0860  data_time: 0.0608  memory: 149  loss: 0.3853  fgnd: 0.5487  bgnd: 0.0280  P: 0.7083  R: 0.1491  F1: 0.2464
2025/06/23 12:35:43 - mmengine - INFO - Epoch(train)  [11][ 30/123]  lr: 2.0000e-02  eta: 0:20:25  time: 0.1173  data_time: 0.0901  memory: 149  loss: 0.4060  fgnd: 0.2537  bgnd: 0.0140  P: 0.6889  R: 0.5167  F1: 0.5905
2025/06/23 12:35:44 - mmengine - INFO - Epoch(train)  [11][ 40/123]  lr: 2.0000e-02  eta: 0:20:18  time: 0.1178  data_time: 0.0906  memory: 149  loss: 0.4047  fgnd: 0.2779  bgnd: 0.0127  P: 0.5500  R: 0.5789  F1: 0.5641
2025/06/23 12:35:44 - mmengine - INFO - Epoch(train)  [11][ 50/123]  lr: 2.0000e-02  eta: 0:20:10  time: 0.1185  data_time: 0.0909  memory: 149  loss: 0.4124  fgnd: 0.2391  bgnd: 0.0122  P: 0.7478  R: 0.8600  F1: 0.8000
2025/06/23 12:35:44 - mmengine - INFO - Epoch(train)  [11][ 60/123]  lr: 2.0000e-02  eta: 0:20:01  time: 0.0901  data_time: 0.0652  memory: 149  loss: 0.3671  fgnd: 0.2069  bgnd: 0.0116  P: 0.3879  R: 0.6716  F1: 0.4918
2025/06/23 12:35:46 - mmengine - INFO - Epoch(train)  [11][ 70/123]  lr: 2.0000e-02  eta: 0:20:04  time: 0.0861  data_time: 0.0632  memory: 149  loss: 0.3351  fgnd: 0.5361  bgnd: 0.0261  P: 0.8472  R: 0.4357  F1: 0.5755
2025/06/23 12:35:47 - mmengine - INFO - Epoch(train)  [11][ 80/123]  lr: 2.0000e-02  eta: 0:20:06  time: 0.0784  data_time: 0.0539  memory: 149  loss: 0.3803  fgnd: 0.5716  bgnd: 0.0289  P: 0.8148  R: 0.1849  F1: 0.3014
2025/06/23 12:35:49 - mmengine - INFO - Epoch(train)  [11][ 90/123]  lr: 2.0000e-02  eta: 0:20:11  time: 0.1072  data_time: 0.0794  memory: 149  loss: 0.4156  fgnd: 0.4225  bgnd: 0.0203  P: 0.6579  R: 0.2976  F1: 0.4098
2025/06/23 12:35:50 - mmengine - INFO - Epoch(train)  [11][100/123]  lr: 2.0000e-02  eta: 0:20:09  time: 0.1221  data_time: 0.0943  memory: 149  loss: 0.4191  fgnd: 0.3167  bgnd: 0.0132  P: 0.5714  R: 0.7059  F1: 0.6316
2025/06/23 12:35:50 - mmengine - INFO - Epoch(train)  [11][110/123]  lr: 2.0000e-02  eta: 0:20:01  time: 0.1225  data_time: 0.0946  memory: 149  loss: 0.4333  fgnd: 0.3095  bgnd: 0.0134  P: 0.5798  R: 0.7931  F1: 0.6699
2025/06/23 12:35:51 - mmengine - INFO - Epoch(train)  [11][120/123]  lr: 2.0000e-02  eta: 0:19:53  time: 0.0965  data_time: 0.0707  memory: 149  loss: 0.4042  fgnd: 0.3155  bgnd: 0.0140  P: 0.5385  R: 0.6462  F1: 0.5874
2025/06/23 12:35:51 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_123300
2025/06/23 12:35:51 - mmengine - INFO - Saving checkpoint at 11 epochs
2025/06/23 12:35:51 - mmengine - INFO - Epoch(val)  [11][10/17]    eta: 0:00:00  time: 0.0438  data_time: 0.0342  memory: 149  
2025/06/23 12:35:52 - mmengine - INFO - Epoch(val) [11][17/17]    P: 0.0661  R: 0.2695  F1: 0.1062  data_time: 0.0343  time: 0.0446
2025/06/23 12:35:53 - mmengine - INFO - Epoch(train)  [12][ 10/123]  lr: 2.0000e-02  eta: 0:19:54  time: 0.0883  data_time: 0.0653  memory: 149  loss: 0.3452  fgnd: 0.5277  bgnd: 0.0239  P: 0.7188  R: 0.2447  F1: 0.3651
2025/06/23 12:35:55 - mmengine - INFO - Epoch(train)  [12][ 20/123]  lr: 2.0000e-02  eta: 0:19:58  time: 0.0832  data_time: 0.0588  memory: 149  loss: 0.3782  fgnd: 0.4289  bgnd: 0.0243  P: 0.7000  R: 0.1489  F1: 0.2456
2025/06/23 12:35:58 - mmengine - INFO - Epoch(train)  [12][ 30/123]  lr: 2.0000e-02  eta: 0:20:10  time: 0.1328  data_time: 0.0878  memory: 149  loss: 0.3882  fgnd: 0.3084  bgnd: 0.0157  P: 0.8043  R: 0.4868  F1: 0.6066
2025/06/23 12:35:58 - mmengine - INFO - Epoch(train)  [12][ 40/123]  lr: 2.0000e-02  eta: 0:20:04  time: 0.1370  data_time: 0.0913  memory: 149  loss: 0.3810  fgnd: 0.2624  bgnd: 0.0122  P: 0.7179  R: 0.8660  F1: 0.7850
2025/06/23 12:35:59 - mmengine - INFO - Epoch(train)  [12][ 50/123]  lr: 2.0000e-02  eta: 0:19:56  time: 0.1376  data_time: 0.0914  memory: 149  loss: 0.3817  fgnd: 0.2292  bgnd: 0.0120  P: 0.5685  R: 0.8557  F1: 0.6831
2025/06/23 12:35:59 - mmengine - INFO - Epoch(train)  [12][ 60/123]  lr: 2.0000e-02  eta: 0:19:49  time: 0.1099  data_time: 0.0654  memory: 149  loss: 0.3416  fgnd: 0.1783  bgnd: 0.0112  P: 0.6755  R: 0.9107  F1: 0.7757
2025/06/23 12:36:00 - mmengine - INFO - Epoch(train)  [12][ 70/123]  lr: 2.0000e-02  eta: 0:19:52  time: 0.1076  data_time: 0.0650  memory: 149  loss: 0.3134  fgnd: 0.5639  bgnd: 0.0246  P: 0.7467  R: 0.4308  F1: 0.5463
2025/06/23 12:36:02 - mmengine - INFO - Epoch(train)  [12][ 80/123]  lr: 2.0000e-02  eta: 0:19:54  time: 0.0831  data_time: 0.0565  memory: 149  loss: 0.3549  fgnd: 0.3920  bgnd: 0.0225  P: 0.8824  R: 0.1685  F1: 0.2830
2025/06/23 12:36:04 - mmengine - INFO - Epoch(train)  [12][ 90/123]  lr: 2.0000e-02  eta: 0:19:59  time: 0.1100  data_time: 0.0821  memory: 149  loss: 0.3928  fgnd: 0.4086  bgnd: 0.0152  P: 0.9000  R: 0.3103  F1: 0.4615
2025/06/23 12:36:05 - mmengine - INFO - Epoch(train)  [12][100/123]  lr: 2.0000e-02  eta: 0:19:56  time: 0.1245  data_time: 0.0956  memory: 149  loss: 0.3999  fgnd: 0.3005  bgnd: 0.0125  P: 0.7059  R: 0.8090  F1: 0.7539
2025/06/23 12:36:05 - mmengine - INFO - Epoch(train)  [12][110/123]  lr: 2.0000e-02  eta: 0:19:49  time: 0.1244  data_time: 0.0959  memory: 149  loss: 0.4115  fgnd: 0.3044  bgnd: 0.0128  P: 0.5769  R: 0.8750  F1: 0.6954
2025/06/23 12:36:05 - mmengine - INFO - Epoch(train)  [12][120/123]  lr: 2.0000e-02  eta: 0:19:41  time: 0.0968  data_time: 0.0701  memory: 149  loss: 0.3844  fgnd: 0.2773  bgnd: 0.0130  P: 0.5188  R: 0.8300  F1: 0.6385
2025/06/23 12:36:05 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_123300
2025/06/23 12:36:05 - mmengine - INFO - Saving checkpoint at 12 epochs
2025/06/23 12:36:06 - mmengine - INFO - Epoch(val)  [12][10/17]    eta: 0:00:00  time: 0.0442  data_time: 0.0340  memory: 149  
2025/06/23 12:36:06 - mmengine - INFO - Epoch(val) [12][17/17]    P: 0.0149  R: 0.0667  F1: 0.0243  data_time: 0.0334  time: 0.0438
2025/06/23 12:36:08 - mmengine - INFO - Epoch(train)  [13][ 10/123]  lr: 2.0000e-02  eta: 0:19:42  time: 0.0882  data_time: 0.0642  memory: 149  loss: 0.3473  fgnd: 0.5953  bgnd: 0.0283  P: 0.7083  R: 0.3643  F1: 0.4811
2025/06/23 12:36:10 - mmengine - INFO - Epoch(train)  [13][ 20/123]  lr: 2.0000e-02  eta: 0:19:46  time: 0.0838  data_time: 0.0598  memory: 149  loss: 0.3767  fgnd: 0.5403  bgnd: 0.0261  P: 0.8182  R: 0.0968  F1: 0.1731
2025/06/23 12:36:12 - mmengine - INFO - Epoch(train)  [13][ 30/123]  lr: 2.0000e-02  eta: 0:19:51  time: 0.1165  data_time: 0.0897  memory: 149  loss: 0.4009  fgnd: 0.3437  bgnd: 0.0147  P: 0.5926  R: 0.3137  F1: 0.4103
2025/06/23 12:36:12 - mmengine - INFO - Epoch(train)  [13][ 40/123]  lr: 2.0000e-02  eta: 0:19:45  time: 0.1197  data_time: 0.0930  memory: 149  loss: 0.4027  fgnd: 0.3072  bgnd: 0.0124  P: 0.6829  R: 0.7467  F1: 0.7134
2025/06/23 12:36:12 - mmengine - INFO - Epoch(train)  [13][ 50/123]  lr: 2.0000e-02  eta: 0:19:38  time: 0.1203  data_time: 0.0935  memory: 149  loss: 0.4078  fgnd: 0.2476  bgnd: 0.0121  P: 0.7285  R: 0.9016  F1: 0.8059
2025/06/23 12:36:13 - mmengine - INFO - Epoch(train)  [13][ 60/123]  lr: 2.0000e-02  eta: 0:19:30  time: 0.0910  data_time: 0.0671  memory: 149  loss: 0.3585  fgnd: 0.2019  bgnd: 0.0114  P: 0.6425  R: 0.9500  F1: 0.7666
2025/06/23 12:36:14 - mmengine - INFO - Epoch(train)  [13][ 70/123]  lr: 2.0000e-02  eta: 0:19:32  time: 0.0866  data_time: 0.0639  memory: 149  loss: 0.3403  fgnd: 0.7992  bgnd: 0.0303  P: 0.7130  R: 0.4667  F1: 0.5641
2025/06/23 12:36:15 - mmengine - INFO - Epoch(train)  [13][ 80/123]  lr: 2.0000e-02  eta: 0:19:34  time: 0.0762  data_time: 0.0533  memory: 149  loss: 0.3586  fgnd: 0.5347  bgnd: 0.0267  P: 0.7368  R: 0.1284  F1: 0.2188
2025/06/23 12:36:17 - mmengine - INFO - Epoch(train)  [13][ 90/123]  lr: 2.0000e-02  eta: 0:19:38  time: 0.1057  data_time: 0.0806  memory: 149  loss: 0.3828  fgnd: 0.3358  bgnd: 0.0140  P: 0.8889  R: 0.1818  F1: 0.3019
2025/06/23 12:36:18 - mmengine - INFO - Epoch(train)  [13][100/123]  lr: 2.0000e-02  eta: 0:19:36  time: 0.1202  data_time: 0.0944  memory: 149  loss: 0.3846  fgnd: 0.3010  bgnd: 0.0123  P: 0.7727  R: 0.6296  F1: 0.6939
2025/06/23 12:36:19 - mmengine - INFO - Epoch(train)  [13][110/123]  lr: 2.0000e-02  eta: 0:19:29  time: 0.1206  data_time: 0.0947  memory: 149  loss: 0.3928  fgnd: 0.2817  bgnd: 0.0124  P: 0.5431  R: 0.7683  F1: 0.6364
2025/06/23 12:36:19 - mmengine - INFO - Epoch(train)  [13][120/123]  lr: 2.0000e-02  eta: 0:19:22  time: 0.0949  data_time: 0.0706  memory: 149  loss: 0.3561  fgnd: 0.2069  bgnd: 0.0117  P: 0.4694  R: 0.8023  F1: 0.5923
2025/06/23 12:36:19 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_123300
2025/06/23 12:36:19 - mmengine - INFO - Saving checkpoint at 13 epochs
2025/06/23 12:36:20 - mmengine - INFO - Epoch(val)  [13][10/17]    eta: 0:00:00  time: 0.0445  data_time: 0.0333  memory: 149  
2025/06/23 12:36:20 - mmengine - INFO - Epoch(val) [13][17/17]    P: 0.0265  R: 0.1767  F1: 0.0461  data_time: 0.0319  time: 0.0440
2025/06/23 12:36:22 - mmengine - INFO - Epoch(train)  [14][ 10/123]  lr: 2.0000e-02  eta: 0:19:23  time: 0.0888  data_time: 0.0658  memory: 149  loss: 0.3238  fgnd: 0.4362  bgnd: 0.0221  P: 0.7160  R: 0.4793  F1: 0.5743
2025/06/23 12:36:23 - mmengine - INFO - Epoch(train)  [14][ 20/123]  lr: 2.0000e-02  eta: 0:19:26  time: 0.0838  data_time: 0.0593  memory: 149  loss: 0.3646  fgnd: 0.5509  bgnd: 0.0294  P: 0.4667  R: 0.1239  F1: 0.1958
2025/06/23 12:36:25 - mmengine - INFO - Epoch(train)  [14][ 30/123]  lr: 2.0000e-02  eta: 0:19:30  time: 0.1149  data_time: 0.0876  memory: 149  loss: 0.3832  fgnd: 0.2680  bgnd: 0.0132  P: 0.7895  R: 0.3125  F1: 0.4478
2025/06/23 12:36:26 - mmengine - INFO - Epoch(train)  [14][ 40/123]  lr: 2.0000e-02  eta: 0:19:24  time: 0.1181  data_time: 0.0911  memory: 149  loss: 0.3852  fgnd: 0.2755  bgnd: 0.0121  P: 0.8333  R: 0.6452  F1: 0.7273
2025/06/23 12:36:26 - mmengine - INFO - Epoch(train)  [14][ 50/123]  lr: 2.0000e-02  eta: 0:19:18  time: 0.1184  data_time: 0.0911  memory: 149  loss: 0.3956  fgnd: 0.2576  bgnd: 0.0119  P: 0.6429  R: 0.8761  F1: 0.7416
2025/06/23 12:36:26 - mmengine - INFO - Epoch(train)  [14][ 60/123]  lr: 2.0000e-02  eta: 0:19:11  time: 0.0896  data_time: 0.0647  memory: 149  loss: 0.3558  fgnd: 0.2184  bgnd: 0.0116  P: 0.6270  R: 0.9062  F1: 0.7412
2025/06/23 12:36:28 - mmengine - INFO - Epoch(train)  [14][ 70/123]  lr: 2.0000e-02  eta: 0:19:13  time: 0.0848  data_time: 0.0612  memory: 149  loss: 0.3254  fgnd: 0.5580  bgnd: 0.0275  P: 0.6355  R: 0.4416  F1: 0.5211
2025/06/23 12:36:29 - mmengine - INFO - Epoch(train)  [14][ 80/123]  lr: 2.0000e-02  eta: 0:19:14  time: 0.0775  data_time: 0.0536  memory: 149  loss: 0.3488  fgnd: 0.4941  bgnd: 0.0274  P: 0.7000  R: 0.3603  F1: 0.4757
2025/06/23 12:36:31 - mmengine - INFO - Epoch(train)  [14][ 90/123]  lr: 2.0000e-02  eta: 0:19:18  time: 0.1059  data_time: 0.0784  memory: 149  loss: 0.3684  fgnd: 0.3122  bgnd: 0.0142  P: 0.8065  R: 0.4237  F1: 0.5556
2025/06/23 12:36:32 - mmengine - INFO - Epoch(train)  [14][100/123]  lr: 2.0000e-02  eta: 0:19:16  time: 0.1205  data_time: 0.0931  memory: 149  loss: 0.3682  fgnd: 0.2757  bgnd: 0.0118  P: 0.8000  R: 0.4000  F1: 0.5333
2025/06/23 12:36:32 - mmengine - INFO - Epoch(train)  [14][110/123]  lr: 2.0000e-02  eta: 0:19:10  time: 0.1207  data_time: 0.0936  memory: 149  loss: 0.3736  fgnd: 0.2535  bgnd: 0.0125  P: 0.6515  R: 0.6418  F1: 0.6466
2025/06/23 12:36:32 - mmengine - INFO - Epoch(train)  [14][120/123]  lr: 2.0000e-02  eta: 0:19:03  time: 0.0948  data_time: 0.0697  memory: 149  loss: 0.3413  fgnd: 0.2127  bgnd: 0.0120  P: 0.5897  R: 0.7841  F1: 0.6732
2025/06/23 12:36:32 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_123300
2025/06/23 12:36:32 - mmengine - INFO - Saving checkpoint at 14 epochs
2025/06/23 12:36:33 - mmengine - INFO - Epoch(val)  [14][10/17]    eta: 0:00:00  time: 0.0441  data_time: 0.0325  memory: 149  
2025/06/23 12:36:33 - mmengine - INFO - Epoch(val) [14][17/17]    P: 0.0394  R: 0.2369  F1: 0.0675  data_time: 0.0314  time: 0.0432
2025/06/23 12:36:35 - mmengine - INFO - Epoch(train)  [15][ 10/123]  lr: 2.0000e-02  eta: 0:19:04  time: 0.0874  data_time: 0.0641  memory: 149  loss: 0.3151  fgnd: 0.6096  bgnd: 0.0280  P: 0.7647  R: 0.4452  F1: 0.5628
2025/06/23 12:36:37 - mmengine - INFO - Epoch(train)  [15][ 20/123]  lr: 2.0000e-02  eta: 0:19:07  time: 0.0835  data_time: 0.0590  memory: 149  loss: 0.3492  fgnd: 0.5585  bgnd: 0.0310  P: 0.6977  R: 0.2326  F1: 0.3488
2025/06/23 12:36:39 - mmengine - INFO - Epoch(train)  [15][ 30/123]  lr: 2.0000e-02  eta: 0:19:10  time: 0.1136  data_time: 0.0869  memory: 149  loss: 0.3642  fgnd: 0.3143  bgnd: 0.0138  P: 0.5952  R: 0.4310  F1: 0.5000
2025/06/23 12:36:39 - mmengine - INFO - Epoch(train)  [15][ 40/123]  lr: 2.0000e-02  eta: 0:19:05  time: 0.1168  data_time: 0.0899  memory: 149  loss: 0.3685  fgnd: 0.2626  bgnd: 0.0115  P: 0.7250  R: 0.8056  F1: 0.7632
2025/06/23 12:36:39 - mmengine - INFO - Epoch(train)  [15][ 50/123]  lr: 2.0000e-02  eta: 0:18:59  time: 0.1173  data_time: 0.0902  memory: 149  loss: 0.3797  fgnd: 0.2546  bgnd: 0.0124  P: 0.6180  R: 0.7051  F1: 0.6587
2025/06/23 12:36:39 - mmengine - INFO - Epoch(train)  [15][ 60/123]  lr: 2.0000e-02  eta: 0:18:53  time: 0.0889  data_time: 0.0646  memory: 149  loss: 0.3368  fgnd: 0.2022  bgnd: 0.0116  P: 0.6565  R: 0.8866  F1: 0.7544
2025/06/23 12:36:41 - mmengine - INFO - Epoch(train)  [15][ 70/123]  lr: 2.0000e-02  eta: 0:18:54  time: 0.0839  data_time: 0.0611  memory: 149  loss: 0.3225  fgnd: 0.6043  bgnd: 0.0296  P: 0.6696  R: 0.4747  F1: 0.5556
2025/06/23 12:36:42 - mmengine - INFO - Epoch(train)  [15][ 80/123]  lr: 2.0000e-02  eta: 0:18:55  time: 0.0771  data_time: 0.0530  memory: 149  loss: 0.3565  fgnd: 0.4206  bgnd: 0.0234  P: 0.7797  R: 0.3932  F1: 0.5227
2025/06/23 12:36:44 - mmengine - INFO - Epoch(train)  [15][ 90/123]  lr: 2.0000e-02  eta: 0:18:59  time: 0.1060  data_time: 0.0790  memory: 149  loss: 0.3800  fgnd: 0.2927  bgnd: 0.0128  P: 0.4348  R: 0.3774  F1: 0.4040
2025/06/23 12:36:45 - mmengine - INFO - Epoch(train)  [15][100/123]  lr: 2.0000e-02  eta: 0:18:57  time: 0.1206  data_time: 0.0937  memory: 149  loss: 0.3811  fgnd: 0.2888  bgnd: 0.0123  P: 0.8125  R: 0.6500  F1: 0.7222
2025/06/23 12:36:46 - mmengine - INFO - Epoch(train)  [15][110/123]  lr: 2.0000e-02  eta: 0:18:51  time: 0.1209  data_time: 0.0940  memory: 149  loss: 0.3924  fgnd: 0.2585  bgnd: 0.0120  P: 0.4578  R: 0.6333  F1: 0.5315
2025/06/23 12:36:46 - mmengine - INFO - Epoch(train)  [15][120/123]  lr: 2.0000e-02  eta: 0:18:45  time: 0.0952  data_time: 0.0702  memory: 149  loss: 0.3548  fgnd: 0.2266  bgnd: 0.0120  P: 0.3596  R: 0.5517  F1: 0.4354
2025/06/23 12:36:46 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_123300
2025/06/23 12:36:46 - mmengine - INFO - Saving checkpoint at 15 epochs
2025/06/23 12:36:47 - mmengine - INFO - Epoch(val)  [15][10/17]    eta: 0:00:00  time: 0.0439  data_time: 0.0315  memory: 149  
2025/06/23 12:36:47 - mmengine - INFO - Epoch(val) [15][17/17]    P: 0.0210  R: 0.1617  F1: 0.0372  data_time: 0.0314  time: 0.0438
2025/06/23 12:36:48 - mmengine - INFO - Epoch(train)  [16][ 10/123]  lr: 2.0000e-02  eta: 0:18:45  time: 0.0877  data_time: 0.0650  memory: 149  loss: 0.3429  fgnd: 0.7031  bgnd: 0.0292  P: 0.6277  R: 0.4069  F1: 0.4937
2025/06/23 12:36:50 - mmengine - INFO - Epoch(train)  [16][ 20/123]  lr: 2.0000e-02  eta: 0:18:48  time: 0.0837  data_time: 0.0583  memory: 149  loss: 0.3856  fgnd: 0.5436  bgnd: 0.0266  P: 0.7857  R: 0.4198  F1: 0.5473
2025/06/23 12:36:52 - mmengine - INFO - Epoch(train)  [16][ 30/123]  lr: 2.0000e-02  eta: 0:18:51  time: 0.1145  data_time: 0.0870  memory: 149  loss: 0.4049  fgnd: 0.3442  bgnd: 0.0150  P: 0.6290  R: 0.5417  F1: 0.5821
2025/06/23 12:36:52 - mmengine - INFO - Epoch(train)  [16][ 40/123]  lr: 2.0000e-02  eta: 0:18:47  time: 0.1180  data_time: 0.0902  memory: 149  loss: 0.4175  fgnd: 0.3210  bgnd: 0.0126  P: 0.6066  R: 0.6271  F1: 0.6167
2025/06/23 12:36:53 - mmengine - INFO - Epoch(train)  [16][ 50/123]  lr: 2.0000e-02  eta: 0:18:41  time: 0.1184  data_time: 0.0906  memory: 149  loss: 0.4328  fgnd: 0.2715  bgnd: 0.0121  P: 0.6906  R: 0.8727  F1: 0.7711
2025/06/23 12:36:53 - mmengine - INFO - Epoch(train)  [16][ 60/123]  lr: 2.0000e-02  eta: 0:18:35  time: 0.0900  data_time: 0.0646  memory: 149  loss: 0.3774  fgnd: 0.2470  bgnd: 0.0117  P: 0.7384  R: 0.9338  F1: 0.8247
2025/06/23 12:36:54 - mmengine - INFO - Epoch(train)  [16][ 70/123]  lr: 2.0000e-02  eta: 0:18:36  time: 0.0848  data_time: 0.0619  memory: 149  loss: 0.3407  fgnd: 0.5005  bgnd: 0.0262  P: 0.7466  R: 0.6022  F1: 0.6667
2025/06/23 12:36:56 - mmengine - INFO - Epoch(train)  [16][ 80/123]  lr: 2.0000e-02  eta: 0:18:37  time: 0.0766  data_time: 0.0528  memory: 149  loss: 0.3646  fgnd: 0.5120  bgnd: 0.0257  P: 0.7538  R: 0.3630  F1: 0.4900
2025/06/23 12:36:58 - mmengine - INFO - Epoch(train)  [16][ 90/123]  lr: 2.0000e-02  eta: 0:18:40  time: 0.1055  data_time: 0.0787  memory: 149  loss: 0.3808  fgnd: 0.2945  bgnd: 0.0151  P: 0.7805  R: 0.4848  F1: 0.5981
2025/06/23 12:36:59 - mmengine - INFO - Epoch(train)  [16][100/123]  lr: 2.0000e-02  eta: 0:18:39  time: 0.1198  data_time: 0.0929  memory: 149  loss: 0.3725  fgnd: 0.2628  bgnd: 0.0119  P: 0.8367  R: 0.6308  F1: 0.7193
2025/06/23 12:36:59 - mmengine - INFO - Epoch(train)  [16][110/123]  lr: 2.0000e-02  eta: 0:18:33  time: 0.1202  data_time: 0.0933  memory: 149  loss: 0.3724  fgnd: 0.2650  bgnd: 0.0118  P: 0.6484  R: 0.7867  F1: 0.7108
2025/06/23 12:36:59 - mmengine - INFO - Epoch(train)  [16][120/123]  lr: 2.0000e-02  eta: 0:18:28  time: 0.0947  data_time: 0.0698  memory: 149  loss: 0.3450  fgnd: 0.2079  bgnd: 0.0121  P: 0.6408  R: 0.7952  F1: 0.7097
2025/06/23 12:36:59 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_123300
2025/06/23 12:36:59 - mmengine - INFO - Saving checkpoint at 16 epochs
2025/06/23 12:37:00 - mmengine - INFO - Epoch(val)  [16][10/17]    eta: 0:00:00  time: 0.0438  data_time: 0.0317  memory: 149  
2025/06/23 12:37:00 - mmengine - INFO - Epoch(val) [16][17/17]    P: 0.0338  R: 0.2100  F1: 0.0583  data_time: 0.0333  time: 0.0439
2025/06/23 12:37:02 - mmengine - INFO - Epoch(train)  [17][ 10/123]  lr: 2.0000e-02  eta: 0:18:28  time: 0.0883  data_time: 0.0653  memory: 149  loss: 0.3159  fgnd: 0.5720  bgnd: 0.0257  P: 0.8082  R: 0.4683  F1: 0.5930
2025/06/23 12:37:04 - mmengine - INFO - Epoch(train)  [17][ 20/123]  lr: 2.0000e-02  eta: 0:18:31  time: 0.0840  data_time: 0.0586  memory: 149  loss: 0.3583  fgnd: 0.4496  bgnd: 0.0281  P: 0.6949  R: 0.3130  F1: 0.4316
2025/06/23 12:37:06 - mmengine - INFO - Epoch(train)  [17][ 30/123]  lr: 2.0000e-02  eta: 0:18:33  time: 0.1146  data_time: 0.0872  memory: 149  loss: 0.3799  fgnd: 0.2628  bgnd: 0.0147  P: 0.7765  R: 0.6804  F1: 0.7253
2025/06/23 12:37:06 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_123300
2025/06/23 12:37:06 - mmengine - INFO - Epoch(train)  [17][ 40/123]  lr: 2.0000e-02  eta: 0:18:29  time: 0.1178  data_time: 0.0904  memory: 149  loss: 0.3847  fgnd: 0.3134  bgnd: 0.0121  P: 0.7077  R: 0.6765  F1: 0.6917
2025/06/23 12:37:06 - mmengine - INFO - Epoch(train)  [17][ 50/123]  lr: 2.0000e-02  eta: 0:18:23  time: 0.1181  data_time: 0.0904  memory: 149  loss: 0.3971  fgnd: 0.2540  bgnd: 0.0115  P: 0.7339  R: 0.8421  F1: 0.7843
2025/06/23 12:37:06 - mmengine - INFO - Epoch(train)  [17][ 60/123]  lr: 2.0000e-02  eta: 0:18:18  time: 0.0894  data_time: 0.0641  memory: 149  loss: 0.3543  fgnd: 0.2254  bgnd: 0.0111  P: 0.6905  R: 0.8529  F1: 0.7632
2025/06/23 12:37:08 - mmengine - INFO - Epoch(train)  [17][ 70/123]  lr: 2.0000e-02  eta: 0:18:19  time: 0.0849  data_time: 0.0622  memory: 149  loss: 0.3236  fgnd: 0.5941  bgnd: 0.0270  P: 0.7938  R: 0.5033  F1: 0.6160
2025/06/23 12:37:09 - mmengine - INFO - Epoch(train)  [17][ 80/123]  lr: 2.0000e-02  eta: 0:18:20  time: 0.0770  data_time: 0.0528  memory: 149  loss: 0.3486  fgnd: 0.4729  bgnd: 0.0246  P: 0.6795  R: 0.4109  F1: 0.5121
2025/06/23 12:37:11 - mmengine - INFO - Epoch(train)  [17][ 90/123]  lr: 2.0000e-02  eta: 0:18:23  time: 0.1052  data_time: 0.0776  memory: 149  loss: 0.3706  fgnd: 0.3483  bgnd: 0.0148  P: 0.7200  R: 0.6353  F1: 0.6750
2025/06/23 12:37:12 - mmengine - INFO - Epoch(train)  [17][100/123]  lr: 2.0000e-02  eta: 0:18:21  time: 0.1202  data_time: 0.0927  memory: 149  loss: 0.3623  fgnd: 0.2779  bgnd: 0.0114  P: 0.7692  R: 0.7246  F1: 0.7463
2025/06/23 12:37:12 - mmengine - INFO - Epoch(train)  [17][110/123]  lr: 2.0000e-02  eta: 0:18:16  time: 0.1204  data_time: 0.0930  memory: 149  loss: 0.3650  fgnd: 0.2731  bgnd: 0.0119  P: 0.6056  R: 0.6515  F1: 0.6277
2025/06/23 12:37:13 - mmengine - INFO - Epoch(train)  [17][120/123]  lr: 2.0000e-02  eta: 0:18:11  time: 0.0942  data_time: 0.0688  memory: 149  loss: 0.3326  fgnd: 0.2145  bgnd: 0.0111  P: 0.4880  R: 0.7625  F1: 0.5951
2025/06/23 12:37:13 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_123300
2025/06/23 12:37:13 - mmengine - INFO - Saving checkpoint at 17 epochs
2025/06/23 12:37:13 - mmengine - INFO - Epoch(val)  [17][10/17]    eta: 0:00:00  time: 0.0441  data_time: 0.0319  memory: 149  
2025/06/23 12:37:14 - mmengine - INFO - Epoch(val) [17][17/17]    P: 0.0334  R: 0.1844  F1: 0.0566  data_time: 0.0320  time: 0.0446
2025/06/23 12:37:15 - mmengine - INFO - Epoch(train)  [18][ 10/123]  lr: 2.0000e-02  eta: 0:18:11  time: 0.0880  data_time: 0.0651  memory: 149  loss: 0.3048  fgnd: 0.5537  bgnd: 0.0240  P: 0.6735  R: 0.5077  F1: 0.5789
2025/06/23 12:37:17 - mmengine - INFO - Epoch(train)  [18][ 20/123]  lr: 2.0000e-02  eta: 0:18:13  time: 0.0839  data_time: 0.0602  memory: 149  loss: 0.3362  fgnd: 0.5205  bgnd: 0.0286  P: 0.6909  R: 0.2946  F1: 0.4130
2025/06/23 12:37:19 - mmengine - INFO - Epoch(train)  [18][ 30/123]  lr: 2.0000e-02  eta: 0:18:16  time: 0.1141  data_time: 0.0874  memory: 149  loss: 0.3551  fgnd: 0.2936  bgnd: 0.0149  P: 0.7885  R: 0.5541  F1: 0.6508
2025/06/23 12:37:19 - mmengine - INFO - Epoch(train)  [18][ 40/123]  lr: 2.0000e-02  eta: 0:18:12  time: 0.1178  data_time: 0.0912  memory: 149  loss: 0.3566  fgnd: 0.2615  bgnd: 0.0117  P: 0.7160  R: 0.7733  F1: 0.7436
2025/06/23 12:37:20 - mmengine - INFO - Epoch(train)  [18][ 50/123]  lr: 2.0000e-02  eta: 0:18:07  time: 0.1184  data_time: 0.0915  memory: 149  loss: 0.3660  fgnd: 0.2365  bgnd: 0.0115  P: 0.6893  R: 0.8765  F1: 0.7717
2025/06/23 12:37:20 - mmengine - INFO - Epoch(train)  [18][ 60/123]  lr: 2.0000e-02  eta: 0:18:02  time: 0.0896  data_time: 0.0647  memory: 149  loss: 0.3232  fgnd: 0.1774  bgnd: 0.0109  P: 0.8246  R: 0.9126  F1: 0.8664
2025/06/23 12:37:21 - mmengine - INFO - Epoch(train)  [18][ 70/123]  lr: 2.0000e-02  eta: 0:18:03  time: 0.0847  data_time: 0.0610  memory: 149  loss: 0.2980  fgnd: 0.4568  bgnd: 0.0262  P: 0.8056  R: 0.4113  F1: 0.5446
2025/06/23 12:37:23 - mmengine - INFO - Epoch(train)  [18][ 80/123]  lr: 2.0000e-02  eta: 0:18:03  time: 0.0775  data_time: 0.0534  memory: 149  loss: 0.3281  fgnd: 0.4375  bgnd: 0.0249  P: 0.7816  R: 0.4789  F1: 0.5939
2025/06/23 12:37:25 - mmengine - INFO - Epoch(train)  [18][ 90/123]  lr: 2.0000e-02  eta: 0:18:06  time: 0.1055  data_time: 0.0784  memory: 149  loss: 0.3448  fgnd: 0.2673  bgnd: 0.0147  P: 0.7342  R: 0.6304  F1: 0.6784
2025/06/23 12:37:26 - mmengine - INFO - Epoch(train)  [18][100/123]  lr: 2.0000e-02  eta: 0:18:04  time: 0.1199  data_time: 0.0928  memory: 149  loss: 0.3415  fgnd: 0.2708  bgnd: 0.0110  P: 0.7375  R: 0.7662  F1: 0.7516
2025/06/23 12:37:26 - mmengine - INFO - Epoch(train)  [18][110/123]  lr: 2.0000e-02  eta: 0:17:59  time: 0.1203  data_time: 0.0935  memory: 149  loss: 0.3470  fgnd: 0.2214  bgnd: 0.0110  P: 0.7374  R: 0.8588  F1: 0.7935
2025/06/23 12:37:26 - mmengine - INFO - Epoch(train)  [18][120/123]  lr: 2.0000e-02  eta: 0:17:54  time: 0.0946  data_time: 0.0699  memory: 149  loss: 0.3218  fgnd: 0.2039  bgnd: 0.0116  P: 0.0833  R: 0.2250  F1: 0.1216
2025/06/23 12:37:26 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_123300
2025/06/23 12:37:26 - mmengine - INFO - Saving checkpoint at 18 epochs
2025/06/23 12:37:27 - mmengine - INFO - Epoch(val)  [18][10/17]    eta: 0:00:00  time: 0.0446  data_time: 0.0330  memory: 149  
2025/06/23 12:37:27 - mmengine - INFO - Epoch(val) [18][17/17]    P: 0.0410  R: 0.1504  F1: 0.0645  data_time: 0.0343  time: 0.0442
2025/06/23 12:37:29 - mmengine - INFO - Epoch(train)  [19][ 10/123]  lr: 2.0000e-02  eta: 0:17:55  time: 0.0885  data_time: 0.0655  memory: 149  loss: 0.2882  fgnd: 0.4212  bgnd: 0.0240  P: 0.8043  R: 0.5481  F1: 0.6520
2025/06/23 12:37:31 - mmengine - INFO - Epoch(train)  [19][ 20/123]  lr: 2.0000e-02  eta: 0:17:57  time: 0.0846  data_time: 0.0603  memory: 149  loss: 0.3361  fgnd: 0.4745  bgnd: 0.0296  P: 0.7895  R: 0.3947  F1: 0.5263
2025/06/23 12:37:32 - mmengine - INFO - Epoch(train)  [19][ 30/123]  lr: 2.0000e-02  eta: 0:17:59  time: 0.1152  data_time: 0.0886  memory: 149  loss: 0.3535  fgnd: 0.2396  bgnd: 0.0112  P: 0.7232  R: 0.8804  F1: 0.7941
2025/06/23 12:37:33 - mmengine - INFO - Epoch(train)  [19][ 40/123]  lr: 2.0000e-02  eta: 0:17:55  time: 0.1187  data_time: 0.0918  memory: 149  loss: 0.3574  fgnd: 0.2737  bgnd: 0.0112  P: 0.6915  R: 0.7831  F1: 0.7345
2025/06/23 12:37:33 - mmengine - INFO - Epoch(train)  [19][ 50/123]  lr: 2.0000e-02  eta: 0:17:50  time: 0.1191  data_time: 0.0920  memory: 149  loss: 0.3720  fgnd: 0.3004  bgnd: 0.0120  P: 0.5938  R: 0.8172  F1: 0.6878
2025/06/23 12:37:33 - mmengine - INFO - Epoch(train)  [19][ 60/123]  lr: 2.0000e-02  eta: 0:17:46  time: 0.0901  data_time: 0.0654  memory: 149  loss: 0.3430  fgnd: 0.2034  bgnd: 0.0117  P: 0.6014  R: 0.8350  F1: 0.6992
2025/06/23 12:37:35 - mmengine - INFO - Epoch(train)  [19][ 70/123]  lr: 2.0000e-02  eta: 0:17:47  time: 0.0852  data_time: 0.0625  memory: 149  loss: 0.3114  fgnd: 0.4271  bgnd: 0.0229  P: 0.8696  R: 0.4478  F1: 0.5911
2025/06/23 12:37:36 - mmengine - INFO - Epoch(train)  [19][ 80/123]  lr: 2.0000e-02  eta: 0:17:47  time: 0.0775  data_time: 0.0537  memory: 149  loss: 0.3505  fgnd: 0.4774  bgnd: 0.0276  P: 0.8000  R: 0.3862  F1: 0.5209
2025/06/23 12:37:38 - mmengine - INFO - Epoch(train)  [19][ 90/123]  lr: 2.0000e-02  eta: 0:17:50  time: 0.1057  data_time: 0.0793  memory: 149  loss: 0.3678  fgnd: 0.3249  bgnd: 0.0158  P: 0.7500  R: 0.6667  F1: 0.7059
2025/06/23 12:37:39 - mmengine - INFO - Epoch(train)  [19][100/123]  lr: 2.0000e-02  eta: 0:17:48  time: 0.1201  data_time: 0.0938  memory: 149  loss: 0.3615  fgnd: 0.2570  bgnd: 0.0114  P: 0.6923  R: 0.6207  F1: 0.6545
2025/06/23 12:37:39 - mmengine - INFO - Epoch(train)  [19][110/123]  lr: 2.0000e-02  eta: 0:17:43  time: 0.1203  data_time: 0.0940  memory: 149  loss: 0.3669  fgnd: 0.2270  bgnd: 0.0111  P: 0.6702  R: 0.7875  F1: 0.7241
2025/06/23 12:37:40 - mmengine - INFO - Epoch(train)  [19][120/123]  lr: 2.0000e-02  eta: 0:17:39  time: 0.0943  data_time: 0.0696  memory: 149  loss: 0.3379  fgnd: 0.2098  bgnd: 0.0109  P: 0.6136  R: 0.8265  F1: 0.7043
2025/06/23 12:37:40 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_123300
2025/06/23 12:37:40 - mmengine - INFO - Saving checkpoint at 19 epochs
2025/06/23 12:37:40 - mmengine - INFO - Epoch(val)  [19][10/17]    eta: 0:00:00  time: 0.0449  data_time: 0.0316  memory: 149  
2025/06/23 12:37:41 - mmengine - INFO - Epoch(val) [19][17/17]    P: 0.0167  R: 0.3065  F1: 0.0316  data_time: 0.0275  time: 0.0461
2025/06/23 12:37:42 - mmengine - INFO - Epoch(train)  [20][ 10/123]  lr: 2.0000e-02  eta: 0:17:39  time: 0.0879  data_time: 0.0663  memory: 149  loss: 0.2934  fgnd: 0.3667  bgnd: 0.0206  P: 0.7387  R: 0.6074  F1: 0.6667
2025/06/23 12:37:44 - mmengine - INFO - Epoch(train)  [20][ 20/123]  lr: 2.0000e-02  eta: 0:17:41  time: 0.0838  data_time: 0.0594  memory: 149  loss: 0.3325  fgnd: 0.4431  bgnd: 0.0244  P: 0.7386  R: 0.4745  F1: 0.5778
2025/06/23 12:37:46 - mmengine - INFO - Epoch(train)  [20][ 30/123]  lr: 2.0000e-02  eta: 0:17:43  time: 0.1143  data_time: 0.0874  memory: 149  loss: 0.3536  fgnd: 0.2902  bgnd: 0.0162  P: 0.6533  R: 0.5632  F1: 0.6049
2025/06/23 12:37:46 - mmengine - INFO - Epoch(train)  [20][ 40/123]  lr: 2.0000e-02  eta: 0:17:39  time: 0.1179  data_time: 0.0909  memory: 149  loss: 0.3573  fgnd: 0.2708  bgnd: 0.0114  P: 0.7067  R: 0.7361  F1: 0.7211
2025/06/23 12:37:46 - mmengine - INFO - Epoch(train)  [20][ 50/123]  lr: 2.0000e-02  eta: 0:17:35  time: 0.1183  data_time: 0.0911  memory: 149  loss: 0.3664  fgnd: 0.2310  bgnd: 0.0113  P: 0.8132  R: 0.8222  F1: 0.8177
2025/06/23 12:37:47 - mmengine - INFO - Epoch(train)  [20][ 60/123]  lr: 2.0000e-02  eta: 0:17:30  time: 0.0898  data_time: 0.0649  memory: 149  loss: 0.3317  fgnd: 0.1912  bgnd: 0.0105  P: 0.7381  R: 0.9208  F1: 0.8194
2025/06/23 12:37:48 - mmengine - INFO - Epoch(train)  [20][ 70/123]  lr: 2.0000e-02  eta: 0:17:31  time: 0.0847  data_time: 0.0619  memory: 149  loss: 0.3072  fgnd: 0.4437  bgnd: 0.0232  P: 0.6723  R: 0.5517  F1: 0.6061
2025/06/23 12:37:50 - mmengine - INFO - Epoch(train)  [20][ 80/123]  lr: 2.0000e-02  eta: 0:17:31  time: 0.0771  data_time: 0.0531  memory: 149  loss: 0.3322  fgnd: 0.3424  bgnd: 0.0237  P: 0.7215  R: 0.4524  F1: 0.5561
2025/06/23 12:37:52 - mmengine - INFO - Epoch(train)  [20][ 90/123]  lr: 2.0000e-02  eta: 0:17:33  time: 0.1056  data_time: 0.0787  memory: 149  loss: 0.3574  fgnd: 0.2964  bgnd: 0.0150  P: 0.7727  R: 0.5862  F1: 0.6667
2025/06/23 12:37:52 - mmengine - INFO - Epoch(train)  [20][100/123]  lr: 2.0000e-02  eta: 0:17:32  time: 0.1202  data_time: 0.0932  memory: 149  loss: 0.3590  fgnd: 0.2804  bgnd: 0.0113  P: 0.6593  R: 0.7692  F1: 0.7101
2025/06/23 12:37:53 - mmengine - INFO - Epoch(train)  [20][110/123]  lr: 2.0000e-02  eta: 0:17:28  time: 0.1204  data_time: 0.0934  memory: 149  loss: 0.3650  fgnd: 0.2327  bgnd: 0.0107  P: 0.6880  R: 0.8958  F1: 0.7783
2025/06/23 12:37:53 - mmengine - INFO - Epoch(train)  [20][120/123]  lr: 2.0000e-02  eta: 0:17:23  time: 0.0946  data_time: 0.0693  memory: 149  loss: 0.3345  fgnd: 0.2052  bgnd: 0.0107  P: 0.5900  R: 0.7468  F1: 0.6592
2025/06/23 12:37:53 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_123300
2025/06/23 12:37:53 - mmengine - INFO - Saving checkpoint at 20 epochs
2025/06/23 12:37:54 - mmengine - INFO - Epoch(val)  [20][10/17]    eta: 0:00:00  time: 0.0449  data_time: 0.0313  memory: 149  
2025/06/23 12:37:54 - mmengine - INFO - Epoch(val) [20][17/17]    P: 0.0402  R: 0.2036  F1: 0.0672  data_time: 0.0334  time: 0.0442
2025/06/23 12:37:56 - mmengine - INFO - Epoch(train)  [21][ 10/123]  lr: 2.0000e-02  eta: 0:17:23  time: 0.0879  data_time: 0.0649  memory: 149  loss: 0.3038  fgnd: 0.5137  bgnd: 0.0252  P: 0.7193  R: 0.5325  F1: 0.6119
2025/06/23 12:37:58 - mmengine - INFO - Epoch(train)  [21][ 20/123]  lr: 2.0000e-02  eta: 0:17:25  time: 0.0839  data_time: 0.0593  memory: 149  loss: 0.3342  fgnd: 0.4385  bgnd: 0.0278  P: 0.7248  R: 0.4877  F1: 0.5830
2025/06/23 12:37:59 - mmengine - INFO - Epoch(train)  [21][ 30/123]  lr: 2.0000e-02  eta: 0:17:27  time: 0.1144  data_time: 0.0874  memory: 149  loss: 0.3471  fgnd: 0.2792  bgnd: 0.0143  P: 0.7738  R: 0.6842  F1: 0.7263
2025/06/23 12:38:00 - mmengine - INFO - Epoch(train)  [21][ 40/123]  lr: 2.0000e-02  eta: 0:17:23  time: 0.1178  data_time: 0.0905  memory: 149  loss: 0.3501  fgnd: 0.2595  bgnd: 0.0113  P: 0.8254  R: 0.7324  F1: 0.7761
2025/06/23 12:38:00 - mmengine - INFO - Epoch(train)  [21][ 50/123]  lr: 2.0000e-02  eta: 0:17:19  time: 0.1183  data_time: 0.0908  memory: 149  loss: 0.3583  fgnd: 0.2126  bgnd: 0.0108  P: 0.7396  R: 0.8452  F1: 0.7889
2025/06/23 12:38:00 - mmengine - INFO - Epoch(train)  [21][ 60/123]  lr: 2.0000e-02  eta: 0:17:15  time: 0.0897  data_time: 0.0648  memory: 149  loss: 0.3172  fgnd: 0.1843  bgnd: 0.0107  P: 0.7619  R: 0.8989  F1: 0.8247
2025/06/23 12:38:02 - mmengine - INFO - Epoch(train)  [21][ 70/123]  lr: 2.0000e-02  eta: 0:17:15  time: 0.0845  data_time: 0.0615  memory: 149  loss: 0.2946  fgnd: 0.4557  bgnd: 0.0253  P: 0.8182  R: 0.5455  F1: 0.6545
2025/06/23 12:38:03 - mmengine - INFO - Epoch(train)  [21][ 80/123]  lr: 2.0000e-02  eta: 0:17:16  time: 0.0770  data_time: 0.0528  memory: 149  loss: 0.3246  fgnd: 0.3832  bgnd: 0.0227  P: 0.8462  R: 0.5197  F1: 0.6439
2025/06/23 12:38:05 - mmengine - INFO - Epoch(train)  [21][ 90/123]  lr: 2.0000e-02  eta: 0:17:18  time: 0.1063  data_time: 0.0799  memory: 149  loss: 0.3418  fgnd: 0.2858  bgnd: 0.0143  P: 0.7125  R: 0.6404  F1: 0.6746
2025/06/23 12:38:06 - mmengine - INFO - Epoch(train)  [21][100/123]  lr: 2.0000e-02  eta: 0:17:17  time: 0.1213  data_time: 0.0942  memory: 149  loss: 0.3391  fgnd: 0.2380  bgnd: 0.0110  P: 0.6667  R: 0.7353  F1: 0.6993
2025/06/23 12:38:06 - mmengine - INFO - Epoch(train)  [21][110/123]  lr: 2.0000e-02  eta: 0:17:12  time: 0.1219  data_time: 0.0947  memory: 149  loss: 0.3443  fgnd: 0.2401  bgnd: 0.0116  P: 0.6804  R: 0.8148  F1: 0.7416
2025/06/23 12:38:07 - mmengine - INFO - Epoch(train)  [21][120/123]  lr: 2.0000e-02  eta: 0:17:08  time: 0.0963  data_time: 0.0709  memory: 149  loss: 0.3146  fgnd: 0.1929  bgnd: 0.0108  P: 0.6870  R: 0.8404  F1: 0.7560
2025/06/23 12:38:07 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_123300
2025/06/23 12:38:07 - mmengine - INFO - Saving checkpoint at 21 epochs
