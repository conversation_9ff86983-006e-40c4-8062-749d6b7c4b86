2025/06/23 12:38:22 - mmengine - INFO - 
------------------------------------------------------------
System environment:
    sys.platform: win32
    Python: 3.12.11 | packaged by Anaconda, Inc. | (main, Jun  5 2025, 12:58:53) [MSC v.1929 64 bit (AMD64)]
    CUDA available: True
    MUSA available: False
    numpy_random_seed: 547498512
    GPU 0: NVIDIA GeForce RTX 5060 Ti
    CUDA_HOME: None
    MSVC: Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35209 for x64
    GCC: n/a
    PyTorch: 2.8.0.dev20250608+cu128
    PyTorch compiling details: PyTorch built with:
  - C++ Version: 201703
  - MSVC 193833144
  - Intel(R) oneAPI Math Kernel Library Version 2025.1-Product Build 20250306 for Intel(R) 64 architecture applications
  - Intel(R) MKL-DNN v3.7.1 (Git Hash 8d263e693366ef8db40acc569cc7d8edf644556d)
  - OpenMP 2019
  - LAPACK is enabled (usually provided by MKL)
  - CPU capability usage: AVX512
  - CUDA Runtime 12.8
  - NVCC architecture flags: -gencode;arch=compute_61,code=sm_61;-gencode;arch=compute_70,code=sm_70;-gencode;arch=compute_75,code=sm_75;-gencode;arch=compute_80,code=sm_80;-gencode;arch=compute_86,code=sm_86;-gencode;arch=compute_90,code=sm_90;-gencode;arch=compute_100,code=sm_100;-gencode;arch=compute_120,code=sm_120
  - CuDNN 90.7.1
  - Magma 2.5.4
  - Build settings: BLAS_INFO=mkl, BUILD_TYPE=Release, COMMIT_SHA=843156205ec57cf78d5cf10bf982656b3db51dfd, CUDA_VERSION=12.8, CUDNN_VERSION=9.7.1, CXX_COMPILER=C:/actions-runner/_work/pytorch/pytorch/pytorch/.ci/pytorch/windows/tmp_bin/sccache-cl.exe, CXX_FLAGS=/DWIN32 /D_WINDOWS /GR /EHsc /Zc:__cplusplus /bigobj /FS /utf-8 -DUSE_PTHREADPOOL -DNDEBUG -DUSE_KINETO -DLIBKINETO_NOROCTRACER -DLIBKINETO_NOXPUPTI=ON -DUSE_FBGEMM -DUSE_XNNPACK -DSYMBOLICATE_MOBILE_DEBUG_HANDLE /wd4624 /wd4068 /wd4067 /wd4267 /wd4661 /wd4717 /wd4244 /wd4804 /wd4273, LAPACK_INFO=mkl, PERF_WITH_AVX=1, PERF_WITH_AVX2=1, TORCH_VERSION=2.8.0, USE_CUDA=ON, USE_CUDNN=ON, USE_CUSPARSELT=OFF, USE_GFLAGS=OFF, USE_GLOG=OFF, USE_GLOO=ON, USE_MKL=ON, USE_MKLDNN=ON, USE_MPI=OFF, USE_NCCL=OFF, USE_NNPACK=OFF, USE_OPENMP=ON, USE_ROCM=OFF, USE_ROCM_KERNEL_ASSERT=OFF, USE_XCCL=OFF, USE_XPU=OFF, 

    TorchVision: 0.23.0.dev20250609+cu128
    OpenCV: 4.11.0
    MMEngine: 0.10.4

Runtime environment:
    cudnn_benchmark: True
    mp_cfg: {'mp_start_method': 'fork', 'opencv_num_threads': 0}
    dist_cfg: {'backend': 'nccl'}
    seed: 547498512
    deterministic: False
    Distributed launcher: none
    Distributed training: False
    GPU number: 1
------------------------------------------------------------

2025/06/23 12:38:24 - mmengine - INFO - Distributed training is not used, all SyncBatchNorm (SyncBN) layers in the model will be automatically reverted to BatchNormXd layers if they are used.
2025/06/23 12:38:24 - mmengine - WARNING - The prefix is not set in metric class FomoMetric.
Name of parameter - Initialization information

backbone.conv1.conv.weight - torch.Size([16, 3, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.conv1.norm.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.conv1.norm.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer1.0.conv.0.conv.weight - torch.Size([16, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer1.0.conv.0.norm.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer1.0.conv.0.norm.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer1.0.conv.1.weight - torch.Size([8, 16, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer1.0.conv.2.weight - torch.Size([8]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer1.0.conv.2.bias - torch.Size([8]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.0.conv.0.conv.weight - torch.Size([48, 8, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer2.0.conv.0.norm.weight - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.0.conv.0.norm.bias - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.0.conv.1.conv.weight - torch.Size([48, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer2.0.conv.1.norm.weight - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.0.conv.1.norm.bias - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.0.conv.2.weight - torch.Size([8, 48, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer2.0.conv.3.weight - torch.Size([8]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.0.conv.3.bias - torch.Size([8]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.1.conv.0.conv.weight - torch.Size([48, 8, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer2.1.conv.0.norm.weight - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.1.conv.0.norm.bias - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.1.conv.1.conv.weight - torch.Size([48, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer2.1.conv.1.norm.weight - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.1.conv.1.norm.bias - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.1.conv.2.weight - torch.Size([8, 48, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer2.1.conv.3.weight - torch.Size([8]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.1.conv.3.bias - torch.Size([8]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.0.conv.0.conv.weight - torch.Size([48, 8, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer3.0.conv.0.norm.weight - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.0.conv.0.norm.bias - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.0.conv.1.conv.weight - torch.Size([48, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer3.0.conv.1.norm.weight - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.0.conv.1.norm.bias - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.0.conv.2.weight - torch.Size([16, 48, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer3.0.conv.3.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.0.conv.3.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.1.conv.0.conv.weight - torch.Size([96, 16, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer3.1.conv.0.norm.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.1.conv.0.norm.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.1.conv.1.conv.weight - torch.Size([96, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer3.1.conv.1.norm.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.1.conv.1.norm.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.1.conv.2.weight - torch.Size([16, 96, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer3.1.conv.3.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.1.conv.3.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.2.conv.0.conv.weight - torch.Size([96, 16, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer3.2.conv.0.norm.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.2.conv.0.norm.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.2.conv.1.conv.weight - torch.Size([96, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer3.2.conv.1.norm.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.2.conv.1.norm.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.2.conv.2.weight - torch.Size([16, 96, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer3.2.conv.3.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.2.conv.3.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.0.conv.0.conv.weight - torch.Size([96, 16, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.0.conv.0.norm.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.0.conv.0.norm.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.0.conv.1.conv.weight - torch.Size([96, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.0.conv.1.norm.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.0.conv.1.norm.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.0.conv.2.weight - torch.Size([24, 96, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.0.conv.3.weight - torch.Size([24]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.0.conv.3.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.1.conv.0.conv.weight - torch.Size([144, 24, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.1.conv.0.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.1.conv.0.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.1.conv.1.conv.weight - torch.Size([144, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.1.conv.1.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.1.conv.1.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.1.conv.2.weight - torch.Size([24, 144, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.1.conv.3.weight - torch.Size([24]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.1.conv.3.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.2.conv.0.conv.weight - torch.Size([144, 24, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.2.conv.0.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.2.conv.0.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.2.conv.1.conv.weight - torch.Size([144, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.2.conv.1.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.2.conv.1.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.2.conv.2.weight - torch.Size([24, 144, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.2.conv.3.weight - torch.Size([24]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.2.conv.3.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.3.conv.0.conv.weight - torch.Size([144, 24, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.3.conv.0.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.3.conv.0.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.3.conv.1.conv.weight - torch.Size([144, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.3.conv.1.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.3.conv.1.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.3.conv.2.weight - torch.Size([24, 144, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.3.conv.3.weight - torch.Size([24]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.3.conv.3.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.0.conv.0.conv.weight - torch.Size([144, 24, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer5.0.conv.0.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.0.conv.0.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.0.conv.1.conv.weight - torch.Size([144, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer5.0.conv.1.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.0.conv.1.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.0.conv.2.weight - torch.Size([32, 144, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer5.0.conv.3.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.0.conv.3.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.1.conv.0.conv.weight - torch.Size([192, 32, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer5.1.conv.0.norm.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.1.conv.0.norm.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.1.conv.1.conv.weight - torch.Size([192, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer5.1.conv.1.norm.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.1.conv.1.norm.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.1.conv.2.weight - torch.Size([32, 192, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer5.1.conv.3.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.1.conv.3.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.2.conv.0.conv.weight - torch.Size([192, 32, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer5.2.conv.0.norm.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.2.conv.0.norm.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.2.conv.1.conv.weight - torch.Size([192, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer5.2.conv.1.norm.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.2.conv.1.norm.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.2.conv.2.weight - torch.Size([32, 192, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer5.2.conv.3.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.2.conv.3.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.0.conv.0.conv.weight - torch.Size([192, 32, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer6.0.conv.0.norm.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.0.conv.0.norm.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.0.conv.1.conv.weight - torch.Size([192, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer6.0.conv.1.norm.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.0.conv.1.norm.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.0.conv.2.weight - torch.Size([56, 192, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer6.0.conv.3.weight - torch.Size([56]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.0.conv.3.bias - torch.Size([56]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.1.conv.0.conv.weight - torch.Size([336, 56, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer6.1.conv.0.norm.weight - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.1.conv.0.norm.bias - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.1.conv.1.conv.weight - torch.Size([336, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer6.1.conv.1.norm.weight - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.1.conv.1.norm.bias - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.1.conv.2.weight - torch.Size([56, 336, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer6.1.conv.3.weight - torch.Size([56]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.1.conv.3.bias - torch.Size([56]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.2.conv.0.conv.weight - torch.Size([336, 56, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer6.2.conv.0.norm.weight - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.2.conv.0.norm.bias - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.2.conv.1.conv.weight - torch.Size([336, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer6.2.conv.1.norm.weight - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.2.conv.1.norm.bias - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.2.conv.2.weight - torch.Size([56, 336, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer6.2.conv.3.weight - torch.Size([56]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.2.conv.3.bias - torch.Size([56]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer7.0.conv.0.conv.weight - torch.Size([336, 56, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer7.0.conv.0.norm.weight - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer7.0.conv.0.norm.bias - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer7.0.conv.1.conv.weight - torch.Size([336, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer7.0.conv.1.norm.weight - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer7.0.conv.1.norm.bias - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer7.0.conv.2.weight - torch.Size([112, 336, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer7.0.conv.3.weight - torch.Size([112]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer7.0.conv.3.bias - torch.Size([112]): 
The value is the same before and after calling `init_weights` of Fomo  

bbox_head.convs_bridge.0.0.weight - torch.Size([48, 16, 1, 1]): 
Initialized by user-defined `init_weights` in FomoHead  

bbox_head.convs_bridge.0.1.weight - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

bbox_head.convs_bridge.0.1.bias - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

bbox_head.convs_pred.0.weight - torch.Size([3, 48, 1, 1]): 
Initialized by user-defined `init_weights` in FomoHead  

bbox_head.convs_pred.0.bias - torch.Size([3]): 
Initialized by user-defined `init_weights` in FomoHead  
2025/06/23 12:38:25 - mmengine - WARNING - "FileClient" will be deprecated in future. Please use io functions in https://mmengine.readthedocs.io/en/latest/api/fileio.html#file-io
2025/06/23 12:38:25 - mmengine - WARNING - "HardDiskBackend" is the alias of "LocalBackend" and the former will be deprecated in future.
2025/06/23 12:38:25 - mmengine - INFO - Checkpoints will be saved to D:\OBJECT_DETECTION\sscma\ModelAssistant\work_dirs\fomo_mobnetv2_0.35_x8_coco.
2025/06/23 12:38:35 - mmengine - INFO - Epoch(train)   [1][ 10/123]  lr: 6.2207e-03  eta: 3:33:31  time: 1.0425  data_time: 0.9723  memory: 161  loss: 1.1161  fgnd: 0.9812  bgnd: 0.2062  P: 0.1008  R: 0.1034  F1: 0.1021
2025/06/23 12:38:37 - mmengine - INFO - Epoch(train)   [1][ 20/123]  lr: 1.3110e-02  eta: 2:04:28  time: 0.6082  data_time: 0.5591  memory: 147  loss: 1.0811  fgnd: 0.9249  bgnd: 0.0988  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:38:39 - mmengine - INFO - Epoch(train)   [1][ 30/123]  lr: 2.0000e-02  eta: 1:35:14  time: 0.4657  data_time: 0.4264  memory: 147  loss: 0.9961  fgnd: 0.6616  bgnd: 0.0364  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:38:39 - mmengine - INFO - Epoch(train)   [1][ 40/123]  lr: 2.0000e-02  eta: 1:13:25  time: 0.3593  data_time: 0.3263  memory: 147  loss: 0.8681  fgnd: 0.3707  bgnd: 0.0172  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:38:39 - mmengine - INFO - Epoch(train)   [1][ 50/123]  lr: 2.0000e-02  eta: 0:59:38  time: 0.2921  data_time: 0.2630  memory: 147  loss: 0.7650  fgnd: 0.2921  bgnd: 0.0176  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:38:39 - mmengine - INFO - Epoch(train)   [1][ 60/123]  lr: 2.0000e-02  eta: 0:50:27  time: 0.0883  data_time: 0.0706  memory: 147  loss: 0.6015  fgnd: 0.2737  bgnd: 0.0174  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:38:41 - mmengine - INFO - Epoch(train)   [1][ 70/123]  lr: 2.0000e-02  eta: 0:47:30  time: 0.0831  data_time: 0.0673  memory: 147  loss: 0.6448  fgnd: 1.7598  bgnd: 0.0443  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:38:42 - mmengine - INFO - Epoch(train)   [1][ 80/123]  lr: 2.0000e-02  eta: 0:45:08  time: 0.0752  data_time: 0.0593  memory: 147  loss: 0.6767  fgnd: 0.6648  bgnd: 0.0558  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:38:44 - mmengine - INFO - Epoch(train)   [1][ 90/123]  lr: 2.0000e-02  eta: 0:44:18  time: 0.1045  data_time: 0.0869  memory: 147  loss: 0.7288  fgnd: 0.5752  bgnd: 0.0467  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:38:45 - mmengine - INFO - Epoch(train)   [1][100/123]  lr: 2.0000e-02  eta: 0:41:43  time: 0.1182  data_time: 0.1006  memory: 147  loss: 0.7793  fgnd: 0.5390  bgnd: 0.0399  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:38:45 - mmengine - INFO - Epoch(train)   [1][110/123]  lr: 2.0000e-02  eta: 0:38:19  time: 0.1183  data_time: 0.1006  memory: 147  loss: 0.8334  fgnd: 0.4757  bgnd: 0.0367  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:38:46 - mmengine - INFO - Epoch(train)   [1][120/123]  lr: 2.0000e-02  eta: 0:35:29  time: 0.0932  data_time: 0.0765  memory: 147  loss: 0.6759  fgnd: 0.4127  bgnd: 0.0347  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:38:46 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_123820
2025/06/23 12:38:46 - mmengine - INFO - Saving checkpoint at 1 epochs
2025/06/23 12:38:55 - mmengine - INFO - Epoch(val)   [1][10/17]    eta: 0:00:06  time: 0.9275  data_time: 0.8973  memory: 147  
2025/06/23 12:38:55 - mmengine - INFO - Epoch(val) [1][17/17]    P: 0.0000  R: 0.0000  F1: 0.0000  data_time: 0.5378  time: 0.5583
2025/06/23 12:38:57 - mmengine - INFO - Epoch(train)   [2][ 10/123]  lr: 2.0000e-02  eta: 0:34:34  time: 0.0875  data_time: 0.0707  memory: 150  loss: 0.5944  fgnd: 0.9658  bgnd: 0.0414  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:38:59 - mmengine - INFO - Epoch(train)   [2][ 20/123]  lr: 2.0000e-02  eta: 0:34:35  time: 0.0825  data_time: 0.0657  memory: 149  loss: 0.6318  fgnd: 0.7722  bgnd: 0.0369  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:39:01 - mmengine - INFO - Epoch(train)   [2][ 30/123]  lr: 2.0000e-02  eta: 0:34:47  time: 0.1138  data_time: 0.0958  memory: 149  loss: 0.6324  fgnd: 0.5257  bgnd: 0.0285  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:39:01 - mmengine - INFO - Epoch(train)   [2][ 40/123]  lr: 2.0000e-02  eta: 0:33:07  time: 0.1172  data_time: 0.0981  memory: 149  loss: 0.6163  fgnd: 0.4300  bgnd: 0.0219  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:39:01 - mmengine - INFO - Epoch(train)   [2][ 50/123]  lr: 2.0000e-02  eta: 0:31:26  time: 0.1166  data_time: 0.0986  memory: 149  loss: 0.6097  fgnd: 0.3639  bgnd: 0.0185  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:39:01 - mmengine - INFO - Epoch(train)   [2][ 60/123]  lr: 2.0000e-02  eta: 0:29:57  time: 0.0895  data_time: 0.0726  memory: 149  loss: 0.5436  fgnd: 0.3148  bgnd: 0.0170  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:39:03 - mmengine - INFO - Epoch(train)   [2][ 70/123]  lr: 2.0000e-02  eta: 0:29:57  time: 0.0848  data_time: 0.0687  memory: 149  loss: 0.4862  fgnd: 0.7039  bgnd: 0.0275  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:39:04 - mmengine - INFO - Epoch(train)   [2][ 80/123]  lr: 2.0000e-02  eta: 0:29:51  time: 0.0755  data_time: 0.0591  memory: 149  loss: 0.5303  fgnd: 0.7802  bgnd: 0.0317  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:39:06 - mmengine - INFO - Epoch(train)   [2][ 90/123]  lr: 2.0000e-02  eta: 0:30:09  time: 0.1040  data_time: 0.0881  memory: 149  loss: 0.5575  fgnd: 0.4928  bgnd: 0.0219  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:39:07 - mmengine - INFO - Epoch(train)   [2][100/123]  lr: 2.0000e-02  eta: 0:29:38  time: 0.1184  data_time: 0.1023  memory: 149  loss: 0.5683  fgnd: 0.4498  bgnd: 0.0194  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:39:07 - mmengine - INFO - Epoch(train)   [2][110/123]  lr: 2.0000e-02  eta: 0:28:32  time: 0.1183  data_time: 0.1022  memory: 149  loss: 0.5835  fgnd: 0.3906  bgnd: 0.0195  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:39:08 - mmengine - INFO - Epoch(train)   [2][120/123]  lr: 2.0000e-02  eta: 0:27:31  time: 0.0928  data_time: 0.0776  memory: 149  loss: 0.5428  fgnd: 0.3515  bgnd: 0.0192  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:39:08 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_123820
2025/06/23 12:39:08 - mmengine - INFO - Saving checkpoint at 2 epochs
2025/06/23 12:39:08 - mmengine - INFO - Epoch(val)   [2][10/17]    eta: 0:00:00  time: 0.3720  data_time: 0.3559  memory: 149  
2025/06/23 12:39:09 - mmengine - INFO - Epoch(val) [2][17/17]    P: 0.0000  R: 0.0000  F1: 0.0000  data_time: 0.0351  time: 0.0424
2025/06/23 12:39:10 - mmengine - INFO - Epoch(train)   [3][ 10/123]  lr: 2.0000e-02  eta: 0:27:25  time: 0.0865  data_time: 0.0722  memory: 149  loss: 0.4837  fgnd: 0.7544  bgnd: 0.0309  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:39:12 - mmengine - INFO - Epoch(train)   [3][ 20/123]  lr: 2.0000e-02  eta: 0:27:39  time: 0.0814  data_time: 0.0660  memory: 149  loss: 0.5294  fgnd: 0.8524  bgnd: 0.0361  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:39:14 - mmengine - INFO - Epoch(train)   [3][ 30/123]  lr: 2.0000e-02  eta: 0:27:58  time: 0.1120  data_time: 0.0951  memory: 149  loss: 0.5313  fgnd: 0.4354  bgnd: 0.0209  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:39:14 - mmengine - INFO - Epoch(train)   [3][ 40/123]  lr: 2.0000e-02  eta: 0:27:14  time: 0.1153  data_time: 0.0983  memory: 149  loss: 0.5273  fgnd: 0.3854  bgnd: 0.0152  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:39:14 - mmengine - INFO - Epoch(train)   [3][ 50/123]  lr: 2.0000e-02  eta: 0:26:27  time: 0.1159  data_time: 0.0984  memory: 149  loss: 0.5295  fgnd: 0.3119  bgnd: 0.0159  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:39:15 - mmengine - INFO - Epoch(train)   [3][ 60/123]  lr: 2.0000e-02  eta: 0:25:43  time: 0.0879  data_time: 0.0713  memory: 149  loss: 0.4696  fgnd: 0.2718  bgnd: 0.0157  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:39:16 - mmengine - INFO - Epoch(train)   [3][ 70/123]  lr: 2.0000e-02  eta: 0:25:52  time: 0.0850  data_time: 0.0686  memory: 149  loss: 0.4300  fgnd: 0.6172  bgnd: 0.0260  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:39:18 - mmengine - INFO - Epoch(train)   [3][ 80/123]  lr: 2.0000e-02  eta: 0:25:57  time: 0.0777  data_time: 0.0611  memory: 149  loss: 0.4862  fgnd: 0.6084  bgnd: 0.0263  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:39:19 - mmengine - INFO - Epoch(train)   [3][ 90/123]  lr: 2.0000e-02  eta: 0:26:17  time: 0.1080  data_time: 0.0898  memory: 149  loss: 0.5147  fgnd: 0.3953  bgnd: 0.0173  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:39:20 - mmengine - INFO - Epoch(train)   [3][100/123]  lr: 2.0000e-02  eta: 0:26:05  time: 0.1233  data_time: 0.1043  memory: 149  loss: 0.5219  fgnd: 0.3863  bgnd: 0.0157  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:39:21 - mmengine - INFO - Epoch(train)   [3][110/123]  lr: 2.0000e-02  eta: 0:25:28  time: 0.1238  data_time: 0.1047  memory: 149  loss: 0.5341  fgnd: 0.3497  bgnd: 0.0161  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:39:21 - mmengine - INFO - Epoch(train)   [3][120/123]  lr: 2.0000e-02  eta: 0:24:53  time: 0.0970  data_time: 0.0791  memory: 149  loss: 0.4881  fgnd: 0.2975  bgnd: 0.0157  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:39:21 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_123820
2025/06/23 12:39:21 - mmengine - INFO - Saving checkpoint at 3 epochs
2025/06/23 12:39:22 - mmengine - INFO - Epoch(val)   [3][10/17]    eta: 0:00:00  time: 0.2455  data_time: 0.2325  memory: 149  
2025/06/23 12:39:22 - mmengine - INFO - Epoch(val) [3][17/17]    P: 0.0000  R: 0.0000  F1: 0.0000  data_time: 0.0351  time: 0.0434
2025/06/23 12:39:24 - mmengine - INFO - Epoch(train)   [4][ 10/123]  lr: 2.0000e-02  eta: 0:24:54  time: 0.0897  data_time: 0.0735  memory: 149  loss: 0.4241  fgnd: 0.6521  bgnd: 0.0274  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:39:26 - mmengine - INFO - Epoch(train)   [4][ 20/123]  lr: 2.0000e-02  eta: 0:25:09  time: 0.0848  data_time: 0.0687  memory: 149  loss: 0.4633  fgnd: 0.5546  bgnd: 0.0277  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:39:28 - mmengine - INFO - Epoch(train)   [4][ 30/123]  lr: 2.0000e-02  eta: 0:25:27  time: 0.1163  data_time: 0.0982  memory: 149  loss: 0.4781  fgnd: 0.3870  bgnd: 0.0199  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:39:28 - mmengine - INFO - Epoch(train)   [4][ 40/123]  lr: 2.0000e-02  eta: 0:25:00  time: 0.1199  data_time: 0.1019  memory: 149  loss: 0.4776  fgnd: 0.3317  bgnd: 0.0148  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:39:28 - mmengine - INFO - Epoch(train)   [4][ 50/123]  lr: 2.0000e-02  eta: 0:24:30  time: 0.1204  data_time: 0.1023  memory: 149  loss: 0.4783  fgnd: 0.2836  bgnd: 0.0146  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:39:28 - mmengine - INFO - Epoch(train)   [4][ 60/123]  lr: 2.0000e-02  eta: 0:24:01  time: 0.0917  data_time: 0.0748  memory: 149  loss: 0.4224  fgnd: 0.2375  bgnd: 0.0137  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:39:30 - mmengine - INFO - Epoch(train)   [4][ 70/123]  lr: 2.0000e-02  eta: 0:24:08  time: 0.0861  data_time: 0.0699  memory: 149  loss: 0.3940  fgnd: 0.8093  bgnd: 0.0292  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:39:31 - mmengine - INFO - Epoch(train)   [4][ 80/123]  lr: 2.0000e-02  eta: 0:24:14  time: 0.0782  data_time: 0.0621  memory: 149  loss: 0.4514  fgnd: 0.5489  bgnd: 0.0264  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:39:33 - mmengine - INFO - Epoch(train)   [4][ 90/123]  lr: 2.0000e-02  eta: 0:24:31  time: 0.1081  data_time: 0.0901  memory: 149  loss: 0.4807  fgnd: 0.3690  bgnd: 0.0176  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:39:34 - mmengine - INFO - Epoch(train)   [4][100/123]  lr: 2.0000e-02  eta: 0:24:23  time: 0.1232  data_time: 0.1051  memory: 149  loss: 0.4943  fgnd: 0.4018  bgnd: 0.0145  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:39:35 - mmengine - INFO - Epoch(train)   [4][110/123]  lr: 2.0000e-02  eta: 0:23:58  time: 0.1235  data_time: 0.1055  memory: 149  loss: 0.5141  fgnd: 0.3323  bgnd: 0.0149  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:39:35 - mmengine - INFO - Epoch(train)   [4][120/123]  lr: 2.0000e-02  eta: 0:23:32  time: 0.0977  data_time: 0.0806  memory: 149  loss: 0.4758  fgnd: 0.2752  bgnd: 0.0148  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:39:35 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_123820
2025/06/23 12:39:35 - mmengine - INFO - Saving checkpoint at 4 epochs
2025/06/23 12:39:36 - mmengine - INFO - Epoch(val)   [4][10/17]    eta: 0:00:00  time: 0.0433  data_time: 0.0353  memory: 149  
2025/06/23 12:39:36 - mmengine - INFO - Epoch(val) [4][17/17]    P: 0.0000  R: 0.0000  F1: 0.0000  data_time: 0.0361  time: 0.0441
2025/06/23 12:39:37 - mmengine - INFO - Epoch(train)   [5][ 10/123]  lr: 2.0000e-02  eta: 0:23:35  time: 0.0900  data_time: 0.0750  memory: 149  loss: 0.4088  fgnd: 0.6063  bgnd: 0.0262  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:39:39 - mmengine - INFO - Epoch(train)   [5][ 20/123]  lr: 2.0000e-02  eta: 0:23:47  time: 0.0849  data_time: 0.0678  memory: 149  loss: 0.4587  fgnd: 0.6470  bgnd: 0.0323  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:39:41 - mmengine - INFO - Epoch(train)   [5][ 30/123]  lr: 2.0000e-02  eta: 0:24:03  time: 0.1167  data_time: 0.0984  memory: 149  loss: 0.4682  fgnd: 0.4157  bgnd: 0.0180  P: 1.0000  R: 0.0204  F1: 0.0400
2025/06/23 12:39:42 - mmengine - INFO - Epoch(train)   [5][ 40/123]  lr: 2.0000e-02  eta: 0:23:43  time: 0.1200  data_time: 0.1015  memory: 149  loss: 0.4658  fgnd: 0.3674  bgnd: 0.0150  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:39:42 - mmengine - INFO - Epoch(train)   [5][ 50/123]  lr: 2.0000e-02  eta: 0:23:21  time: 0.1208  data_time: 0.1022  memory: 149  loss: 0.4713  fgnd: 0.3047  bgnd: 0.0139  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:39:42 - mmengine - INFO - Epoch(train)   [5][ 60/123]  lr: 2.0000e-02  eta: 0:22:59  time: 0.0918  data_time: 0.0743  memory: 149  loss: 0.4245  fgnd: 0.2891  bgnd: 0.0139  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:39:44 - mmengine - INFO - Epoch(train)   [5][ 70/123]  lr: 2.0000e-02  eta: 0:23:05  time: 0.0865  data_time: 0.0710  memory: 149  loss: 0.3844  fgnd: 0.6302  bgnd: 0.0270  P: 1.0000  R: 0.0233  F1: 0.0455
2025/06/23 12:39:45 - mmengine - INFO - Epoch(train)   [5][ 80/123]  lr: 2.0000e-02  eta: 0:23:11  time: 0.0778  data_time: 0.0609  memory: 149  loss: 0.4205  fgnd: 0.5093  bgnd: 0.0256  P: 1.0000  R: 0.0116  F1: 0.0230
2025/06/23 12:39:47 - mmengine - INFO - Epoch(train)   [5][ 90/123]  lr: 2.0000e-02  eta: 0:23:25  time: 0.1087  data_time: 0.0883  memory: 149  loss: 0.4415  fgnd: 0.3879  bgnd: 0.0187  P: 0.3750  R: 0.0556  F1: 0.0968
2025/06/23 12:39:48 - mmengine - INFO - Epoch(train)   [5][100/123]  lr: 2.0000e-02  eta: 0:23:18  time: 0.1226  data_time: 0.1016  memory: 149  loss: 0.4402  fgnd: 0.2901  bgnd: 0.0141  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:39:48 - mmengine - INFO - Epoch(train)   [5][110/123]  lr: 2.0000e-02  eta: 0:22:58  time: 0.1229  data_time: 0.1021  memory: 149  loss: 0.4418  fgnd: 0.2677  bgnd: 0.0136  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:39:48 - mmengine - INFO - Epoch(train)   [5][120/123]  lr: 2.0000e-02  eta: 0:22:39  time: 0.0972  data_time: 0.0771  memory: 149  loss: 0.4014  fgnd: 0.2592  bgnd: 0.0128  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:39:49 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_123820
2025/06/23 12:39:49 - mmengine - INFO - Saving checkpoint at 5 epochs
2025/06/23 12:39:49 - mmengine - INFO - Epoch(val)   [5][10/17]    eta: 0:00:00  time: 0.0438  data_time: 0.0356  memory: 149  
2025/06/23 12:39:50 - mmengine - INFO - Epoch(val) [5][17/17]    P: 0.0000  R: 0.0000  F1: 0.0000  data_time: 0.0454  time: 0.0529
2025/06/23 12:39:51 - mmengine - INFO - Epoch(train)   [6][ 10/123]  lr: 2.0000e-02  eta: 0:22:42  time: 0.0894  data_time: 0.0713  memory: 149  loss: 0.3661  fgnd: 0.6120  bgnd: 0.0262  P: 0.8000  R: 0.0455  F1: 0.0860
2025/06/23 12:39:53 - mmengine - INFO - Epoch(train)   [6][ 20/123]  lr: 2.0000e-02  eta: 0:22:53  time: 0.0851  data_time: 0.0654  memory: 149  loss: 0.4176  fgnd: 0.5776  bgnd: 0.0280  P: 0.3333  R: 0.0288  F1: 0.0531
2025/06/23 12:39:55 - mmengine - INFO - Epoch(train)   [6][ 30/123]  lr: 2.0000e-02  eta: 0:23:05  time: 0.1174  data_time: 0.0959  memory: 149  loss: 0.4471  fgnd: 0.4008  bgnd: 0.0190  P: 0.1667  R: 0.0185  F1: 0.0333
2025/06/23 12:39:55 - mmengine - INFO - Epoch(train)   [6][ 40/123]  lr: 2.0000e-02  eta: 0:22:49  time: 0.1202  data_time: 0.0980  memory: 149  loss: 0.4569  fgnd: 0.3307  bgnd: 0.0137  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:39:56 - mmengine - INFO - Epoch(train)   [6][ 50/123]  lr: 2.0000e-02  eta: 0:22:32  time: 0.1208  data_time: 0.0984  memory: 149  loss: 0.4697  fgnd: 0.2843  bgnd: 0.0139  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:39:56 - mmengine - INFO - Epoch(train)   [6][ 60/123]  lr: 2.0000e-02  eta: 0:22:15  time: 0.0917  data_time: 0.0715  memory: 149  loss: 0.4163  fgnd: 0.2821  bgnd: 0.0136  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:39:57 - mmengine - INFO - Epoch(train)   [6][ 70/123]  lr: 2.0000e-02  eta: 0:22:21  time: 0.0870  data_time: 0.0694  memory: 149  loss: 0.3870  fgnd: 0.7680  bgnd: 0.0336  P: 1.0000  R: 0.0661  F1: 0.1240
2025/06/23 12:39:59 - mmengine - INFO - Epoch(train)   [6][ 80/123]  lr: 2.0000e-02  eta: 0:22:25  time: 0.0782  data_time: 0.0590  memory: 149  loss: 0.4098  fgnd: 0.4855  bgnd: 0.0269  P: 0.7143  R: 0.0543  F1: 0.1010
2025/06/23 12:40:01 - mmengine - INFO - Epoch(train)   [6][ 90/123]  lr: 2.0000e-02  eta: 0:22:36  time: 0.1090  data_time: 0.0864  memory: 149  loss: 0.4361  fgnd: 0.4485  bgnd: 0.0184  P: 0.5556  R: 0.1724  F1: 0.2632
2025/06/23 12:40:02 - mmengine - INFO - Epoch(train)   [6][100/123]  lr: 2.0000e-02  eta: 0:22:32  time: 0.1235  data_time: 0.0998  memory: 149  loss: 0.4385  fgnd: 0.3027  bgnd: 0.0134  P: 0.4737  R: 0.2250  F1: 0.3051
2025/06/23 12:40:02 - mmengine - INFO - Epoch(train)   [6][110/123]  lr: 2.0000e-02  eta: 0:22:16  time: 0.1237  data_time: 0.0994  memory: 149  loss: 0.4459  fgnd: 0.2815  bgnd: 0.0135  P: 0.6667  R: 0.0606  F1: 0.1111
2025/06/23 12:40:02 - mmengine - INFO - Epoch(train)   [6][120/123]  lr: 2.0000e-02  eta: 0:22:00  time: 0.0969  data_time: 0.0741  memory: 149  loss: 0.4062  fgnd: 0.2841  bgnd: 0.0136  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:40:02 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_123820
2025/06/23 12:40:02 - mmengine - INFO - Saving checkpoint at 6 epochs
2025/06/23 12:40:03 - mmengine - INFO - Epoch(val)   [6][10/17]    eta: 0:00:00  time: 0.0475  data_time: 0.0395  memory: 149  
2025/06/23 12:40:03 - mmengine - INFO - Epoch(val) [6][17/17]    P: 0.0000  R: 0.0000  F1: 0.0000  data_time: 0.0363  time: 0.0436
2025/06/23 12:40:05 - mmengine - INFO - Epoch(train)   [7][ 10/123]  lr: 2.0000e-02  eta: 0:22:03  time: 0.0890  data_time: 0.0691  memory: 149  loss: 0.3706  fgnd: 0.5150  bgnd: 0.0230  P: 0.6667  R: 0.0274  F1: 0.0526
2025/06/23 12:40:07 - mmengine - INFO - Epoch(train)   [7][ 20/123]  lr: 2.0000e-02  eta: 0:22:12  time: 0.0856  data_time: 0.0638  memory: 149  loss: 0.4194  fgnd: 0.6634  bgnd: 0.0331  P: 0.8333  R: 0.0820  F1: 0.1493
2025/06/23 12:40:09 - mmengine - INFO - Epoch(train)   [7][ 30/123]  lr: 2.0000e-02  eta: 0:22:22  time: 0.1168  data_time: 0.0926  memory: 149  loss: 0.4347  fgnd: 0.3099  bgnd: 0.0145  P: 0.7353  R: 0.4237  F1: 0.5376
2025/06/23 12:40:09 - mmengine - INFO - Epoch(train)   [7][ 40/123]  lr: 2.0000e-02  eta: 0:22:09  time: 0.1201  data_time: 0.0953  memory: 149  loss: 0.4389  fgnd: 0.3264  bgnd: 0.0130  P: 0.6500  R: 0.3095  F1: 0.4194
2025/06/23 12:40:09 - mmengine - INFO - Epoch(train)   [7][ 50/123]  lr: 2.0000e-02  eta: 0:21:55  time: 0.1208  data_time: 0.0949  memory: 149  loss: 0.4514  fgnd: 0.3307  bgnd: 0.0128  P: 0.8571  R: 0.2927  F1: 0.4364
2025/06/23 12:40:10 - mmengine - INFO - Epoch(train)   [7][ 60/123]  lr: 2.0000e-02  eta: 0:21:41  time: 0.0921  data_time: 0.0670  memory: 149  loss: 0.4083  fgnd: 0.2569  bgnd: 0.0127  P: 0.6667  R: 0.2632  F1: 0.3774
2025/06/23 12:40:11 - mmengine - INFO - Epoch(train)   [7][ 70/123]  lr: 2.0000e-02  eta: 0:21:46  time: 0.0871  data_time: 0.0645  memory: 149  loss: 0.3698  fgnd: 0.6983  bgnd: 0.0317  P: 1.0000  R: 0.0877  F1: 0.1613
2025/06/23 12:40:13 - mmengine - INFO - Epoch(train)   [7][ 80/123]  lr: 2.0000e-02  eta: 0:21:50  time: 0.0795  data_time: 0.0564  memory: 149  loss: 0.4071  fgnd: 0.4839  bgnd: 0.0233  P: 0.9091  R: 0.1124  F1: 0.2000
2025/06/23 12:40:15 - mmengine - INFO - Epoch(train)   [7][ 90/123]  lr: 2.0000e-02  eta: 0:22:00  time: 0.1106  data_time: 0.0840  memory: 149  loss: 0.4393  fgnd: 0.3018  bgnd: 0.0146  P: 0.7000  R: 0.2917  F1: 0.4118
2025/06/23 12:40:16 - mmengine - INFO - Epoch(train)   [7][100/123]  lr: 2.0000e-02  eta: 0:21:57  time: 0.1251  data_time: 0.0982  memory: 149  loss: 0.4410  fgnd: 0.3564  bgnd: 0.0128  P: 0.7083  R: 0.3778  F1: 0.4928
2025/06/23 12:40:16 - mmengine - INFO - Epoch(train)   [7][110/123]  lr: 2.0000e-02  eta: 0:21:43  time: 0.1254  data_time: 0.0984  memory: 149  loss: 0.4507  fgnd: 0.2754  bgnd: 0.0128  P: 0.6522  R: 0.3750  F1: 0.4762
2025/06/23 12:40:16 - mmengine - INFO - Epoch(train)   [7][120/123]  lr: 2.0000e-02  eta: 0:21:30  time: 0.0982  data_time: 0.0726  memory: 149  loss: 0.4128  fgnd: 0.2444  bgnd: 0.0127  P: 0.7778  R: 0.4286  F1: 0.5526
2025/06/23 12:40:16 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_123820
2025/06/23 12:40:16 - mmengine - INFO - Saving checkpoint at 7 epochs
2025/06/23 12:40:17 - mmengine - INFO - Epoch(val)   [7][10/17]    eta: 0:00:00  time: 0.0475  data_time: 0.0394  memory: 149  
2025/06/23 12:40:17 - mmengine - INFO - Epoch(val) [7][17/17]    P: 0.0445  R: 0.0945  F1: 0.0605  data_time: 0.0357  time: 0.0446
2025/06/23 12:40:19 - mmengine - INFO - Epoch(train)   [8][ 10/123]  lr: 2.0000e-02  eta: 0:21:32  time: 0.0908  data_time: 0.0674  memory: 149  loss: 0.3626  fgnd: 0.6006  bgnd: 0.0277  P: 0.7500  R: 0.1622  F1: 0.2667
2025/06/23 12:40:21 - mmengine - INFO - Epoch(train)   [8][ 20/123]  lr: 2.0000e-02  eta: 0:21:40  time: 0.0854  data_time: 0.0603  memory: 149  loss: 0.4043  fgnd: 0.5327  bgnd: 0.0298  P: 0.7895  R: 0.1271  F1: 0.2190
2025/06/23 12:40:23 - mmengine - INFO - Epoch(train)   [8][ 30/123]  lr: 2.0000e-02  eta: 0:21:49  time: 0.1170  data_time: 0.0904  memory: 149  loss: 0.4110  fgnd: 0.2767  bgnd: 0.0148  P: 0.5333  R: 0.3077  F1: 0.3902
2025/06/23 12:40:23 - mmengine - INFO - Epoch(train)   [8][ 40/123]  lr: 2.0000e-02  eta: 0:21:38  time: 0.1211  data_time: 0.0941  memory: 149  loss: 0.4096  fgnd: 0.2743  bgnd: 0.0123  P: 0.0938  R: 0.0857  F1: 0.0896
2025/06/23 12:40:23 - mmengine - INFO - Epoch(train)   [8][ 50/123]  lr: 2.0000e-02  eta: 0:21:26  time: 0.1216  data_time: 0.0944  memory: 149  loss: 0.4144  fgnd: 0.2485  bgnd: 0.0123  P: 0.6786  R: 0.4043  F1: 0.5067
2025/06/23 12:40:24 - mmengine - INFO - Epoch(train)   [8][ 60/123]  lr: 2.0000e-02  eta: 0:21:14  time: 0.0923  data_time: 0.0674  memory: 149  loss: 0.3741  fgnd: 0.2215  bgnd: 0.0118  P: 0.6053  R: 0.4600  F1: 0.5227
2025/06/23 12:40:25 - mmengine - INFO - Epoch(train)   [8][ 70/123]  lr: 2.0000e-02  eta: 0:21:18  time: 0.0867  data_time: 0.0643  memory: 149  loss: 0.3359  fgnd: 0.5390  bgnd: 0.0241  P: 0.5938  R: 0.2000  F1: 0.2992
2025/06/23 12:40:27 - mmengine - INFO - Epoch(train)   [8][ 80/123]  lr: 2.0000e-02  eta: 0:21:21  time: 0.0786  data_time: 0.0546  memory: 149  loss: 0.3791  fgnd: 0.6001  bgnd: 0.0298  P: 0.8378  R: 0.2441  F1: 0.3780
2025/06/23 12:40:29 - mmengine - INFO - Epoch(train)   [8][ 90/123]  lr: 2.0000e-02  eta: 0:21:29  time: 0.1078  data_time: 0.0820  memory: 149  loss: 0.3998  fgnd: 0.3155  bgnd: 0.0140  P: 0.6939  R: 0.5075  F1: 0.5862
2025/06/23 12:40:29 - mmengine - INFO - Epoch(train)   [8][100/123]  lr: 2.0000e-02  eta: 0:21:26  time: 0.1225  data_time: 0.0957  memory: 149  loss: 0.4020  fgnd: 0.2983  bgnd: 0.0130  P: 0.5909  R: 0.5200  F1: 0.5532
2025/06/23 12:40:30 - mmengine - INFO - Epoch(train)   [8][110/123]  lr: 2.0000e-02  eta: 0:21:14  time: 0.1227  data_time: 0.0957  memory: 149  loss: 0.4098  fgnd: 0.2685  bgnd: 0.0129  P: 0.7353  R: 0.5319  F1: 0.6173
2025/06/23 12:40:30 - mmengine - INFO - Epoch(train)   [8][120/123]  lr: 2.0000e-02  eta: 0:21:03  time: 0.0971  data_time: 0.0718  memory: 149  loss: 0.3764  fgnd: 0.2557  bgnd: 0.0122  P: 0.8400  R: 0.4286  F1: 0.5676
2025/06/23 12:40:30 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_123820
2025/06/23 12:40:30 - mmengine - INFO - Saving checkpoint at 8 epochs
2025/06/23 12:40:31 - mmengine - INFO - Epoch(val)   [8][10/17]    eta: 0:00:00  time: 0.0438  data_time: 0.0354  memory: 149  
2025/06/23 12:40:31 - mmengine - INFO - Epoch(val) [8][17/17]    P: 0.0136  R: 0.0253  F1: 0.0177  data_time: 0.0351  time: 0.0438
2025/06/23 12:40:33 - mmengine - INFO - Epoch(train)   [9][ 10/123]  lr: 2.0000e-02  eta: 0:21:05  time: 0.0908  data_time: 0.0682  memory: 149  loss: 0.3313  fgnd: 0.5537  bgnd: 0.0257  P: 0.7500  R: 0.2330  F1: 0.3556
2025/06/23 12:40:34 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_123820
2025/06/23 12:40:34 - mmengine - INFO - Epoch(train)   [9][ 20/123]  lr: 2.0000e-02  eta: 0:21:12  time: 0.0860  data_time: 0.0613  memory: 149  loss: 0.3750  fgnd: 0.5713  bgnd: 0.0309  P: 0.7692  R: 0.0870  F1: 0.1562
2025/06/23 12:40:36 - mmengine - INFO - Epoch(train)   [9][ 30/123]  lr: 2.0000e-02  eta: 0:21:20  time: 0.1184  data_time: 0.0915  memory: 149  loss: 0.3887  fgnd: 0.2958  bgnd: 0.0151  P: 0.6316  R: 0.3429  F1: 0.4444
2025/06/23 12:40:37 - mmengine - INFO - Epoch(train)   [9][ 40/123]  lr: 2.0000e-02  eta: 0:21:11  time: 0.1224  data_time: 0.0952  memory: 149  loss: 0.3892  fgnd: 0.2853  bgnd: 0.0123  P: 0.5385  R: 0.7206  F1: 0.6164
2025/06/23 12:40:37 - mmengine - INFO - Epoch(train)   [9][ 50/123]  lr: 2.0000e-02  eta: 0:21:00  time: 0.1227  data_time: 0.0954  memory: 149  loss: 0.3957  fgnd: 0.2395  bgnd: 0.0120  P: 0.7246  R: 0.7353  F1: 0.7299
2025/06/23 12:40:37 - mmengine - INFO - Epoch(train)   [9][ 60/123]  lr: 2.0000e-02  eta: 0:20:50  time: 0.0928  data_time: 0.0675  memory: 149  loss: 0.3520  fgnd: 0.2004  bgnd: 0.0118  P: 0.8182  R: 0.5192  F1: 0.6353
2025/06/23 12:40:39 - mmengine - INFO - Epoch(train)   [9][ 70/123]  lr: 2.0000e-02  eta: 0:20:53  time: 0.0880  data_time: 0.0646  memory: 149  loss: 0.3257  fgnd: 0.5602  bgnd: 0.0256  P: 0.8378  R: 0.2696  F1: 0.4079
2025/06/23 12:40:40 - mmengine - INFO - Epoch(train)   [9][ 80/123]  lr: 2.0000e-02  eta: 0:20:56  time: 0.0788  data_time: 0.0546  memory: 149  loss: 0.3558  fgnd: 0.4623  bgnd: 0.0259  P: 0.8889  R: 0.1569  F1: 0.2667
2025/06/23 12:40:42 - mmengine - INFO - Epoch(train)   [9][ 90/123]  lr: 2.0000e-02  eta: 0:21:03  time: 0.1086  data_time: 0.0808  memory: 149  loss: 0.3830  fgnd: 0.3779  bgnd: 0.0159  P: 0.7692  R: 0.3226  F1: 0.4545
2025/06/23 12:40:43 - mmengine - INFO - Epoch(train)   [9][100/123]  lr: 2.0000e-02  eta: 0:21:01  time: 0.1234  data_time: 0.0955  memory: 149  loss: 0.3870  fgnd: 0.2953  bgnd: 0.0125  P: 0.5397  R: 0.5574  F1: 0.5484
2025/06/23 12:40:44 - mmengine - INFO - Epoch(train)   [9][110/123]  lr: 2.0000e-02  eta: 0:20:51  time: 0.1238  data_time: 0.0958  memory: 149  loss: 0.3984  fgnd: 0.2671  bgnd: 0.0121  P: 0.3793  R: 0.6667  F1: 0.4835
2025/06/23 12:40:44 - mmengine - INFO - Epoch(train)   [9][120/123]  lr: 2.0000e-02  eta: 0:20:40  time: 0.0973  data_time: 0.0716  memory: 149  loss: 0.3639  fgnd: 0.2173  bgnd: 0.0122  P: 0.5158  R: 0.6622  F1: 0.5799
2025/06/23 12:40:44 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_123820
2025/06/23 12:40:44 - mmengine - INFO - Saving checkpoint at 9 epochs
2025/06/23 12:40:45 - mmengine - INFO - Epoch(val)   [9][10/17]    eta: 0:00:00  time: 0.0445  data_time: 0.0347  memory: 149  
2025/06/23 12:40:45 - mmengine - INFO - Epoch(val) [9][17/17]    P: 0.0355  R: 0.2128  F1: 0.0609  data_time: 0.0322  time: 0.0442
2025/06/23 12:40:46 - mmengine - INFO - Epoch(train)  [10][ 10/123]  lr: 2.0000e-02  eta: 0:20:42  time: 0.0905  data_time: 0.0667  memory: 149  loss: 0.3279  fgnd: 0.4758  bgnd: 0.0225  P: 0.6875  R: 0.2444  F1: 0.3607
2025/06/23 12:40:48 - mmengine - INFO - Epoch(train)  [10][ 20/123]  lr: 2.0000e-02  eta: 0:20:48  time: 0.0851  data_time: 0.0603  memory: 149  loss: 0.3725  fgnd: 0.5915  bgnd: 0.0292  P: 0.8182  R: 0.0841  F1: 0.1525
2025/06/23 12:40:50 - mmengine - INFO - Epoch(train)  [10][ 30/123]  lr: 2.0000e-02  eta: 0:20:54  time: 0.1170  data_time: 0.0899  memory: 149  loss: 0.3879  fgnd: 0.3228  bgnd: 0.0166  P: 0.8148  R: 0.3188  F1: 0.4583
2025/06/23 12:40:51 - mmengine - INFO - Epoch(train)  [10][ 40/123]  lr: 2.0000e-02  eta: 0:20:46  time: 0.1205  data_time: 0.0932  memory: 149  loss: 0.3906  fgnd: 0.3122  bgnd: 0.0130  P: 0.7429  R: 0.7324  F1: 0.7376
2025/06/23 12:40:51 - mmengine - INFO - Epoch(train)  [10][ 50/123]  lr: 2.0000e-02  eta: 0:20:37  time: 0.1210  data_time: 0.0934  memory: 149  loss: 0.4005  fgnd: 0.2633  bgnd: 0.0128  P: 0.5794  R: 0.7654  F1: 0.6596
2025/06/23 12:40:51 - mmengine - INFO - Epoch(train)  [10][ 60/123]  lr: 2.0000e-02  eta: 0:20:27  time: 0.0918  data_time: 0.0668  memory: 149  loss: 0.3636  fgnd: 0.2183  bgnd: 0.0128  P: 0.6282  R: 0.7101  F1: 0.6667
2025/06/23 12:40:53 - mmengine - INFO - Epoch(train)  [10][ 70/123]  lr: 2.0000e-02  eta: 0:20:31  time: 0.0876  data_time: 0.0643  memory: 149  loss: 0.3281  fgnd: 0.6126  bgnd: 0.0271  P: 0.8605  R: 0.3162  F1: 0.4625
2025/06/23 12:40:54 - mmengine - INFO - Epoch(train)  [10][ 80/123]  lr: 2.0000e-02  eta: 0:20:33  time: 0.0792  data_time: 0.0550  memory: 149  loss: 0.3636  fgnd: 0.4608  bgnd: 0.0242  P: 0.9333  R: 0.1687  F1: 0.2857
2025/06/23 12:40:56 - mmengine - INFO - Epoch(train)  [10][ 90/123]  lr: 2.0000e-02  eta: 0:20:40  time: 0.1096  data_time: 0.0830  memory: 149  loss: 0.3862  fgnd: 0.3145  bgnd: 0.0153  P: 0.6667  R: 0.1961  F1: 0.3030
2025/06/23 12:40:57 - mmengine - INFO - Epoch(train)  [10][100/123]  lr: 2.0000e-02  eta: 0:20:37  time: 0.1245  data_time: 0.0980  memory: 149  loss: 0.3850  fgnd: 0.2717  bgnd: 0.0124  P: 0.5254  R: 0.5536  F1: 0.5391
2025/06/23 12:40:57 - mmengine - INFO - Epoch(train)  [10][110/123]  lr: 2.0000e-02  eta: 0:20:28  time: 0.1250  data_time: 0.0982  memory: 149  loss: 0.3915  fgnd: 0.2902  bgnd: 0.0128  P: 0.1984  R: 0.4902  F1: 0.2825
2025/06/23 12:40:58 - mmengine - INFO - Epoch(train)  [10][120/123]  lr: 2.0000e-02  eta: 0:20:20  time: 0.0984  data_time: 0.0733  memory: 149  loss: 0.3604  fgnd: 0.2475  bgnd: 0.0118  P: 0.0661  R: 0.2000  F1: 0.0994
2025/06/23 12:40:58 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_123820
2025/06/23 12:40:58 - mmengine - INFO - Saving checkpoint at 10 epochs
2025/06/23 12:40:59 - mmengine - INFO - Epoch(val)  [10][10/17]    eta: 0:00:00  time: 0.0445  data_time: 0.0335  memory: 149  
2025/06/23 12:40:59 - mmengine - INFO - Epoch(val) [10][17/17]    P: 0.0335  R: 0.2130  F1: 0.0579  data_time: 0.0327  time: 0.0445
2025/06/23 12:41:00 - mmengine - INFO - Epoch(train)  [11][ 10/123]  lr: 2.0000e-02  eta: 0:20:21  time: 0.0900  data_time: 0.0673  memory: 149  loss: 0.3228  fgnd: 0.4292  bgnd: 0.0202  P: 0.6456  R: 0.5000  F1: 0.5635
2025/06/23 12:41:02 - mmengine - INFO - Epoch(train)  [11][ 20/123]  lr: 2.0000e-02  eta: 0:20:26  time: 0.0858  data_time: 0.0600  memory: 149  loss: 0.3613  fgnd: 0.3884  bgnd: 0.0214  P: 0.8333  R: 0.1852  F1: 0.3030
2025/06/23 12:41:04 - mmengine - INFO - Epoch(train)  [11][ 30/123]  lr: 2.0000e-02  eta: 0:20:32  time: 0.1172  data_time: 0.0893  memory: 149  loss: 0.3844  fgnd: 0.3230  bgnd: 0.0156  P: 0.7419  R: 0.3594  F1: 0.4842
2025/06/23 12:41:05 - mmengine - INFO - Epoch(train)  [11][ 40/123]  lr: 2.0000e-02  eta: 0:20:24  time: 0.1202  data_time: 0.0904  memory: 149  loss: 0.3883  fgnd: 0.2842  bgnd: 0.0120  P: 0.5000  R: 0.7465  F1: 0.5989
2025/06/23 12:41:05 - mmengine - INFO - Epoch(train)  [11][ 50/123]  lr: 2.0000e-02  eta: 0:20:16  time: 0.1204  data_time: 0.0906  memory: 149  loss: 0.3953  fgnd: 0.2822  bgnd: 0.0126  P: 0.5957  R: 0.8485  F1: 0.7000
2025/06/23 12:41:05 - mmengine - INFO - Epoch(train)  [11][ 60/123]  lr: 2.0000e-02  eta: 0:20:07  time: 0.0916  data_time: 0.0639  memory: 149  loss: 0.3548  fgnd: 0.1961  bgnd: 0.0114  P: 0.6143  R: 0.8431  F1: 0.7107
2025/06/23 12:41:07 - mmengine - INFO - Epoch(train)  [11][ 70/123]  lr: 2.0000e-02  eta: 0:20:10  time: 0.0860  data_time: 0.0608  memory: 149  loss: 0.3284  fgnd: 0.5639  bgnd: 0.0281  P: 0.6771  R: 0.4545  F1: 0.5439
2025/06/23 12:41:08 - mmengine - INFO - Epoch(train)  [11][ 80/123]  lr: 2.0000e-02  eta: 0:20:12  time: 0.0781  data_time: 0.0514  memory: 149  loss: 0.3554  fgnd: 0.3826  bgnd: 0.0263  P: 0.3750  R: 0.1200  F1: 0.1818
2025/06/23 12:41:10 - mmengine - INFO - Epoch(train)  [11][ 90/123]  lr: 2.0000e-02  eta: 0:20:18  time: 0.1079  data_time: 0.0812  memory: 149  loss: 0.3740  fgnd: 0.3077  bgnd: 0.0154  P: 0.5263  R: 0.1818  F1: 0.2703
2025/06/23 12:41:11 - mmengine - INFO - Epoch(train)  [11][100/123]  lr: 2.0000e-02  eta: 0:20:15  time: 0.1229  data_time: 0.0961  memory: 149  loss: 0.3756  fgnd: 0.2989  bgnd: 0.0124  P: 0.3226  R: 0.4167  F1: 0.3636
2025/06/23 12:41:11 - mmengine - INFO - Epoch(train)  [11][110/123]  lr: 2.0000e-02  eta: 0:20:07  time: 0.1232  data_time: 0.0964  memory: 149  loss: 0.3819  fgnd: 0.2608  bgnd: 0.0126  P: 0.6224  R: 0.7722  F1: 0.6893
2025/06/23 12:41:11 - mmengine - INFO - Epoch(train)  [11][120/123]  lr: 2.0000e-02  eta: 0:19:59  time: 0.0977  data_time: 0.0728  memory: 149  loss: 0.3517  fgnd: 0.2547  bgnd: 0.0129  P: 0.5726  R: 0.7978  F1: 0.6667
2025/06/23 12:41:11 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_123820
2025/06/23 12:41:11 - mmengine - INFO - Saving checkpoint at 11 epochs
2025/06/23 12:41:12 - mmengine - INFO - Epoch(val)  [11][10/17]    eta: 0:00:00  time: 0.0445  data_time: 0.0328  memory: 149  
2025/06/23 12:41:12 - mmengine - INFO - Epoch(val) [11][17/17]    P: 0.0328  R: 0.1341  F1: 0.0528  data_time: 0.0340  time: 0.0447
2025/06/23 12:41:14 - mmengine - INFO - Epoch(train)  [12][ 10/123]  lr: 2.0000e-02  eta: 0:20:00  time: 0.0899  data_time: 0.0676  memory: 149  loss: 0.3200  fgnd: 0.5287  bgnd: 0.0242  P: 0.6364  R: 0.4628  F1: 0.5359
2025/06/23 12:41:16 - mmengine - INFO - Epoch(train)  [12][ 20/123]  lr: 2.0000e-02  eta: 0:20:05  time: 0.0849  data_time: 0.0597  memory: 149  loss: 0.3511  fgnd: 0.4764  bgnd: 0.0338  P: 0.8333  R: 0.1799  F1: 0.2959
2025/06/23 12:41:18 - mmengine - INFO - Epoch(train)  [12][ 30/123]  lr: 2.0000e-02  eta: 0:20:10  time: 0.1165  data_time: 0.0887  memory: 149  loss: 0.3617  fgnd: 0.2561  bgnd: 0.0140  P: 0.4490  R: 0.3929  F1: 0.4190
2025/06/23 12:41:18 - mmengine - INFO - Epoch(train)  [12][ 40/123]  lr: 2.0000e-02  eta: 0:20:03  time: 0.1195  data_time: 0.0917  memory: 149  loss: 0.3614  fgnd: 0.2709  bgnd: 0.0119  P: 0.4206  R: 0.6522  F1: 0.5114
2025/06/23 12:41:18 - mmengine - INFO - Epoch(train)  [12][ 50/123]  lr: 2.0000e-02  eta: 0:19:55  time: 0.1199  data_time: 0.0917  memory: 149  loss: 0.3670  fgnd: 0.2375  bgnd: 0.0124  P: 0.5639  R: 0.8152  F1: 0.6667
2025/06/23 12:41:19 - mmengine - INFO - Epoch(train)  [12][ 60/123]  lr: 2.0000e-02  eta: 0:19:48  time: 0.0907  data_time: 0.0648  memory: 149  loss: 0.3253  fgnd: 0.2058  bgnd: 0.0120  P: 0.6198  R: 0.7895  F1: 0.6944
2025/06/23 12:41:20 - mmengine - INFO - Epoch(train)  [12][ 70/123]  lr: 2.0000e-02  eta: 0:19:50  time: 0.0867  data_time: 0.0628  memory: 149  loss: 0.3120  fgnd: 0.6852  bgnd: 0.0285  P: 0.7123  R: 0.3824  F1: 0.4976
2025/06/23 12:41:22 - mmengine - INFO - Epoch(train)  [12][ 80/123]  lr: 2.0000e-02  eta: 0:19:52  time: 0.0779  data_time: 0.0534  memory: 149  loss: 0.3405  fgnd: 0.3870  bgnd: 0.0245  P: 0.8333  R: 0.2451  F1: 0.3788
2025/06/23 12:41:24 - mmengine - INFO - Epoch(train)  [12][ 90/123]  lr: 2.0000e-02  eta: 0:19:57  time: 0.1082  data_time: 0.0806  memory: 149  loss: 0.3622  fgnd: 0.3066  bgnd: 0.0157  P: 0.3830  R: 0.3103  F1: 0.3429
2025/06/23 12:41:25 - mmengine - INFO - Epoch(train)  [12][100/123]  lr: 2.0000e-02  eta: 0:19:55  time: 0.1229  data_time: 0.0953  memory: 149  loss: 0.3641  fgnd: 0.2696  bgnd: 0.0124  P: 0.2708  R: 0.5098  F1: 0.3537
2025/06/23 12:41:25 - mmengine - INFO - Epoch(train)  [12][110/123]  lr: 2.0000e-02  eta: 0:19:47  time: 0.1233  data_time: 0.0957  memory: 149  loss: 0.3720  fgnd: 0.2608  bgnd: 0.0124  P: 0.4540  R: 0.8681  F1: 0.5962
2025/06/23 12:41:25 - mmengine - INFO - Epoch(train)  [12][120/123]  lr: 2.0000e-02  eta: 0:19:40  time: 0.0966  data_time: 0.0708  memory: 149  loss: 0.3358  fgnd: 0.2383  bgnd: 0.0123  P: 0.4460  R: 0.7470  F1: 0.5586
2025/06/23 12:41:25 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_123820
2025/06/23 12:41:25 - mmengine - INFO - Saving checkpoint at 12 epochs
2025/06/23 12:41:26 - mmengine - INFO - Epoch(val)  [12][10/17]    eta: 0:00:00  time: 0.0451  data_time: 0.0335  memory: 149  
2025/06/23 12:41:26 - mmengine - INFO - Epoch(val) [12][17/17]    P: 0.0153  R: 0.0732  F1: 0.0254  data_time: 0.0342  time: 0.0449
2025/06/23 12:41:28 - mmengine - INFO - Epoch(train)  [13][ 10/123]  lr: 2.0000e-02  eta: 0:19:41  time: 0.0904  data_time: 0.0667  memory: 149  loss: 0.3020  fgnd: 0.3836  bgnd: 0.0220  P: 0.6883  R: 0.4818  F1: 0.5668
2025/06/23 12:41:30 - mmengine - INFO - Epoch(train)  [13][ 20/123]  lr: 2.0000e-02  eta: 0:19:45  time: 0.0858  data_time: 0.0602  memory: 149  loss: 0.3356  fgnd: 0.4875  bgnd: 0.0281  P: 0.8571  R: 0.2400  F1: 0.3750
2025/06/23 12:41:32 - mmengine - INFO - Epoch(train)  [13][ 30/123]  lr: 2.0000e-02  eta: 0:19:50  time: 0.1177  data_time: 0.0899  memory: 149  loss: 0.3512  fgnd: 0.3116  bgnd: 0.0167  P: 0.4746  R: 0.4058  F1: 0.4375
2025/06/23 12:41:32 - mmengine - INFO - Epoch(train)  [13][ 40/123]  lr: 2.0000e-02  eta: 0:19:44  time: 0.1211  data_time: 0.0930  memory: 149  loss: 0.3497  fgnd: 0.2475  bgnd: 0.0121  P: 0.4310  R: 0.7042  F1: 0.5348
2025/06/23 12:41:32 - mmengine - INFO - Epoch(train)  [13][ 50/123]  lr: 2.0000e-02  eta: 0:19:37  time: 0.1215  data_time: 0.0930  memory: 149  loss: 0.3567  fgnd: 0.2573  bgnd: 0.0124  P: 0.4891  R: 0.7882  F1: 0.6036
2025/06/23 12:41:32 - mmengine - INFO - Epoch(train)  [13][ 60/123]  lr: 2.0000e-02  eta: 0:19:29  time: 0.0917  data_time: 0.0656  memory: 149  loss: 0.3234  fgnd: 0.1939  bgnd: 0.0118  P: 0.5185  R: 0.8235  F1: 0.6364
2025/06/23 12:41:34 - mmengine - INFO - Epoch(train)  [13][ 70/123]  lr: 2.0000e-02  eta: 0:19:32  time: 0.0871  data_time: 0.0630  memory: 149  loss: 0.3094  fgnd: 0.5547  bgnd: 0.0252  P: 0.7143  R: 0.5245  F1: 0.6048
2025/06/23 12:41:35 - mmengine - INFO - Epoch(train)  [13][ 80/123]  lr: 2.0000e-02  eta: 0:19:33  time: 0.0783  data_time: 0.0534  memory: 149  loss: 0.3296  fgnd: 0.3020  bgnd: 0.0221  P: 0.8627  R: 0.4112  F1: 0.5570
2025/06/23 12:41:37 - mmengine - INFO - Epoch(train)  [13][ 90/123]  lr: 2.0000e-02  eta: 0:19:38  time: 0.1089  data_time: 0.0810  memory: 149  loss: 0.3524  fgnd: 0.3232  bgnd: 0.0165  P: 0.3000  R: 0.2903  F1: 0.2951
2025/06/23 12:41:38 - mmengine - INFO - Epoch(train)  [13][100/123]  lr: 2.0000e-02  eta: 0:19:36  time: 0.1241  data_time: 0.0962  memory: 149  loss: 0.3544  fgnd: 0.2392  bgnd: 0.0114  P: 0.3813  R: 0.7794  F1: 0.5121
2025/06/23 12:41:39 - mmengine - INFO - Epoch(train)  [13][110/123]  lr: 2.0000e-02  eta: 0:19:29  time: 0.1243  data_time: 0.0965  memory: 149  loss: 0.3629  fgnd: 0.2598  bgnd: 0.0122  P: 0.3701  R: 0.7917  F1: 0.5044
2025/06/23 12:41:39 - mmengine - INFO - Epoch(train)  [13][120/123]  lr: 2.0000e-02  eta: 0:19:22  time: 0.0977  data_time: 0.0720  memory: 149  loss: 0.3274  fgnd: 0.2131  bgnd: 0.0117  P: 0.5535  R: 0.8302  F1: 0.6642
2025/06/23 12:41:39 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_123820
2025/06/23 12:41:39 - mmengine - INFO - Saving checkpoint at 13 epochs
2025/06/23 12:41:40 - mmengine - INFO - Epoch(val)  [13][10/17]    eta: 0:00:00  time: 0.0449  data_time: 0.0335  memory: 149  
2025/06/23 12:41:40 - mmengine - INFO - Epoch(val) [13][17/17]    P: 0.0302  R: 0.1519  F1: 0.0504  data_time: 0.0322  time: 0.0440
2025/06/23 12:41:42 - mmengine - INFO - Epoch(train)  [14][ 10/123]  lr: 2.0000e-02  eta: 0:19:23  time: 0.0905  data_time: 0.0666  memory: 149  loss: 0.3058  fgnd: 0.4564  bgnd: 0.0255  P: 0.6803  R: 0.5497  F1: 0.6081
2025/06/23 12:41:43 - mmengine - INFO - Epoch(train)  [14][ 20/123]  lr: 2.0000e-02  eta: 0:19:27  time: 0.0858  data_time: 0.0592  memory: 149  loss: 0.3388  fgnd: 0.5117  bgnd: 0.0279  P: 0.7463  R: 0.3788  F1: 0.5025
2025/06/23 12:41:45 - mmengine - INFO - Epoch(train)  [14][ 30/123]  lr: 2.0000e-02  eta: 0:19:31  time: 0.1175  data_time: 0.0895  memory: 149  loss: 0.3447  fgnd: 0.2540  bgnd: 0.0133  P: 0.4947  R: 0.6620  F1: 0.5663
2025/06/23 12:41:46 - mmengine - INFO - Epoch(train)  [14][ 40/123]  lr: 2.0000e-02  eta: 0:19:26  time: 0.1208  data_time: 0.0930  memory: 149  loss: 0.3446  fgnd: 0.2406  bgnd: 0.0120  P: 0.3000  R: 0.5882  F1: 0.3974
2025/06/23 12:41:46 - mmengine - INFO - Epoch(train)  [14][ 50/123]  lr: 2.0000e-02  eta: 0:19:19  time: 0.1212  data_time: 0.0934  memory: 149  loss: 0.3506  fgnd: 0.2453  bgnd: 0.0123  P: 0.5508  R: 0.7831  F1: 0.6468
2025/06/23 12:41:46 - mmengine - INFO - Epoch(train)  [14][ 60/123]  lr: 2.0000e-02  eta: 0:19:13  time: 0.0923  data_time: 0.0671  memory: 149  loss: 0.3126  fgnd: 0.2059  bgnd: 0.0117  P: 0.6204  R: 0.8586  F1: 0.7203
2025/06/23 12:41:48 - mmengine - INFO - Epoch(train)  [14][ 70/123]  lr: 2.0000e-02  eta: 0:19:14  time: 0.0873  data_time: 0.0650  memory: 149  loss: 0.2849  fgnd: 0.4448  bgnd: 0.0224  P: 0.6531  R: 0.6486  F1: 0.6508
2025/06/23 12:41:49 - mmengine - INFO - Epoch(train)  [14][ 80/123]  lr: 2.0000e-02  eta: 0:19:16  time: 0.0785  data_time: 0.0540  memory: 149  loss: 0.3164  fgnd: 0.3649  bgnd: 0.0215  P: 0.7705  R: 0.4563  F1: 0.5732
2025/06/23 12:41:51 - mmengine - INFO - Epoch(train)  [14][ 90/123]  lr: 2.0000e-02  eta: 0:19:20  time: 0.1080  data_time: 0.0804  memory: 149  loss: 0.3352  fgnd: 0.2944  bgnd: 0.0126  P: 0.3368  R: 0.5079  F1: 0.4051
2025/06/23 12:41:52 - mmengine - INFO - Epoch(train)  [14][100/123]  lr: 2.0000e-02  eta: 0:19:18  time: 0.1225  data_time: 0.0947  memory: 149  loss: 0.3379  fgnd: 0.3058  bgnd: 0.0119  P: 0.2105  R: 0.4082  F1: 0.2778
2025/06/23 12:41:52 - mmengine - INFO - Epoch(train)  [14][110/123]  lr: 2.0000e-02  eta: 0:19:12  time: 0.1226  data_time: 0.0950  memory: 149  loss: 0.3466  fgnd: 0.2442  bgnd: 0.0122  P: 0.4590  R: 0.8400  F1: 0.5936
2025/06/23 12:41:52 - mmengine - INFO - Epoch(train)  [14][120/123]  lr: 2.0000e-02  eta: 0:19:05  time: 0.0960  data_time: 0.0702  memory: 149  loss: 0.3219  fgnd: 0.2019  bgnd: 0.0118  P: 0.5774  R: 0.8661  F1: 0.6929
2025/06/23 12:41:53 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_123820
2025/06/23 12:41:53 - mmengine - INFO - Saving checkpoint at 14 epochs
2025/06/23 12:41:53 - mmengine - INFO - Epoch(val)  [14][10/17]    eta: 0:00:00  time: 0.0448  data_time: 0.0336  memory: 149  
2025/06/23 12:41:54 - mmengine - INFO - Epoch(val) [14][17/17]    P: 0.0227  R: 0.1067  F1: 0.0374  data_time: 0.0349  time: 0.0449
2025/06/23 12:41:55 - mmengine - INFO - Epoch(train)  [15][ 10/123]  lr: 2.0000e-02  eta: 0:19:06  time: 0.0898  data_time: 0.0670  memory: 149  loss: 0.2947  fgnd: 0.5774  bgnd: 0.0258  P: 0.6914  R: 0.4444  F1: 0.5411
2025/06/23 12:41:57 - mmengine - INFO - Epoch(train)  [15][ 20/123]  lr: 2.0000e-02  eta: 0:19:09  time: 0.0856  data_time: 0.0599  memory: 149  loss: 0.3176  fgnd: 0.4353  bgnd: 0.0274  P: 0.8276  R: 0.3664  F1: 0.5079
2025/06/23 12:41:59 - mmengine - INFO - Epoch(train)  [15][ 30/123]  lr: 2.0000e-02  eta: 0:19:13  time: 0.1174  data_time: 0.0887  memory: 149  loss: 0.3332  fgnd: 0.2607  bgnd: 0.0128  P: 0.3978  R: 0.6066  F1: 0.4805
2025/06/23 12:42:00 - mmengine - INFO - Epoch(train)  [15][ 40/123]  lr: 2.0000e-02  eta: 0:19:08  time: 0.1204  data_time: 0.0915  memory: 149  loss: 0.3303  fgnd: 0.2279  bgnd: 0.0117  P: 0.3759  R: 0.7143  F1: 0.4926
2025/06/23 12:42:00 - mmengine - INFO - Epoch(train)  [15][ 50/123]  lr: 2.0000e-02  eta: 0:19:02  time: 0.1209  data_time: 0.0917  memory: 149  loss: 0.3375  fgnd: 0.2251  bgnd: 0.0117  P: 0.5066  R: 0.8280  F1: 0.6286
2025/06/23 12:42:00 - mmengine - INFO - Epoch(train)  [15][ 60/123]  lr: 2.0000e-02  eta: 0:18:55  time: 0.0915  data_time: 0.0647  memory: 149  loss: 0.3025  fgnd: 0.2002  bgnd: 0.0115  P: 0.4886  R: 0.8431  F1: 0.6187
2025/06/23 12:42:02 - mmengine - INFO - Epoch(train)  [15][ 70/123]  lr: 2.0000e-02  eta: 0:18:57  time: 0.0866  data_time: 0.0621  memory: 149  loss: 0.2968  fgnd: 0.5361  bgnd: 0.0274  P: 0.7387  R: 0.5290  F1: 0.6165
2025/06/23 12:42:03 - mmengine - INFO - Epoch(train)  [15][ 80/123]  lr: 2.0000e-02  eta: 0:18:58  time: 0.0776  data_time: 0.0525  memory: 149  loss: 0.3150  fgnd: 0.3805  bgnd: 0.0270  P: 0.8611  R: 0.4526  F1: 0.5933
2025/06/23 12:42:05 - mmengine - INFO - Epoch(train)  [15][ 90/123]  lr: 2.0000e-02  eta: 0:19:02  time: 0.1079  data_time: 0.0796  memory: 149  loss: 0.3332  fgnd: 0.2868  bgnd: 0.0135  P: 0.4375  R: 0.5385  F1: 0.4828
2025/06/23 12:42:06 - mmengine - INFO - Epoch(train)  [15][100/123]  lr: 2.0000e-02  eta: 0:19:00  time: 0.1225  data_time: 0.0942  memory: 149  loss: 0.3331  fgnd: 0.2711  bgnd: 0.0120  P: 0.2328  R: 0.5294  F1: 0.3234
2025/06/23 12:42:06 - mmengine - INFO - Epoch(train)  [15][110/123]  lr: 2.0000e-02  eta: 0:18:54  time: 0.1228  data_time: 0.0942  memory: 149  loss: 0.3398  fgnd: 0.2348  bgnd: 0.0123  P: 0.3381  R: 0.6912  F1: 0.4541
2025/06/23 12:42:06 - mmengine - INFO - Epoch(train)  [15][120/123]  lr: 2.0000e-02  eta: 0:18:48  time: 0.0963  data_time: 0.0701  memory: 149  loss: 0.3057  fgnd: 0.1938  bgnd: 0.0116  P: 0.5503  R: 0.8632  F1: 0.6721
2025/06/23 12:42:07 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_123820
2025/06/23 12:42:07 - mmengine - INFO - Saving checkpoint at 15 epochs
2025/06/23 12:42:08 - mmengine - INFO - Epoch(val)  [15][10/17]    eta: 0:00:00  time: 0.0450  data_time: 0.0335  memory: 149  
2025/06/23 12:42:08 - mmengine - INFO - Epoch(val) [15][17/17]    P: 0.0470  R: 0.2651  F1: 0.0799  data_time: 0.0335  time: 0.0456
2025/06/23 12:42:10 - mmengine - INFO - Epoch(train)  [16][ 10/123]  lr: 2.0000e-02  eta: 0:18:52  time: 0.0989  data_time: 0.0662  memory: 149  loss: 0.2841  fgnd: 0.5039  bgnd: 0.0258  P: 0.6486  R: 0.5143  F1: 0.5737
2025/06/23 12:42:11 - mmengine - INFO - Epoch(train)  [16][ 20/123]  lr: 2.0000e-02  eta: 0:18:54  time: 0.0946  data_time: 0.0595  memory: 149  loss: 0.3117  fgnd: 0.4632  bgnd: 0.0286  P: 0.7527  R: 0.4667  F1: 0.5761
2025/06/23 12:42:13 - mmengine - INFO - Epoch(train)  [16][ 30/123]  lr: 2.0000e-02  eta: 0:18:58  time: 0.1266  data_time: 0.0889  memory: 149  loss: 0.3252  fgnd: 0.2149  bgnd: 0.0117  P: 0.3115  R: 0.6552  F1: 0.4222
2025/06/23 12:42:14 - mmengine - INFO - Epoch(train)  [16][ 40/123]  lr: 2.0000e-02  eta: 0:18:53  time: 0.1302  data_time: 0.0924  memory: 149  loss: 0.3246  fgnd: 0.2602  bgnd: 0.0118  P: 0.3311  R: 0.7353  F1: 0.4566
2025/06/23 12:42:14 - mmengine - INFO - Epoch(train)  [16][ 50/123]  lr: 2.0000e-02  eta: 0:18:47  time: 0.1212  data_time: 0.0924  memory: 149  loss: 0.3316  fgnd: 0.2706  bgnd: 0.0129  P: 0.2667  R: 0.6471  F1: 0.3777
2025/06/23 12:42:14 - mmengine - INFO - Epoch(train)  [16][ 60/123]  lr: 2.0000e-02  eta: 0:18:42  time: 0.0921  data_time: 0.0658  memory: 149  loss: 0.3027  fgnd: 0.1838  bgnd: 0.0119  P: 0.5341  R: 0.8785  F1: 0.6643
2025/06/23 12:42:16 - mmengine - INFO - Epoch(train)  [16][ 70/123]  lr: 2.0000e-02  eta: 0:18:43  time: 0.0866  data_time: 0.0624  memory: 149  loss: 0.2896  fgnd: 0.5021  bgnd: 0.0282  P: 0.7059  R: 0.5967  F1: 0.6467
2025/06/23 12:42:17 - mmengine - INFO - Epoch(train)  [16][ 80/123]  lr: 2.0000e-02  eta: 0:18:44  time: 0.0782  data_time: 0.0532  memory: 149  loss: 0.3135  fgnd: 0.3603  bgnd: 0.0263  P: 0.8313  R: 0.4894  F1: 0.6161
2025/06/23 12:42:19 - mmengine - INFO - Epoch(train)  [16][ 90/123]  lr: 2.0000e-02  eta: 0:18:47  time: 0.1077  data_time: 0.0801  memory: 149  loss: 0.3374  fgnd: 0.3084  bgnd: 0.0130  P: 0.4588  R: 0.5821  F1: 0.5132
2025/06/23 12:42:20 - mmengine - INFO - Epoch(train)  [16][100/123]  lr: 2.0000e-02  eta: 0:18:46  time: 0.1229  data_time: 0.0937  memory: 149  loss: 0.3336  fgnd: 0.2189  bgnd: 0.0115  P: 0.3952  R: 0.7538  F1: 0.5185
2025/06/23 12:42:20 - mmengine - INFO - Epoch(train)  [16][110/123]  lr: 2.0000e-02  eta: 0:18:40  time: 0.1231  data_time: 0.0938  memory: 149  loss: 0.3365  fgnd: 0.2278  bgnd: 0.0121  P: 0.4902  R: 0.7979  F1: 0.6073
2025/06/23 12:42:21 - mmengine - INFO - Epoch(train)  [16][120/123]  lr: 2.0000e-02  eta: 0:18:35  time: 0.0974  data_time: 0.0704  memory: 149  loss: 0.3054  fgnd: 0.1803  bgnd: 0.0114  P: 0.5879  R: 0.9065  F1: 0.7132
2025/06/23 12:42:21 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_123820
2025/06/23 12:42:21 - mmengine - INFO - Saving checkpoint at 16 epochs
2025/06/23 12:42:21 - mmengine - INFO - Epoch(val)  [16][10/17]    eta: 0:00:00  time: 0.0456  data_time: 0.0336  memory: 149  
2025/06/23 12:42:22 - mmengine - INFO - Epoch(val) [16][17/17]    P: 0.0280  R: 0.2308  F1: 0.0499  data_time: 0.0316  time: 0.0446
2025/06/23 12:42:23 - mmengine - INFO - Epoch(train)  [17][ 10/123]  lr: 2.0000e-02  eta: 0:18:35  time: 0.0903  data_time: 0.0658  memory: 149  loss: 0.2760  fgnd: 0.5290  bgnd: 0.0285  P: 0.5909  R: 0.4221  F1: 0.4924
2025/06/23 12:42:25 - mmengine - INFO - Epoch(train)  [17][ 20/123]  lr: 2.0000e-02  eta: 0:18:38  time: 0.0846  data_time: 0.0583  memory: 149  loss: 0.3035  fgnd: 0.3928  bgnd: 0.0285  P: 0.8090  R: 0.5902  F1: 0.6825
2025/06/23 12:42:27 - mmengine - INFO - Epoch(train)  [17][ 30/123]  lr: 2.0000e-02  eta: 0:18:41  time: 0.1168  data_time: 0.0895  memory: 149  loss: 0.3149  fgnd: 0.2400  bgnd: 0.0127  P: 0.3841  R: 0.7683  F1: 0.5122
2025/06/23 12:42:27 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_123820
2025/06/23 12:42:27 - mmengine - INFO - Epoch(train)  [17][ 40/123]  lr: 2.0000e-02  eta: 0:18:36  time: 0.1202  data_time: 0.0914  memory: 149  loss: 0.3172  fgnd: 0.2480  bgnd: 0.0113  P: 0.3671  R: 0.7733  F1: 0.4979
2025/06/23 12:42:28 - mmengine - INFO - Epoch(train)  [17][ 50/123]  lr: 2.0000e-02  eta: 0:18:31  time: 0.1206  data_time: 0.0914  memory: 149  loss: 0.3277  fgnd: 0.2181  bgnd: 0.0117  P: 0.5379  R: 0.8571  F1: 0.6610
2025/06/23 12:42:28 - mmengine - INFO - Epoch(train)  [17][ 60/123]  lr: 2.0000e-02  eta: 0:18:26  time: 0.0912  data_time: 0.0650  memory: 149  loss: 0.2939  fgnd: 0.1913  bgnd: 0.0113  P: 0.5455  R: 0.8824  F1: 0.6742
2025/06/23 12:42:29 - mmengine - INFO - Epoch(train)  [17][ 70/123]  lr: 2.0000e-02  eta: 0:18:27  time: 0.0871  data_time: 0.0623  memory: 149  loss: 0.2744  fgnd: 0.3914  bgnd: 0.0230  P: 0.7361  R: 0.6667  F1: 0.6997
2025/06/23 12:42:31 - mmengine - INFO - Epoch(train)  [17][ 80/123]  lr: 2.0000e-02  eta: 0:18:28  time: 0.0787  data_time: 0.0524  memory: 149  loss: 0.2897  fgnd: 0.2955  bgnd: 0.0213  P: 0.7481  R: 0.7014  F1: 0.7240
2025/06/23 12:42:33 - mmengine - INFO - Epoch(train)  [17][ 90/123]  lr: 2.0000e-02  eta: 0:18:31  time: 0.1084  data_time: 0.0818  memory: 149  loss: 0.3008  fgnd: 0.2367  bgnd: 0.0119  P: 0.5280  R: 0.7952  F1: 0.6346
2025/06/23 12:42:34 - mmengine - INFO - Epoch(train)  [17][100/123]  lr: 2.0000e-02  eta: 0:18:30  time: 0.1242  data_time: 0.0957  memory: 149  loss: 0.2983  fgnd: 0.2149  bgnd: 0.0110  P: 0.3828  R: 0.7000  F1: 0.4949
2025/06/23 12:42:34 - mmengine - INFO - Epoch(train)  [17][110/123]  lr: 2.0000e-02  eta: 0:18:24  time: 0.1238  data_time: 0.0952  memory: 149  loss: 0.2995  fgnd: 0.1847  bgnd: 0.0110  P: 0.5694  R: 0.8817  F1: 0.6920
2025/06/23 12:42:34 - mmengine - INFO - Epoch(train)  [17][120/123]  lr: 2.0000e-02  eta: 0:18:19  time: 0.0974  data_time: 0.0704  memory: 149  loss: 0.2733  fgnd: 0.1746  bgnd: 0.0111  P: 0.5679  R: 0.8846  F1: 0.6917
2025/06/23 12:42:34 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_123820
2025/06/23 12:42:34 - mmengine - INFO - Saving checkpoint at 17 epochs
2025/06/23 12:42:35 - mmengine - INFO - Epoch(val)  [17][10/17]    eta: 0:00:00  time: 0.0451  data_time: 0.0322  memory: 149  
2025/06/23 12:42:35 - mmengine - INFO - Epoch(val) [17][17/17]    P: 0.0308  R: 0.2645  F1: 0.0552  data_time: 0.0312  time: 0.0445
2025/06/23 12:42:37 - mmengine - INFO - Epoch(train)  [18][ 10/123]  lr: 2.0000e-02  eta: 0:18:19  time: 0.0908  data_time: 0.0658  memory: 149  loss: 0.2649  fgnd: 0.4960  bgnd: 0.0272  P: 0.6911  R: 0.5183  F1: 0.5923
2025/06/23 12:42:39 - mmengine - INFO - Epoch(train)  [18][ 20/123]  lr: 2.0000e-02  eta: 0:18:22  time: 0.0851  data_time: 0.0577  memory: 149  loss: 0.2926  fgnd: 0.4064  bgnd: 0.0272  P: 0.7281  R: 0.5287  F1: 0.6125
2025/06/23 12:42:41 - mmengine - INFO - Epoch(train)  [18][ 30/123]  lr: 2.0000e-02  eta: 0:18:25  time: 0.1177  data_time: 0.0894  memory: 149  loss: 0.3129  fgnd: 0.2277  bgnd: 0.0137  P: 0.3942  R: 0.6750  F1: 0.4977
2025/06/23 12:42:41 - mmengine - INFO - Epoch(train)  [18][ 40/123]  lr: 2.0000e-02  eta: 0:18:20  time: 0.1213  data_time: 0.0922  memory: 149  loss: 0.3187  fgnd: 0.2370  bgnd: 0.0120  P: 0.2812  R: 0.7143  F1: 0.4036
2025/06/23 12:42:42 - mmengine - INFO - Epoch(train)  [18][ 50/123]  lr: 2.0000e-02  eta: 0:18:15  time: 0.1219  data_time: 0.0923  memory: 149  loss: 0.3284  fgnd: 0.2166  bgnd: 0.0119  P: 0.4868  R: 0.8598  F1: 0.6216
2025/06/23 12:42:42 - mmengine - INFO - Epoch(train)  [18][ 60/123]  lr: 2.0000e-02  eta: 0:18:10  time: 0.0926  data_time: 0.0657  memory: 149  loss: 0.2939  fgnd: 0.1874  bgnd: 0.0116  P: 0.5143  R: 0.8911  F1: 0.6522
2025/06/23 12:42:43 - mmengine - INFO - Epoch(train)  [18][ 70/123]  lr: 2.0000e-02  eta: 0:18:12  time: 0.0885  data_time: 0.0634  memory: 149  loss: 0.2823  fgnd: 0.4315  bgnd: 0.0246  P: 0.8421  R: 0.6512  F1: 0.7344
2025/06/23 12:42:45 - mmengine - INFO - Epoch(train)  [18][ 80/123]  lr: 2.0000e-02  eta: 0:18:12  time: 0.0786  data_time: 0.0526  memory: 149  loss: 0.2983  fgnd: 0.3439  bgnd: 0.0230  P: 0.8119  R: 0.5942  F1: 0.6862
2025/06/23 12:42:47 - mmengine - INFO - Epoch(train)  [18][ 90/123]  lr: 2.0000e-02  eta: 0:18:15  time: 0.1080  data_time: 0.0800  memory: 149  loss: 0.3147  fgnd: 0.2170  bgnd: 0.0114  P: 0.4933  R: 0.8132  F1: 0.6141
2025/06/23 12:42:48 - mmengine - INFO - Epoch(train)  [18][100/123]  lr: 2.0000e-02  eta: 0:18:13  time: 0.1230  data_time: 0.0956  memory: 149  loss: 0.3091  fgnd: 0.2379  bgnd: 0.0111  P: 0.3862  R: 0.7568  F1: 0.5114
2025/06/23 12:42:48 - mmengine - INFO - Epoch(train)  [18][110/123]  lr: 2.0000e-02  eta: 0:18:09  time: 0.1232  data_time: 0.0959  memory: 149  loss: 0.3103  fgnd: 0.1886  bgnd: 0.0109  P: 0.5000  R: 0.9057  F1: 0.6443
2025/06/23 12:42:48 - mmengine - INFO - Epoch(train)  [18][120/123]  lr: 2.0000e-02  eta: 0:18:04  time: 0.0966  data_time: 0.0712  memory: 149  loss: 0.2772  fgnd: 0.1750  bgnd: 0.0109  P: 0.6118  R: 0.8942  F1: 0.7266
2025/06/23 12:42:48 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_123820
2025/06/23 12:42:48 - mmengine - INFO - Saving checkpoint at 18 epochs
2025/06/23 12:42:49 - mmengine - INFO - Epoch(val)  [18][10/17]    eta: 0:00:00  time: 0.0453  data_time: 0.0317  memory: 149  
2025/06/23 12:42:49 - mmengine - INFO - Epoch(val) [18][17/17]    P: 0.0352  R: 0.2575  F1: 0.0619  data_time: 0.0321  time: 0.0454
2025/06/23 12:42:51 - mmengine - INFO - Epoch(train)  [19][ 10/123]  lr: 2.0000e-02  eta: 0:18:04  time: 0.0902  data_time: 0.0671  memory: 149  loss: 0.2507  fgnd: 0.4207  bgnd: 0.0243  P: 0.7016  R: 0.5800  F1: 0.6350
2025/06/23 12:42:53 - mmengine - INFO - Epoch(train)  [19][ 20/123]  lr: 2.0000e-02  eta: 0:18:06  time: 0.0858  data_time: 0.0603  memory: 149  loss: 0.2785  fgnd: 0.3180  bgnd: 0.0252  P: 0.8110  R: 0.6280  F1: 0.7079
2025/06/23 12:42:55 - mmengine - INFO - Epoch(train)  [19][ 30/123]  lr: 2.0000e-02  eta: 0:18:09  time: 0.1174  data_time: 0.0892  memory: 149  loss: 0.2917  fgnd: 0.2334  bgnd: 0.0123  P: 0.4706  R: 0.7826  F1: 0.5878
2025/06/23 12:42:55 - mmengine - INFO - Epoch(train)  [19][ 40/123]  lr: 2.0000e-02  eta: 0:18:05  time: 0.1204  data_time: 0.0918  memory: 149  loss: 0.2941  fgnd: 0.2286  bgnd: 0.0114  P: 0.2706  R: 0.6970  F1: 0.3898
2025/06/23 12:42:55 - mmengine - INFO - Epoch(train)  [19][ 50/123]  lr: 2.0000e-02  eta: 0:18:00  time: 0.1207  data_time: 0.0919  memory: 149  loss: 0.3044  fgnd: 0.2004  bgnd: 0.0121  P: 0.3750  R: 0.8313  F1: 0.5169
2025/06/23 12:42:55 - mmengine - INFO - Epoch(train)  [19][ 60/123]  lr: 2.0000e-02  eta: 0:17:55  time: 0.0919  data_time: 0.0654  memory: 149  loss: 0.2770  fgnd: 0.1769  bgnd: 0.0109  P: 0.5168  R: 0.8556  F1: 0.6444
2025/06/23 12:42:57 - mmengine - INFO - Epoch(train)  [19][ 70/123]  lr: 2.0000e-02  eta: 0:17:56  time: 0.0870  data_time: 0.0627  memory: 149  loss: 0.2596  fgnd: 0.3233  bgnd: 0.0207  P: 0.7541  R: 0.6479  F1: 0.6970
2025/06/23 12:42:59 - mmengine - INFO - Epoch(train)  [19][ 80/123]  lr: 2.0000e-02  eta: 0:17:57  time: 0.0789  data_time: 0.0537  memory: 149  loss: 0.2838  fgnd: 0.4324  bgnd: 0.0268  P: 0.8456  R: 0.6571  F1: 0.7395
2025/06/23 12:43:00 - mmengine - INFO - Epoch(train)  [19][ 90/123]  lr: 2.0000e-02  eta: 0:17:59  time: 0.1078  data_time: 0.0797  memory: 149  loss: 0.3030  fgnd: 0.2268  bgnd: 0.0133  P: 0.5407  R: 0.7849  F1: 0.6404
2025/06/23 12:43:01 - mmengine - INFO - Epoch(train)  [19][100/123]  lr: 2.0000e-02  eta: 0:17:58  time: 0.1227  data_time: 0.0944  memory: 149  loss: 0.2993  fgnd: 0.2076  bgnd: 0.0113  P: 0.4479  R: 0.8391  F1: 0.5840
2025/06/23 12:43:02 - mmengine - INFO - Epoch(train)  [19][110/123]  lr: 2.0000e-02  eta: 0:17:53  time: 0.1229  data_time: 0.0947  memory: 149  loss: 0.3030  fgnd: 0.2720  bgnd: 0.0123  P: 0.4882  R: 0.8557  F1: 0.6217
2025/06/23 12:43:02 - mmengine - INFO - Epoch(train)  [19][120/123]  lr: 2.0000e-02  eta: 0:17:48  time: 0.0964  data_time: 0.0703  memory: 149  loss: 0.2818  fgnd: 0.1895  bgnd: 0.0117  P: 0.4694  R: 0.8313  F1: 0.6000
2025/06/23 12:43:02 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_123820
2025/06/23 12:43:02 - mmengine - INFO - Saving checkpoint at 19 epochs
2025/06/23 12:43:03 - mmengine - INFO - Epoch(val)  [19][10/17]    eta: 0:00:00  time: 0.0459  data_time: 0.0319  memory: 149  
2025/06/23 12:43:03 - mmengine - INFO - Epoch(val) [19][17/17]    P: 0.0249  R: 0.2388  F1: 0.0450  data_time: 0.0316  time: 0.0455
2025/06/23 12:43:05 - mmengine - INFO - Epoch(train)  [20][ 10/123]  lr: 2.0000e-02  eta: 0:17:49  time: 0.0904  data_time: 0.0676  memory: 149  loss: 0.2634  fgnd: 0.5980  bgnd: 0.0296  P: 0.7226  R: 0.5500  F1: 0.6246
2025/06/23 12:43:06 - mmengine - INFO - Epoch(train)  [20][ 20/123]  lr: 2.0000e-02  eta: 0:17:51  time: 0.0865  data_time: 0.0612  memory: 149  loss: 0.2885  fgnd: 0.3287  bgnd: 0.0224  P: 0.7967  R: 0.6577  F1: 0.7206
2025/06/23 12:43:08 - mmengine - INFO - Epoch(train)  [20][ 30/123]  lr: 2.0000e-02  eta: 0:17:53  time: 0.1186  data_time: 0.0906  memory: 149  loss: 0.2980  fgnd: 0.2422  bgnd: 0.0143  P: 0.4639  R: 0.7624  F1: 0.5768
2025/06/23 12:43:09 - mmengine - INFO - Epoch(train)  [20][ 40/123]  lr: 2.0000e-02  eta: 0:17:49  time: 0.1216  data_time: 0.0936  memory: 149  loss: 0.3014  fgnd: 0.2275  bgnd: 0.0116  P: 0.3714  R: 0.8228  F1: 0.5118
2025/06/23 12:43:09 - mmengine - INFO - Epoch(train)  [20][ 50/123]  lr: 2.0000e-02  eta: 0:17:45  time: 0.1220  data_time: 0.0936  memory: 149  loss: 0.3058  fgnd: 0.2162  bgnd: 0.0111  P: 0.4914  R: 0.8600  F1: 0.6255
2025/06/23 12:43:09 - mmengine - INFO - Epoch(train)  [20][ 60/123]  lr: 2.0000e-02  eta: 0:17:40  time: 0.0921  data_time: 0.0658  memory: 149  loss: 0.2701  fgnd: 0.1823  bgnd: 0.0110  P: 0.5586  R: 0.8804  F1: 0.6835
2025/06/23 12:43:11 - mmengine - INFO - Epoch(train)  [20][ 70/123]  lr: 2.0000e-02  eta: 0:17:41  time: 0.0869  data_time: 0.0629  memory: 149  loss: 0.2506  fgnd: 0.3853  bgnd: 0.0214  P: 0.7295  R: 0.6403  F1: 0.6820
2025/06/23 12:43:12 - mmengine - INFO - Epoch(train)  [20][ 80/123]  lr: 2.0000e-02  eta: 0:17:42  time: 0.0780  data_time: 0.0534  memory: 149  loss: 0.2735  fgnd: 0.2914  bgnd: 0.0204  P: 0.7863  R: 0.7203  F1: 0.7518
2025/06/23 12:43:14 - mmengine - INFO - Epoch(train)  [20][ 90/123]  lr: 2.0000e-02  eta: 0:17:44  time: 0.1083  data_time: 0.0808  memory: 149  loss: 0.2828  fgnd: 0.2688  bgnd: 0.0142  P: 0.4270  R: 0.7525  F1: 0.5448
2025/06/23 12:43:15 - mmengine - INFO - Epoch(train)  [20][100/123]  lr: 2.0000e-02  eta: 0:17:42  time: 0.1233  data_time: 0.0959  memory: 149  loss: 0.2845  fgnd: 0.2162  bgnd: 0.0111  P: 0.3280  R: 0.7949  F1: 0.4644
2025/06/23 12:43:15 - mmengine - INFO - Epoch(train)  [20][110/123]  lr: 2.0000e-02  eta: 0:17:38  time: 0.1237  data_time: 0.0965  memory: 149  loss: 0.2883  fgnd: 0.2043  bgnd: 0.0112  P: 0.3718  R: 0.7838  F1: 0.5043
2025/06/23 12:43:16 - mmengine - INFO - Epoch(train)  [20][120/123]  lr: 2.0000e-02  eta: 0:17:34  time: 0.0973  data_time: 0.0721  memory: 149  loss: 0.2655  fgnd: 0.1646  bgnd: 0.0110  P: 0.5437  R: 0.8969  F1: 0.6770
2025/06/23 12:43:16 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_123820
2025/06/23 12:43:16 - mmengine - INFO - Saving checkpoint at 20 epochs
2025/06/23 12:43:17 - mmengine - INFO - Epoch(val)  [20][10/17]    eta: 0:00:00  time: 0.0456  data_time: 0.0320  memory: 149  
2025/06/23 12:43:17 - mmengine - INFO - Epoch(val) [20][17/17]    P: 0.0518  R: 0.2679  F1: 0.0868  data_time: 0.0327  time: 0.0445
2025/06/23 12:43:19 - mmengine - INFO - Epoch(train)  [21][ 10/123]  lr: 2.0000e-02  eta: 0:17:34  time: 0.0902  data_time: 0.0673  memory: 149  loss: 0.2424  fgnd: 0.3369  bgnd: 0.0200  P: 0.7460  R: 0.6763  F1: 0.7094
2025/06/23 12:43:20 - mmengine - INFO - Epoch(train)  [21][ 20/123]  lr: 2.0000e-02  eta: 0:17:35  time: 0.0852  data_time: 0.0603  memory: 149  loss: 0.2677  fgnd: 0.4108  bgnd: 0.0276  P: 0.8376  R: 0.5765  F1: 0.6829
2025/06/23 12:43:22 - mmengine - INFO - Epoch(train)  [21][ 30/123]  lr: 2.0000e-02  eta: 0:17:38  time: 0.1173  data_time: 0.0897  memory: 149  loss: 0.2803  fgnd: 0.2332  bgnd: 0.0156  P: 0.6500  R: 0.6771  F1: 0.6633
2025/06/23 12:43:23 - mmengine - INFO - Epoch(train)  [21][ 40/123]  lr: 2.0000e-02  eta: 0:17:34  time: 0.1206  data_time: 0.0911  memory: 149  loss: 0.2797  fgnd: 0.2173  bgnd: 0.0115  P: 0.3750  R: 0.7600  F1: 0.5022
2025/06/23 12:43:23 - mmengine - INFO - Epoch(train)  [21][ 50/123]  lr: 2.0000e-02  eta: 0:17:30  time: 0.1206  data_time: 0.0908  memory: 149  loss: 0.2862  fgnd: 0.2149  bgnd: 0.0113  P: 0.5569  R: 0.8611  F1: 0.6764
2025/06/23 12:43:23 - mmengine - INFO - Epoch(train)  [21][ 60/123]  lr: 2.0000e-02  eta: 0:17:25  time: 0.0917  data_time: 0.0641  memory: 149  loss: 0.2602  fgnd: 0.1727  bgnd: 0.0102  P: 0.6400  R: 0.9057  F1: 0.7500
2025/06/23 12:43:25 - mmengine - INFO - Epoch(train)  [21][ 70/123]  lr: 2.0000e-02  eta: 0:17:26  time: 0.0871  data_time: 0.0616  memory: 149  loss: 0.2506  fgnd: 0.4286  bgnd: 0.0243  P: 0.7661  R: 0.5901  F1: 0.6667
2025/06/23 12:43:26 - mmengine - INFO - Epoch(train)  [21][ 80/123]  lr: 2.0000e-02  eta: 0:17:26  time: 0.0776  data_time: 0.0513  memory: 149  loss: 0.2715  fgnd: 0.3859  bgnd: 0.0219  P: 0.7459  R: 0.6408  F1: 0.6894
2025/06/23 12:43:28 - mmengine - INFO - Epoch(train)  [21][ 90/123]  lr: 2.0000e-02  eta: 0:17:29  time: 0.1090  data_time: 0.0820  memory: 149  loss: 0.2900  fgnd: 0.2568  bgnd: 0.0137  P: 0.4880  R: 0.8182  F1: 0.6113
2025/06/23 12:43:29 - mmengine - INFO - Epoch(train)  [21][100/123]  lr: 2.0000e-02  eta: 0:17:28  time: 0.1248  data_time: 0.0963  memory: 149  loss: 0.2890  fgnd: 0.2090  bgnd: 0.0118  P: 0.3487  R: 0.8395  F1: 0.4928
2025/06/23 12:43:29 - mmengine - INFO - Epoch(train)  [21][110/123]  lr: 2.0000e-02  eta: 0:17:23  time: 0.1251  data_time: 0.0969  memory: 149  loss: 0.2962  fgnd: 0.2147  bgnd: 0.0119  P: 0.4187  R: 0.8272  F1: 0.5560
2025/06/23 12:43:30 - mmengine - INFO - Epoch(train)  [21][120/123]  lr: 2.0000e-02  eta: 0:17:19  time: 0.0988  data_time: 0.0722  memory: 149  loss: 0.2713  fgnd: 0.2035  bgnd: 0.0114  P: 0.5202  R: 0.8824  F1: 0.6545
2025/06/23 12:43:30 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_123820
2025/06/23 12:43:30 - mmengine - INFO - Saving checkpoint at 21 epochs
2025/06/23 12:43:30 - mmengine - INFO - Epoch(val)  [21][10/17]    eta: 0:00:00  time: 0.0456  data_time: 0.0314  memory: 149  
2025/06/23 12:43:31 - mmengine - INFO - Epoch(val) [21][17/17]    P: 0.0400  R: 0.4371  F1: 0.0734  data_time: 0.0298  time: 0.0464
2025/06/23 12:43:32 - mmengine - INFO - Epoch(train)  [22][ 10/123]  lr: 2.0000e-02  eta: 0:17:19  time: 0.0915  data_time: 0.0669  memory: 149  loss: 0.2559  fgnd: 0.3436  bgnd: 0.0225  P: 0.7130  R: 0.5857  F1: 0.6431
2025/06/23 12:43:34 - mmengine - INFO - Epoch(train)  [22][ 20/123]  lr: 2.0000e-02  eta: 0:17:21  time: 0.0843  data_time: 0.0572  memory: 149  loss: 0.2819  fgnd: 0.3845  bgnd: 0.0269  P: 0.8261  R: 0.5723  F1: 0.6762
2025/06/23 12:43:36 - mmengine - INFO - Epoch(train)  [22][ 30/123]  lr: 2.0000e-02  eta: 0:17:23  time: 0.1165  data_time: 0.0879  memory: 149  loss: 0.2933  fgnd: 0.1981  bgnd: 0.0136  P: 0.5063  R: 0.8333  F1: 0.6299
2025/06/23 12:43:36 - mmengine - INFO - Epoch(train)  [22][ 40/123]  lr: 2.0000e-02  eta: 0:17:19  time: 0.1199  data_time: 0.0910  memory: 149  loss: 0.2972  fgnd: 0.2201  bgnd: 0.0118  P: 0.0442  R: 0.2051  F1: 0.0727
2025/06/23 12:43:37 - mmengine - INFO - Epoch(train)  [22][ 50/123]  lr: 2.0000e-02  eta: 0:17:15  time: 0.1200  data_time: 0.0911  memory: 149  loss: 0.3036  fgnd: 0.2017  bgnd: 0.0117  P: 0.5231  R: 0.9027  F1: 0.6623
2025/06/23 12:43:37 - mmengine - INFO - Epoch(train)  [22][ 60/123]  lr: 2.0000e-02  eta: 0:17:11  time: 0.0917  data_time: 0.0656  memory: 149  loss: 0.2726  fgnd: 0.1773  bgnd: 0.0110  P: 0.5032  R: 0.8298  F1: 0.6265
2025/06/23 12:43:38 - mmengine - INFO - Epoch(train)  [22][ 70/123]  lr: 2.0000e-02  eta: 0:17:12  time: 0.0875  data_time: 0.0643  memory: 149  loss: 0.2618  fgnd: 0.4795  bgnd: 0.0262  P: 0.7611  R: 0.5244  F1: 0.6209
2025/06/23 12:43:40 - mmengine - INFO - Epoch(train)  [22][ 80/123]  lr: 2.0000e-02  eta: 0:17:12  time: 0.0787  data_time: 0.0540  memory: 149  loss: 0.2845  fgnd: 0.3098  bgnd: 0.0200  P: 0.7929  R: 0.7450  F1: 0.7682
2025/06/23 12:43:42 - mmengine - INFO - Epoch(train)  [22][ 90/123]  lr: 2.0000e-02  eta: 0:17:14  time: 0.1088  data_time: 0.0811  memory: 149  loss: 0.2969  fgnd: 0.2402  bgnd: 0.0137  P: 0.3871  R: 0.7229  F1: 0.5042
2025/06/23 12:43:43 - mmengine - INFO - Epoch(train)  [22][100/123]  lr: 2.0000e-02  eta: 0:17:12  time: 0.1235  data_time: 0.0956  memory: 149  loss: 0.2978  fgnd: 0.2336  bgnd: 0.0118  P: 0.4247  R: 0.8229  F1: 0.5603
2025/06/23 12:43:43 - mmengine - INFO - Epoch(train)  [22][110/123]  lr: 2.0000e-02  eta: 0:17:08  time: 0.1236  data_time: 0.0956  memory: 149  loss: 0.2995  fgnd: 0.2245  bgnd: 0.0115  P: 0.4093  R: 0.8681  F1: 0.5563
2025/06/23 12:43:43 - mmengine - INFO - Epoch(train)  [22][120/123]  lr: 2.0000e-02  eta: 0:17:04  time: 0.0970  data_time: 0.0707  memory: 149  loss: 0.2744  fgnd: 0.1812  bgnd: 0.0113  P: 0.4709  R: 0.8438  F1: 0.6045
2025/06/23 12:43:43 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_123820
2025/06/23 12:43:43 - mmengine - INFO - Saving checkpoint at 22 epochs
2025/06/23 12:43:44 - mmengine - INFO - Epoch(val)  [22][10/17]    eta: 0:00:00  time: 0.0460  data_time: 0.0302  memory: 149  
2025/06/23 12:43:44 - mmengine - INFO - Epoch(val) [22][17/17]    P: 0.0418  R: 0.4906  F1: 0.0771  data_time: 0.0274  time: 0.0463
2025/06/23 12:43:46 - mmengine - INFO - Epoch(train)  [23][ 10/123]  lr: 2.0000e-02  eta: 0:17:04  time: 0.0901  data_time: 0.0670  memory: 149  loss: 0.2541  fgnd: 0.3587  bgnd: 0.0192  P: 0.2388  R: 0.3441  F1: 0.2819
2025/06/23 12:43:48 - mmengine - INFO - Epoch(train)  [23][ 20/123]  lr: 2.0000e-02  eta: 0:17:06  time: 0.0855  data_time: 0.0599  memory: 149  loss: 0.2875  fgnd: 0.4203  bgnd: 0.0271  P: 0.8125  R: 0.6464  F1: 0.7200
2025/06/23 12:43:50 - mmengine - INFO - Epoch(train)  [23][ 30/123]  lr: 2.0000e-02  eta: 0:17:08  time: 0.1174  data_time: 0.0899  memory: 149  loss: 0.2943  fgnd: 0.2292  bgnd: 0.0130  P: 0.4797  R: 0.7717  F1: 0.5917
2025/06/23 12:43:50 - mmengine - INFO - Epoch(train)  [23][ 40/123]  lr: 2.0000e-02  eta: 0:17:04  time: 0.1207  data_time: 0.0933  memory: 149  loss: 0.2927  fgnd: 0.2227  bgnd: 0.0113  P: 0.3827  R: 0.7848  F1: 0.5145
2025/06/23 12:43:50 - mmengine - INFO - Epoch(train)  [23][ 50/123]  lr: 2.0000e-02  eta: 0:17:00  time: 0.1209  data_time: 0.0937  memory: 149  loss: 0.2984  fgnd: 0.1904  bgnd: 0.0109  P: 0.4710  R: 0.8442  F1: 0.6047
2025/06/23 12:43:51 - mmengine - INFO - Epoch(train)  [23][ 60/123]  lr: 2.0000e-02  eta: 0:16:56  time: 0.0920  data_time: 0.0667  memory: 149  loss: 0.2680  fgnd: 0.1657  bgnd: 0.0105  P: 0.5988  R: 0.9196  F1: 0.7254
2025/06/23 12:43:52 - mmengine - INFO - Epoch(train)  [23][ 70/123]  lr: 2.0000e-02  eta: 0:16:57  time: 0.0876  data_time: 0.0644  memory: 149  loss: 0.2407  fgnd: 0.4238  bgnd: 0.0249  P: 0.7840  R: 0.6049  F1: 0.6829
2025/06/23 12:43:54 - mmengine - INFO - Epoch(train)  [23][ 80/123]  lr: 2.0000e-02  eta: 0:16:57  time: 0.0787  data_time: 0.0544  memory: 149  loss: 0.2556  fgnd: 0.3070  bgnd: 0.0217  P: 0.7966  R: 0.6620  F1: 0.7231
2025/06/23 12:43:56 - mmengine - INFO - Epoch(train)  [23][ 90/123]  lr: 2.0000e-02  eta: 0:16:59  time: 0.1094  data_time: 0.0817  memory: 149  loss: 0.2747  fgnd: 0.2103  bgnd: 0.0124  P: 0.5556  R: 0.8095  F1: 0.6589
2025/06/23 12:43:57 - mmengine - INFO - Epoch(train)  [23][100/123]  lr: 2.0000e-02  eta: 0:16:58  time: 0.1241  data_time: 0.0959  memory: 149  loss: 0.2703  fgnd: 0.2335  bgnd: 0.0112  P: 0.4013  R: 0.7625  F1: 0.5259
2025/06/23 12:43:57 - mmengine - INFO - Epoch(train)  [23][110/123]  lr: 2.0000e-02  eta: 0:16:54  time: 0.1242  data_time: 0.0962  memory: 149  loss: 0.2752  fgnd: 0.1965  bgnd: 0.0110  P: 0.5114  R: 0.8738  F1: 0.6452
2025/06/23 12:43:57 - mmengine - INFO - Epoch(train)  [23][120/123]  lr: 2.0000e-02  eta: 0:16:50  time: 0.0973  data_time: 0.0715  memory: 149  loss: 0.2542  fgnd: 0.1616  bgnd: 0.0108  P: 0.5741  R: 0.8942  F1: 0.6992
2025/06/23 12:43:57 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_123820
2025/06/23 12:43:57 - mmengine - INFO - Saving checkpoint at 23 epochs
2025/06/23 12:43:58 - mmengine - INFO - Epoch(val)  [23][10/17]    eta: 0:00:00  time: 0.0461  data_time: 0.0290  memory: 149  
2025/06/23 12:43:58 - mmengine - INFO - Epoch(val) [23][17/17]    P: 0.0477  R: 0.3616  F1: 0.0843  data_time: 0.0302  time: 0.0454
2025/06/23 12:44:00 - mmengine - INFO - Epoch(train)  [24][ 10/123]  lr: 2.0000e-02  eta: 0:16:50  time: 0.0907  data_time: 0.0665  memory: 149  loss: 0.2457  fgnd: 0.3430  bgnd: 0.0213  P: 0.7519  R: 0.6757  F1: 0.7117
2025/06/23 12:44:02 - mmengine - INFO - Epoch(train)  [24][ 20/123]  lr: 2.0000e-02  eta: 0:16:51  time: 0.0851  data_time: 0.0595  memory: 149  loss: 0.2698  fgnd: 0.4438  bgnd: 0.0267  P: 0.8220  R: 0.6218  F1: 0.7080
2025/06/23 12:44:03 - mmengine - INFO - Epoch(train)  [24][ 30/123]  lr: 2.0000e-02  eta: 0:16:53  time: 0.1155  data_time: 0.0884  memory: 149  loss: 0.2831  fgnd: 0.2354  bgnd: 0.0150  P: 0.5030  R: 0.7905  F1: 0.6148
2025/06/23 12:44:04 - mmengine - INFO - Epoch(train)  [24][ 40/123]  lr: 2.0000e-02  eta: 0:16:49  time: 0.1189  data_time: 0.0910  memory: 149  loss: 0.2859  fgnd: 0.2067  bgnd: 0.0111  P: 0.4521  R: 0.8500  F1: 0.5903
2025/06/23 12:44:04 - mmengine - INFO - Epoch(train)  [24][ 50/123]  lr: 2.0000e-02  eta: 0:16:46  time: 0.1191  data_time: 0.0910  memory: 149  loss: 0.2939  fgnd: 0.2013  bgnd: 0.0112  P: 0.5348  R: 0.9091  F1: 0.6734
2025/06/23 12:44:04 - mmengine - INFO - Epoch(train)  [24][ 60/123]  lr: 2.0000e-02  eta: 0:16:42  time: 0.0898  data_time: 0.0643  memory: 149  loss: 0.2624  fgnd: 0.1636  bgnd: 0.0107  P: 0.5154  R: 0.8171  F1: 0.6321
2025/06/23 12:44:06 - mmengine - INFO - Epoch(train)  [24][ 70/123]  lr: 2.0000e-02  eta: 0:16:42  time: 0.0846  data_time: 0.0614  memory: 149  loss: 0.2524  fgnd: 0.4120  bgnd: 0.0255  P: 0.7021  R: 0.5756  F1: 0.6326
2025/06/23 12:44:07 - mmengine - INFO - Epoch(train)  [24][ 80/123]  lr: 2.0000e-02  eta: 0:16:43  time: 0.0773  data_time: 0.0527  memory: 149  loss: 0.2715  fgnd: 0.3639  bgnd: 0.0254  P: 0.8707  R: 0.6957  F1: 0.7734
2025/06/23 12:44:09 - mmengine - INFO - Epoch(train)  [24][ 90/123]  lr: 2.0000e-02  eta: 0:16:44  time: 0.1072  data_time: 0.0799  memory: 149  loss: 0.2875  fgnd: 0.2101  bgnd: 0.0132  P: 0.1481  R: 0.3125  F1: 0.2010
