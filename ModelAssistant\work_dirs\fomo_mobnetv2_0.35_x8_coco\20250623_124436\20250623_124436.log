2025/06/23 12:44:38 - mmengine - INFO - 
------------------------------------------------------------
System environment:
    sys.platform: win32
    Python: 3.12.11 | packaged by Anaconda, Inc. | (main, Jun  5 2025, 12:58:53) [MSC v.1929 64 bit (AMD64)]
    CUDA available: True
    MUSA available: False
    numpy_random_seed: 657571097
    GPU 0: NVIDIA GeForce RTX 5060 Ti
    CUDA_HOME: None
    MSVC: Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35209 for x64
    GCC: n/a
    PyTorch: 2.8.0.dev20250608+cu128
    PyTorch compiling details: PyTorch built with:
  - C++ Version: 201703
  - MSVC 193833144
  - Intel(R) oneAPI Math Kernel Library Version 2025.1-Product Build 20250306 for Intel(R) 64 architecture applications
  - Intel(R) MKL-DNN v3.7.1 (Git Hash 8d263e693366ef8db40acc569cc7d8edf644556d)
  - OpenMP 2019
  - LAPACK is enabled (usually provided by MKL)
  - CPU capability usage: AVX512
  - CUDA Runtime 12.8
  - NVCC architecture flags: -gencode;arch=compute_61,code=sm_61;-gencode;arch=compute_70,code=sm_70;-gencode;arch=compute_75,code=sm_75;-gencode;arch=compute_80,code=sm_80;-gencode;arch=compute_86,code=sm_86;-gencode;arch=compute_90,code=sm_90;-gencode;arch=compute_100,code=sm_100;-gencode;arch=compute_120,code=sm_120
  - CuDNN 90.7.1
  - Magma 2.5.4
  - Build settings: BLAS_INFO=mkl, BUILD_TYPE=Release, COMMIT_SHA=843156205ec57cf78d5cf10bf982656b3db51dfd, CUDA_VERSION=12.8, CUDNN_VERSION=9.7.1, CXX_COMPILER=C:/actions-runner/_work/pytorch/pytorch/pytorch/.ci/pytorch/windows/tmp_bin/sccache-cl.exe, CXX_FLAGS=/DWIN32 /D_WINDOWS /GR /EHsc /Zc:__cplusplus /bigobj /FS /utf-8 -DUSE_PTHREADPOOL -DNDEBUG -DUSE_KINETO -DLIBKINETO_NOROCTRACER -DLIBKINETO_NOXPUPTI=ON -DUSE_FBGEMM -DUSE_XNNPACK -DSYMBOLICATE_MOBILE_DEBUG_HANDLE /wd4624 /wd4068 /wd4067 /wd4267 /wd4661 /wd4717 /wd4244 /wd4804 /wd4273, LAPACK_INFO=mkl, PERF_WITH_AVX=1, PERF_WITH_AVX2=1, TORCH_VERSION=2.8.0, USE_CUDA=ON, USE_CUDNN=ON, USE_CUSPARSELT=OFF, USE_GFLAGS=OFF, USE_GLOG=OFF, USE_GLOO=ON, USE_MKL=ON, USE_MKLDNN=ON, USE_MPI=OFF, USE_NCCL=OFF, USE_NNPACK=OFF, USE_OPENMP=ON, USE_ROCM=OFF, USE_ROCM_KERNEL_ASSERT=OFF, USE_XCCL=OFF, USE_XPU=OFF, 

    TorchVision: 0.23.0.dev20250609+cu128
    OpenCV: 4.11.0
    MMEngine: 0.10.4

Runtime environment:
    cudnn_benchmark: True
    mp_cfg: {'mp_start_method': 'fork', 'opencv_num_threads': 0}
    dist_cfg: {'backend': 'nccl'}
    seed: 657571097
    deterministic: False
    Distributed launcher: none
    Distributed training: False
    GPU number: 1
------------------------------------------------------------

2025/06/23 12:44:40 - mmengine - INFO - Distributed training is not used, all SyncBatchNorm (SyncBN) layers in the model will be automatically reverted to BatchNormXd layers if they are used.
2025/06/23 12:44:40 - mmengine - WARNING - The prefix is not set in metric class FomoMetric.
Name of parameter - Initialization information

backbone.conv1.conv.weight - torch.Size([16, 3, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.conv1.norm.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.conv1.norm.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer1.0.conv.0.conv.weight - torch.Size([16, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer1.0.conv.0.norm.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer1.0.conv.0.norm.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer1.0.conv.1.weight - torch.Size([8, 16, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer1.0.conv.2.weight - torch.Size([8]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer1.0.conv.2.bias - torch.Size([8]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.0.conv.0.conv.weight - torch.Size([48, 8, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer2.0.conv.0.norm.weight - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.0.conv.0.norm.bias - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.0.conv.1.conv.weight - torch.Size([48, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer2.0.conv.1.norm.weight - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.0.conv.1.norm.bias - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.0.conv.2.weight - torch.Size([8, 48, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer2.0.conv.3.weight - torch.Size([8]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.0.conv.3.bias - torch.Size([8]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.1.conv.0.conv.weight - torch.Size([48, 8, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer2.1.conv.0.norm.weight - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.1.conv.0.norm.bias - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.1.conv.1.conv.weight - torch.Size([48, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer2.1.conv.1.norm.weight - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.1.conv.1.norm.bias - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.1.conv.2.weight - torch.Size([8, 48, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer2.1.conv.3.weight - torch.Size([8]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer2.1.conv.3.bias - torch.Size([8]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.0.conv.0.conv.weight - torch.Size([48, 8, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer3.0.conv.0.norm.weight - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.0.conv.0.norm.bias - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.0.conv.1.conv.weight - torch.Size([48, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer3.0.conv.1.norm.weight - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.0.conv.1.norm.bias - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.0.conv.2.weight - torch.Size([16, 48, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer3.0.conv.3.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.0.conv.3.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.1.conv.0.conv.weight - torch.Size([96, 16, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer3.1.conv.0.norm.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.1.conv.0.norm.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.1.conv.1.conv.weight - torch.Size([96, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer3.1.conv.1.norm.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.1.conv.1.norm.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.1.conv.2.weight - torch.Size([16, 96, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer3.1.conv.3.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.1.conv.3.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.2.conv.0.conv.weight - torch.Size([96, 16, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer3.2.conv.0.norm.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.2.conv.0.norm.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.2.conv.1.conv.weight - torch.Size([96, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer3.2.conv.1.norm.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.2.conv.1.norm.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.2.conv.2.weight - torch.Size([16, 96, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer3.2.conv.3.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer3.2.conv.3.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.0.conv.0.conv.weight - torch.Size([96, 16, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.0.conv.0.norm.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.0.conv.0.norm.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.0.conv.1.conv.weight - torch.Size([96, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.0.conv.1.norm.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.0.conv.1.norm.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.0.conv.2.weight - torch.Size([24, 96, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.0.conv.3.weight - torch.Size([24]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.0.conv.3.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.1.conv.0.conv.weight - torch.Size([144, 24, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.1.conv.0.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.1.conv.0.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.1.conv.1.conv.weight - torch.Size([144, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.1.conv.1.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.1.conv.1.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.1.conv.2.weight - torch.Size([24, 144, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.1.conv.3.weight - torch.Size([24]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.1.conv.3.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.2.conv.0.conv.weight - torch.Size([144, 24, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.2.conv.0.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.2.conv.0.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.2.conv.1.conv.weight - torch.Size([144, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.2.conv.1.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.2.conv.1.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.2.conv.2.weight - torch.Size([24, 144, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.2.conv.3.weight - torch.Size([24]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.2.conv.3.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.3.conv.0.conv.weight - torch.Size([144, 24, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.3.conv.0.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.3.conv.0.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.3.conv.1.conv.weight - torch.Size([144, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.3.conv.1.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.3.conv.1.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.3.conv.2.weight - torch.Size([24, 144, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer4.3.conv.3.weight - torch.Size([24]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer4.3.conv.3.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.0.conv.0.conv.weight - torch.Size([144, 24, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer5.0.conv.0.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.0.conv.0.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.0.conv.1.conv.weight - torch.Size([144, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer5.0.conv.1.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.0.conv.1.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.0.conv.2.weight - torch.Size([32, 144, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer5.0.conv.3.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.0.conv.3.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.1.conv.0.conv.weight - torch.Size([192, 32, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer5.1.conv.0.norm.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.1.conv.0.norm.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.1.conv.1.conv.weight - torch.Size([192, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer5.1.conv.1.norm.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.1.conv.1.norm.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.1.conv.2.weight - torch.Size([32, 192, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer5.1.conv.3.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.1.conv.3.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.2.conv.0.conv.weight - torch.Size([192, 32, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer5.2.conv.0.norm.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.2.conv.0.norm.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.2.conv.1.conv.weight - torch.Size([192, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer5.2.conv.1.norm.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.2.conv.1.norm.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.2.conv.2.weight - torch.Size([32, 192, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer5.2.conv.3.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer5.2.conv.3.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.0.conv.0.conv.weight - torch.Size([192, 32, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer6.0.conv.0.norm.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.0.conv.0.norm.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.0.conv.1.conv.weight - torch.Size([192, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer6.0.conv.1.norm.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.0.conv.1.norm.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.0.conv.2.weight - torch.Size([56, 192, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer6.0.conv.3.weight - torch.Size([56]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.0.conv.3.bias - torch.Size([56]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.1.conv.0.conv.weight - torch.Size([336, 56, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer6.1.conv.0.norm.weight - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.1.conv.0.norm.bias - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.1.conv.1.conv.weight - torch.Size([336, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer6.1.conv.1.norm.weight - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.1.conv.1.norm.bias - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.1.conv.2.weight - torch.Size([56, 336, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer6.1.conv.3.weight - torch.Size([56]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.1.conv.3.bias - torch.Size([56]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.2.conv.0.conv.weight - torch.Size([336, 56, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer6.2.conv.0.norm.weight - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.2.conv.0.norm.bias - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.2.conv.1.conv.weight - torch.Size([336, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer6.2.conv.1.norm.weight - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.2.conv.1.norm.bias - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.2.conv.2.weight - torch.Size([56, 336, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer6.2.conv.3.weight - torch.Size([56]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer6.2.conv.3.bias - torch.Size([56]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer7.0.conv.0.conv.weight - torch.Size([336, 56, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer7.0.conv.0.norm.weight - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer7.0.conv.0.norm.bias - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer7.0.conv.1.conv.weight - torch.Size([336, 1, 3, 3]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer7.0.conv.1.norm.weight - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer7.0.conv.1.norm.bias - torch.Size([336]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer7.0.conv.2.weight - torch.Size([112, 336, 1, 1]): 
Initialized by user-defined `init_weights` in MobileNetv2  

backbone.layer7.0.conv.3.weight - torch.Size([112]): 
The value is the same before and after calling `init_weights` of Fomo  

backbone.layer7.0.conv.3.bias - torch.Size([112]): 
The value is the same before and after calling `init_weights` of Fomo  

bbox_head.convs_bridge.0.0.weight - torch.Size([48, 16, 1, 1]): 
Initialized by user-defined `init_weights` in FomoHead  

bbox_head.convs_bridge.0.1.weight - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

bbox_head.convs_bridge.0.1.bias - torch.Size([48]): 
The value is the same before and after calling `init_weights` of Fomo  

bbox_head.convs_pred.0.weight - torch.Size([3, 48, 1, 1]): 
Initialized by user-defined `init_weights` in FomoHead  

bbox_head.convs_pred.0.bias - torch.Size([3]): 
Initialized by user-defined `init_weights` in FomoHead  
2025/06/23 12:44:41 - mmengine - WARNING - "FileClient" will be deprecated in future. Please use io functions in https://mmengine.readthedocs.io/en/latest/api/fileio.html#file-io
2025/06/23 12:44:41 - mmengine - WARNING - "HardDiskBackend" is the alias of "LocalBackend" and the former will be deprecated in future.
2025/06/23 12:44:41 - mmengine - INFO - Checkpoints will be saved to D:\OBJECT_DETECTION\sscma\ModelAssistant\work_dirs\fomo_mobnetv2_0.35_x8_coco.
2025/06/23 12:44:51 - mmengine - INFO - Epoch(train)   [1][ 10/123]  lr: 6.2207e-03  eta: 3:36:32  time: 1.0572  data_time: 0.9871  memory: 161  loss: 1.1297  fgnd: 0.9095  bgnd: 0.2068  P: 0.1330  R: 0.2348  F1: 0.1698
2025/06/23 12:44:53 - mmengine - INFO - Epoch(train)   [1][ 20/123]  lr: 1.3110e-02  eta: 2:06:30  time: 0.6181  data_time: 0.5685  memory: 147  loss: 1.0955  fgnd: 0.9138  bgnd: 0.0999  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:44:55 - mmengine - INFO - Epoch(train)   [1][ 30/123]  lr: 2.0000e-02  eta: 1:37:06  time: 0.4748  data_time: 0.4337  memory: 147  loss: 1.0126  fgnd: 0.7069  bgnd: 0.0367  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:44:55 - mmengine - INFO - Epoch(train)   [1][ 40/123]  lr: 2.0000e-02  eta: 1:14:40  time: 0.3654  data_time: 0.3313  memory: 147  loss: 0.8800  fgnd: 0.3557  bgnd: 0.0181  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:44:55 - mmengine - INFO - Epoch(train)   [1][ 50/123]  lr: 2.0000e-02  eta: 1:00:39  time: 0.2971  data_time: 0.2671  memory: 147  loss: 0.7722  fgnd: 0.2973  bgnd: 0.0152  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:44:56 - mmengine - INFO - Epoch(train)   [1][ 60/123]  lr: 2.0000e-02  eta: 0:51:18  time: 0.0904  data_time: 0.0719  memory: 147  loss: 0.6060  fgnd: 0.2876  bgnd: 0.0192  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:44:57 - mmengine - INFO - Epoch(train)   [1][ 70/123]  lr: 2.0000e-02  eta: 0:48:24  time: 0.0852  data_time: 0.0690  memory: 147  loss: 0.6661  fgnd: 1.7696  bgnd: 0.0356  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:44:59 - mmengine - INFO - Epoch(train)   [1][ 80/123]  lr: 2.0000e-02  eta: 0:45:58  time: 0.0762  data_time: 0.0607  memory: 147  loss: 0.7167  fgnd: 0.7580  bgnd: 0.0527  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:45:00 - mmengine - INFO - Epoch(train)   [1][ 90/123]  lr: 2.0000e-02  eta: 0:44:58  time: 0.1054  data_time: 0.0892  memory: 147  loss: 0.7694  fgnd: 0.6382  bgnd: 0.0492  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:45:01 - mmengine - INFO - Epoch(train)   [1][100/123]  lr: 2.0000e-02  eta: 0:42:28  time: 0.1207  data_time: 0.1033  memory: 147  loss: 0.8302  fgnd: 0.5678  bgnd: 0.0404  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:45:02 - mmengine - INFO - Epoch(train)   [1][110/123]  lr: 2.0000e-02  eta: 0:39:01  time: 0.1207  data_time: 0.1032  memory: 147  loss: 0.8834  fgnd: 0.4978  bgnd: 0.0414  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:45:02 - mmengine - INFO - Epoch(train)   [1][120/123]  lr: 2.0000e-02  eta: 0:36:06  time: 0.0945  data_time: 0.0781  memory: 147  loss: 0.7039  fgnd: 0.3928  bgnd: 0.0324  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:45:02 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_124436
2025/06/23 12:45:02 - mmengine - INFO - Saving checkpoint at 1 epochs
2025/06/23 12:45:12 - mmengine - INFO - Epoch(val)   [1][10/17]    eta: 0:00:06  time: 0.9402  data_time: 0.9267  memory: 147  
2025/06/23 12:45:12 - mmengine - INFO - Epoch(val) [1][17/17]    P: 0.0000  R: 0.0000  F1: 0.0000  data_time: 0.5553  time: 0.5657
2025/06/23 12:45:13 - mmengine - INFO - Epoch(train)   [2][ 10/123]  lr: 2.0000e-02  eta: 0:35:15  time: 0.0893  data_time: 0.0723  memory: 150  loss: 0.6107  fgnd: 0.7158  bgnd: 0.0336  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:45:15 - mmengine - INFO - Epoch(train)   [2][ 20/123]  lr: 2.0000e-02  eta: 0:35:14  time: 0.0844  data_time: 0.0673  memory: 149  loss: 0.6492  fgnd: 0.7362  bgnd: 0.0359  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:45:17 - mmengine - INFO - Epoch(train)   [2][ 30/123]  lr: 2.0000e-02  eta: 0:35:24  time: 0.1158  data_time: 0.0974  memory: 149  loss: 0.6428  fgnd: 0.4661  bgnd: 0.0262  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:45:18 - mmengine - INFO - Epoch(train)   [2][ 40/123]  lr: 2.0000e-02  eta: 0:33:41  time: 0.1191  data_time: 0.1003  memory: 149  loss: 0.6303  fgnd: 0.4335  bgnd: 0.0235  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:45:18 - mmengine - INFO - Epoch(train)   [2][ 50/123]  lr: 2.0000e-02  eta: 0:32:00  time: 0.1183  data_time: 0.1009  memory: 149  loss: 0.6262  fgnd: 0.3682  bgnd: 0.0207  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:45:18 - mmengine - INFO - Epoch(train)   [2][ 60/123]  lr: 2.0000e-02  eta: 0:30:29  time: 0.0901  data_time: 0.0737  memory: 149  loss: 0.5480  fgnd: 0.3380  bgnd: 0.0198  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:45:20 - mmengine - INFO - Epoch(train)   [2][ 70/123]  lr: 2.0000e-02  eta: 0:30:29  time: 0.0857  data_time: 0.0701  memory: 149  loss: 0.4975  fgnd: 0.8047  bgnd: 0.0302  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:45:21 - mmengine - INFO - Epoch(train)   [2][ 80/123]  lr: 2.0000e-02  eta: 0:30:23  time: 0.0767  data_time: 0.0611  memory: 149  loss: 0.5410  fgnd: 0.6472  bgnd: 0.0296  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:45:23 - mmengine - INFO - Epoch(train)   [2][ 90/123]  lr: 2.0000e-02  eta: 0:30:40  time: 0.1056  data_time: 0.0882  memory: 149  loss: 0.5699  fgnd: 0.4891  bgnd: 0.0209  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:45:24 - mmengine - INFO - Epoch(train)   [2][100/123]  lr: 2.0000e-02  eta: 0:30:08  time: 0.1201  data_time: 0.1028  memory: 149  loss: 0.5795  fgnd: 0.4612  bgnd: 0.0189  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:45:24 - mmengine - INFO - Epoch(train)   [2][110/123]  lr: 2.0000e-02  eta: 0:29:01  time: 0.1202  data_time: 0.1031  memory: 149  loss: 0.5936  fgnd: 0.3823  bgnd: 0.0186  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:45:24 - mmengine - INFO - Epoch(train)   [2][120/123]  lr: 2.0000e-02  eta: 0:28:00  time: 0.0941  data_time: 0.0777  memory: 149  loss: 0.5438  fgnd: 0.3407  bgnd: 0.0185  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:45:24 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_124436
2025/06/23 12:45:24 - mmengine - INFO - Saving checkpoint at 2 epochs
2025/06/23 12:45:25 - mmengine - INFO - Epoch(val)   [2][10/17]    eta: 0:00:00  time: 0.3764  data_time: 0.3667  memory: 149  
2025/06/23 12:45:25 - mmengine - INFO - Epoch(val) [2][17/17]    P: 0.0000  R: 0.0000  F1: 0.0000  data_time: 0.0352  time: 0.0429
2025/06/23 12:45:27 - mmengine - INFO - Epoch(train)   [3][ 10/123]  lr: 2.0000e-02  eta: 0:27:53  time: 0.0871  data_time: 0.0725  memory: 149  loss: 0.4785  fgnd: 0.7682  bgnd: 0.0301  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:45:29 - mmengine - INFO - Epoch(train)   [3][ 20/123]  lr: 2.0000e-02  eta: 0:28:06  time: 0.0820  data_time: 0.0658  memory: 149  loss: 0.5271  fgnd: 0.7224  bgnd: 0.0315  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:45:30 - mmengine - INFO - Epoch(train)   [3][ 30/123]  lr: 2.0000e-02  eta: 0:28:23  time: 0.1123  data_time: 0.0940  memory: 149  loss: 0.5465  fgnd: 0.3815  bgnd: 0.0190  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:45:31 - mmengine - INFO - Epoch(train)   [3][ 40/123]  lr: 2.0000e-02  eta: 0:27:38  time: 0.1154  data_time: 0.0969  memory: 149  loss: 0.5433  fgnd: 0.3993  bgnd: 0.0163  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:45:31 - mmengine - INFO - Epoch(train)   [3][ 50/123]  lr: 2.0000e-02  eta: 0:26:50  time: 0.1159  data_time: 0.0973  memory: 149  loss: 0.5486  fgnd: 0.3673  bgnd: 0.0165  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:45:31 - mmengine - INFO - Epoch(train)   [3][ 60/123]  lr: 2.0000e-02  eta: 0:26:04  time: 0.0868  data_time: 0.0695  memory: 149  loss: 0.4916  fgnd: 0.2843  bgnd: 0.0162  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:45:33 - mmengine - INFO - Epoch(train)   [3][ 70/123]  lr: 2.0000e-02  eta: 0:26:10  time: 0.0826  data_time: 0.0671  memory: 149  loss: 0.4424  fgnd: 0.6362  bgnd: 0.0277  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:45:34 - mmengine - INFO - Epoch(train)   [3][ 80/123]  lr: 2.0000e-02  eta: 0:26:14  time: 0.0750  data_time: 0.0600  memory: 149  loss: 0.4753  fgnd: 0.6811  bgnd: 0.0310  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:45:36 - mmengine - INFO - Epoch(train)   [3][ 90/123]  lr: 2.0000e-02  eta: 0:26:31  time: 0.1044  data_time: 0.0881  memory: 149  loss: 0.5040  fgnd: 0.4031  bgnd: 0.0202  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:45:37 - mmengine - INFO - Epoch(train)   [3][100/123]  lr: 2.0000e-02  eta: 0:26:18  time: 0.1196  data_time: 0.1033  memory: 149  loss: 0.5036  fgnd: 0.3691  bgnd: 0.0163  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:45:37 - mmengine - INFO - Epoch(train)   [3][110/123]  lr: 2.0000e-02  eta: 0:25:40  time: 0.1201  data_time: 0.1035  memory: 149  loss: 0.5116  fgnd: 0.3451  bgnd: 0.0161  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:45:37 - mmengine - INFO - Epoch(train)   [3][120/123]  lr: 2.0000e-02  eta: 0:25:04  time: 0.0948  data_time: 0.0793  memory: 149  loss: 0.4752  fgnd: 0.2875  bgnd: 0.0153  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:45:37 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_124436
2025/06/23 12:45:37 - mmengine - INFO - Saving checkpoint at 3 epochs
2025/06/23 12:45:38 - mmengine - INFO - Epoch(val)   [3][10/17]    eta: 0:00:00  time: 0.2479  data_time: 0.2387  memory: 149  
2025/06/23 12:45:39 - mmengine - INFO - Epoch(val) [3][17/17]    P: 0.0000  R: 0.0000  F1: 0.0000  data_time: 0.0329  time: 0.0407
2025/06/23 12:45:40 - mmengine - INFO - Epoch(train)   [4][ 10/123]  lr: 2.0000e-02  eta: 0:25:03  time: 0.0872  data_time: 0.0726  memory: 149  loss: 0.4236  fgnd: 0.7474  bgnd: 0.0305  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:45:42 - mmengine - INFO - Epoch(train)   [4][ 20/123]  lr: 2.0000e-02  eta: 0:25:13  time: 0.0799  data_time: 0.0651  memory: 149  loss: 0.4776  fgnd: 0.6721  bgnd: 0.0316  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:45:44 - mmengine - INFO - Epoch(train)   [4][ 30/123]  lr: 2.0000e-02  eta: 0:25:29  time: 0.1104  data_time: 0.0931  memory: 149  loss: 0.5004  fgnd: 0.3397  bgnd: 0.0184  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:45:44 - mmengine - INFO - Epoch(train)   [4][ 40/123]  lr: 2.0000e-02  eta: 0:25:02  time: 0.1137  data_time: 0.0959  memory: 149  loss: 0.5041  fgnd: 0.4003  bgnd: 0.0165  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:45:44 - mmengine - INFO - Epoch(train)   [4][ 50/123]  lr: 2.0000e-02  eta: 0:24:31  time: 0.1142  data_time: 0.0963  memory: 149  loss: 0.5164  fgnd: 0.3210  bgnd: 0.0162  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:45:44 - mmengine - INFO - Epoch(train)   [4][ 60/123]  lr: 2.0000e-02  eta: 0:24:02  time: 0.0866  data_time: 0.0698  memory: 149  loss: 0.4690  fgnd: 0.3024  bgnd: 0.0159  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:45:46 - mmengine - INFO - Epoch(train)   [4][ 70/123]  lr: 2.0000e-02  eta: 0:24:07  time: 0.0825  data_time: 0.0663  memory: 149  loss: 0.4264  fgnd: 0.6010  bgnd: 0.0264  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:45:47 - mmengine - INFO - Epoch(train)   [4][ 80/123]  lr: 2.0000e-02  eta: 0:24:10  time: 0.0733  data_time: 0.0580  memory: 149  loss: 0.4643  fgnd: 0.5755  bgnd: 0.0281  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:45:49 - mmengine - INFO - Epoch(train)   [4][ 90/123]  lr: 2.0000e-02  eta: 0:24:23  time: 0.1010  data_time: 0.0830  memory: 149  loss: 0.4910  fgnd: 0.4303  bgnd: 0.0197  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:45:50 - mmengine - INFO - Epoch(train)   [4][100/123]  lr: 2.0000e-02  eta: 0:24:14  time: 0.1148  data_time: 0.0967  memory: 149  loss: 0.4885  fgnd: 0.3629  bgnd: 0.0156  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:45:50 - mmengine - INFO - Epoch(train)   [4][110/123]  lr: 2.0000e-02  eta: 0:23:48  time: 0.1152  data_time: 0.0970  memory: 149  loss: 0.4892  fgnd: 0.3093  bgnd: 0.0150  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:45:50 - mmengine - INFO - Epoch(train)   [4][120/123]  lr: 2.0000e-02  eta: 0:23:22  time: 0.0904  data_time: 0.0729  memory: 149  loss: 0.4467  fgnd: 0.2605  bgnd: 0.0140  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:45:50 - mmengine - INFO - Exp name: fomo_mobnetv2_0.35_x8_coco_20250623_124436
2025/06/23 12:45:50 - mmengine - INFO - Saving checkpoint at 4 epochs
2025/06/23 12:45:51 - mmengine - INFO - Epoch(val)   [4][10/17]    eta: 0:00:00  time: 0.0420  data_time: 0.0339  memory: 149  
2025/06/23 12:45:51 - mmengine - INFO - Epoch(val) [4][17/17]    P: 0.0000  R: 0.0000  F1: 0.0000  data_time: 0.0338  time: 0.0419
2025/06/23 12:45:53 - mmengine - INFO - Epoch(train)   [5][ 10/123]  lr: 2.0000e-02  eta: 0:23:23  time: 0.0850  data_time: 0.0687  memory: 149  loss: 0.3991  fgnd: 0.6221  bgnd: 0.0257  P: 0.0000  R: 0.0000  F1: 0.0000
2025/06/23 12:45:55 - mmengine - INFO - Epoch(train)   [5][ 20/123]  lr: 2.0000e-02  eta: 0:23:34  time: 0.0800  data_time: 0.0637  memory: 149  loss: 0.4477  fgnd: 0.5063  bgnd: 0.0275  P: 0.0000  R: 0.0000  F1: 0.0000
