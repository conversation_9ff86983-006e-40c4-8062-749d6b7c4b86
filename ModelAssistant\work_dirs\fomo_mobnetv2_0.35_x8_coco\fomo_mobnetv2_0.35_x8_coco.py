auto_scale_lr = dict(base_batch_size=16, enable=False)
batch = 8
data_preprocessor = dict(
    bgr_to_rgb=True,
    mean=[
        0,
        0,
        0,
    ],
    pad_size_divisor=32,
    std=[
        255.0,
        255.0,
        255.0,
    ],
    type='sscma.datasets.DetDataPreprocessor')
data_root = 'D:/OBJECT_DETECTION/sscma/datasets/motherboard_led_detection_coco'
dataset_type = 'sscma.datasets.CustomFomoCocoDataset'
default_hooks = dict(
    visualization=dict(
        score_thr=0.8, type='sscma.engine.DetVisualizationHook'))
default_scope = None
deploy = dict(
    data_preprocessor=dict(
        batch_augments=None,
        bgr_to_rgb=False,
        mean=[
            0,
            0,
            0,
        ],
        std=[
            255,
            255,
            255,
        ],
        type='sscma.datasets.DetDataPreprocessor'),
    type='sscma.deploy.models.fomo_infer.FomoInfer')
downsample_factor = (8, )
dump_config = False
env_cfg = dict(
    cudnn_benchmark=True,
    dist_cfg=dict(backend='nccl'),
    mp_cfg=dict(mp_start_method='fork', opencv_num_threads=0))
epochs = 100
find_unused_parameters = True
height = 192
imdecode_backend = 'cv2'
imgsz = (
    192,
    192,
)
launcher = 'none'
load_from = 'ModelAssistant/work_dirs/fomo_mobnetv2_0.35_x8_coco/epoch_100.pth'
log_level = 'INFO'
log_processor = dict(
    by_epoch=True, type='mmengine.runner.LogProcessor', window_size=50)
lr = 0.02
metainfo = None
model = dict(
    backbone=dict(
        out_indices=(2, ),
        rep=False,
        type='sscma.models.MobileNetv2',
        widen_factor=0.35),
    data_preprocessor=dict(
        bgr_to_rgb=True,
        mean=[
            0,
            0,
            0,
        ],
        pad_size_divisor=32,
        std=[
            255.0,
            255.0,
            255.0,
        ],
        type='sscma.datasets.DetDataPreprocessor'),
    head=dict(
        act_cfg='torch.nn.ReLU',
        cls_weight=100.0,
        input_channels=[
            16,
        ],
        loss_bg=dict(reduction='none', type='torch.nn.BCEWithLogitsLoss'),
        loss_cls=dict(reduction='none', type='torch.nn.BCEWithLogitsLoss'),
        middle_channel=48,
        num_classes=2,
        type='sscma.models.FomoHead'),
    skip_preprocessor=True,
    type='sscma.models.Fomo')
momentum = 0.95
num_classes = 2
optim_wrapper = dict(
    optimizer=dict(
        lr=0.02, momentum=0.95, type='torch.optim.SGD', weight_decay=0.0005))
param_scheduler = [
    dict(
        begin=0,
        by_epoch=False,
        end=30,
        start_factor=0.001,
        type='mmengine.optim.LinearLR'),
    dict(
        begin=1,
        by_epoch=True,
        end=100,
        gamma=0.3,
        milestones=[
            50,
            70,
            90,
        ],
        type='mmengine.optim.MultiStepLR'),
]
persistent_workers = True
pre_transform = [
    dict(
        file_client_args=dict(backend='disk'),
        imdecode_backend='cv2',
        type='sscma.datasets.transforms.LoadImageFromFile'),
    dict(type='sscma.datasets.transforms.LoadAnnotations', with_bbox=True),
]
quantizer_config = dict(
    data_preprocessor=dict(
        bgr_to_rgb=True,
        mean=[
            0,
            0,
            0,
        ],
        pad_size_divisor=32,
        std=[
            255.0,
            255.0,
            255.0,
        ],
        type='sscma.datasets.DetDataPreprocessor'),
    head=dict(
        act_cfg='torch.nn.ReLU',
        cls_weight=100.0,
        input_channels=[
            16,
        ],
        loss_bg=dict(reduction='none', type='torch.nn.BCEWithLogitsLoss'),
        loss_cls=dict(reduction='none', type='torch.nn.BCEWithLogitsLoss'),
        middle_channel=48,
        num_classes=2,
        type='sscma.models.FomoHead'),
    type='sscma.quantizer.FomoQuantizer')
randomness = dict(deterministic=False, seed=None)
resume = False
test_cfg = dict(type='mmengine.runner.loops.TestLoop')
test_dataloader = dict(
    batch_size=16,
    dataset=dict(
        ann_file='annotations/instances_val2017.json',
        data_prefix=dict(img='val2017/'),
        data_root=
        'D:/OBJECT_DETECTION/sscma/datasets/motherboard_led_detection_coco',
        filter_cfg=dict(filter_empty_gt=True, min_size=32),
        metainfo=None,
        pipeline=[
            dict(
                file_client_args=dict(backend='disk'),
                imdecode_backend='cv2',
                type='sscma.datasets.transforms.LoadImageFromFile'),
            dict(
                type='sscma.datasets.transforms.LoadAnnotations',
                with_bbox=True),
            dict(
                keep_ratio=True,
                scale=(
                    192,
                    192,
                ),
                type='sscma.datasets.transforms.Resize'),
            dict(
                pad_val=dict(img=(
                    114,
                    114,
                    114,
                )),
                size=(
                    192,
                    192,
                ),
                type='sscma.datasets.transforms.Pad'),
            dict(
                downsample_factor=(8, ),
                num_classes=2,
                type='sscma.datasets.transforms.Bbox2FomoMask'),
            dict(
                meta_keys=(
                    'fomo_mask',
                    'img_id',
                    'img_path',
                    'ori_shape',
                    'img_shape',
                    'scale_factor',
                ),
                type='sscma.datasets.transforms.PackDetInputs'),
        ],
        type='sscma.datasets.CustomFomoCocoDataset'),
    drop_last=False,
    num_workers=2,
    persistent_workers=True)
test_evaluator = dict(type='sscma.evaluation.FomoMetric')
test_pipeline = [
    dict(
        file_client_args=dict(backend='disk'),
        imdecode_backend='cv2',
        type='sscma.datasets.transforms.LoadImageFromFile'),
    dict(type='sscma.datasets.transforms.LoadAnnotations', with_bbox=True),
    dict(
        keep_ratio=True,
        scale=(
            192,
            192,
        ),
        type='sscma.datasets.transforms.Resize'),
    dict(
        pad_val=dict(img=(
            114,
            114,
            114,
        )),
        size=(
            192,
            192,
        ),
        type='sscma.datasets.transforms.Pad'),
    dict(
        downsample_factor=(8, ),
        num_classes=2,
        type='sscma.datasets.transforms.Bbox2FomoMask'),
    dict(
        meta_keys=(
            'fomo_mask',
            'img_id',
            'img_path',
            'ori_shape',
            'img_shape',
            'scale_factor',
        ),
        type='sscma.datasets.transforms.PackDetInputs'),
]
train_ann = 'annotations/instances_train2017.json'
train_cfg = dict(by_epoch=True, max_epochs=100)
train_data = 'train2017/'
train_dataloader = dict(
    batch_size=8,
    dataset=dict(
        ann_file='annotations/instances_train2017.json',
        data_prefix=dict(img='train2017/'),
        data_root=
        'D:/OBJECT_DETECTION/sscma/datasets/motherboard_led_detection_coco',
        filter_cfg=dict(filter_empty_gt=True, min_size=32),
        metainfo=None,
        pipeline=[
            dict(
                file_client_args=dict(backend='disk'),
                imdecode_backend='cv2',
                type='sscma.datasets.transforms.LoadImageFromFile'),
            dict(
                type='sscma.datasets.transforms.LoadAnnotations',
                with_bbox=True),
            dict(type='sscma.datasets.transforms.HSVRandomAug'),
            dict(
                img_scale=(
                    192,
                    192,
                ),
                pad_val=114.0,
                random_pop=False,
                type='sscma.datasets.transforms.Mosaic',
                use_cached=True),
            dict(
                keep_ratio=True,
                scale=(
                    192,
                    192,
                ),
                type='sscma.datasets.transforms.Resize'),
            dict(prob=0.5, type='sscma.datasets.transforms.RandomFlip'),
            dict(
                pad_val=dict(img=(
                    114,
                    114,
                    114,
                )),
                size=(
                    192,
                    192,
                ),
                type='sscma.datasets.transforms.Pad'),
            dict(
                downsample_factor=(8, ),
                num_classes=2,
                type='sscma.datasets.transforms.Bbox2FomoMask'),
            dict(
                meta_keys=(
                    'fomo_mask',
                    'img_path',
                    'img_id',
                    'instances',
                    'img_shape',
                    'ori_shape',
                    'gt_bboxes',
                    'gt_bboxes_labels',
                ),
                type='sscma.datasets.transforms.PackDetInputs'),
        ],
        type='sscma.datasets.CustomFomoCocoDataset'),
    drop_last=False,
    num_workers=2,
    persistent_workers=True)
train_pipeline = [
    dict(
        file_client_args=dict(backend='disk'),
        imdecode_backend='cv2',
        type='sscma.datasets.transforms.LoadImageFromFile'),
    dict(type='sscma.datasets.transforms.LoadAnnotations', with_bbox=True),
    dict(type='sscma.datasets.transforms.HSVRandomAug'),
    dict(
        img_scale=(
            192,
            192,
        ),
        pad_val=114.0,
        random_pop=False,
        type='sscma.datasets.transforms.Mosaic',
        use_cached=True),
    dict(
        keep_ratio=True,
        scale=(
            192,
            192,
        ),
        type='sscma.datasets.transforms.Resize'),
    dict(prob=0.5, type='sscma.datasets.transforms.RandomFlip'),
    dict(
        pad_val=dict(img=(
            114,
            114,
            114,
        )),
        size=(
            192,
            192,
        ),
        type='sscma.datasets.transforms.Pad'),
    dict(
        downsample_factor=(8, ),
        num_classes=2,
        type='sscma.datasets.transforms.Bbox2FomoMask'),
    dict(
        meta_keys=(
            'fomo_mask',
            'img_path',
            'img_id',
            'instances',
            'img_shape',
            'ori_shape',
            'gt_bboxes',
            'gt_bboxes_labels',
        ),
        type='sscma.datasets.transforms.PackDetInputs'),
]
val_ann = 'annotations/instances_val2017.json'
val_batch = 16
val_cfg = dict(type='mmengine.runner.loops.ValLoop')
val_data = 'val2017/'
val_dataloader = dict(
    batch_size=16,
    dataset=dict(
        ann_file='annotations/instances_val2017.json',
        data_prefix=dict(img='val2017/'),
        data_root=
        'D:/OBJECT_DETECTION/sscma/datasets/motherboard_led_detection_coco',
        filter_cfg=dict(filter_empty_gt=True, min_size=32),
        metainfo=None,
        pipeline=[
            dict(
                file_client_args=dict(backend='disk'),
                imdecode_backend='cv2',
                type='sscma.datasets.transforms.LoadImageFromFile'),
            dict(
                type='sscma.datasets.transforms.LoadAnnotations',
                with_bbox=True),
            dict(
                keep_ratio=True,
                scale=(
                    192,
                    192,
                ),
                type='sscma.datasets.transforms.Resize'),
            dict(
                pad_val=dict(img=(
                    114,
                    114,
                    114,
                )),
                size=(
                    192,
                    192,
                ),
                type='sscma.datasets.transforms.Pad'),
            dict(
                downsample_factor=(8, ),
                num_classes=2,
                type='sscma.datasets.transforms.Bbox2FomoMask'),
            dict(
                meta_keys=(
                    'fomo_mask',
                    'img_id',
                    'img_path',
                    'ori_shape',
                    'img_shape',
                    'scale_factor',
                ),
                type='sscma.datasets.transforms.PackDetInputs'),
        ],
        type='sscma.datasets.CustomFomoCocoDataset'),
    drop_last=False,
    num_workers=2,
    persistent_workers=True)
val_evaluator = dict(type='sscma.evaluation.FomoMetric')
val_workers = 2
vis_backends = [
    dict(type='mmengine.visualization.LocalVisBackend'),
    dict(type='mmengine.visualization.TensorboardVisBackend'),
]
visualizer = dict(fomo=True, type='sscma.visualization.FomoLocalVisualizer')
weight_decay = 0.0005
widen_factor = 0.35
width = 192
work_dir = 'ModelAssistant/work_dirs/fomo_mobnetv2_0.35_x8_coco'
workers = 2
