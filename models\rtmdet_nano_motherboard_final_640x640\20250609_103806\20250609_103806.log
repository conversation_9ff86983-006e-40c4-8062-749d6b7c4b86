2025/06/09 10:38:08 - mmengine - INFO - 
------------------------------------------------------------
System environment:
    sys.platform: win32
    Python: 3.12.11 | packaged by Anaconda, Inc. | (main, Jun  5 2025, 12:58:53) [MSC v.1929 64 bit (AMD64)]
    CUDA available: True
    MUSA available: False
    numpy_random_seed: 1953285297
    GPU 0: NVIDIA GeForce RTX 5060 Ti
    CUDA_HOME: None
    MSVC: Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35209 for x64
    GCC: n/a
    PyTorch: 2.8.0.dev20250605+cu128
    PyTorch compiling details: PyTorch built with:
  - C++ Version: 201703
  - MSVC 193833144
  - Intel(R) oneAPI Math Kernel Library Version 2025.1-Product Build 20250306 for Intel(R) 64 architecture applications
  - Intel(R) MKL-DNN v3.7.1 (Git Hash 8d263e693366ef8db40acc569cc7d8edf644556d)
  - OpenMP 2019
  - LAPACK is enabled (usually provided by MKL)
  - CPU capability usage: AVX512
  - CUDA Runtime 12.8
  - NVCC architecture flags: -gencode;arch=compute_61,code=sm_61;-gencode;arch=compute_70,code=sm_70;-gencode;arch=compute_75,code=sm_75;-gencode;arch=compute_80,code=sm_80;-gencode;arch=compute_86,code=sm_86;-gencode;arch=compute_90,code=sm_90;-gencode;arch=compute_100,code=sm_100;-gencode;arch=compute_120,code=sm_120
  - CuDNN 90.7.1
  - Magma 2.5.4
  - Build settings: BLAS_INFO=mkl, BUILD_TYPE=Release, COMMIT_SHA=d53869579d491ac5c9b8a60419c015062f904a3e, CUDA_VERSION=12.8, CUDNN_VERSION=9.7.1, CXX_COMPILER=C:/actions-runner/_work/pytorch/pytorch/pytorch/.ci/pytorch/windows/tmp_bin/sccache-cl.exe, CXX_FLAGS=/DWIN32 /D_WINDOWS /GR /EHsc /Zc:__cplusplus /bigobj /FS /utf-8 -DUSE_PTHREADPOOL -DNDEBUG -DUSE_KINETO -DLIBKINETO_NOROCTRACER -DLIBKINETO_NOXPUPTI=ON -DUSE_FBGEMM -DUSE_XNNPACK -DSYMBOLICATE_MOBILE_DEBUG_HANDLE /wd4624 /wd4068 /wd4067 /wd4267 /wd4661 /wd4717 /wd4244 /wd4804 /wd4273, LAPACK_INFO=mkl, PERF_WITH_AVX=1, PERF_WITH_AVX2=1, TORCH_VERSION=2.8.0, USE_CUDA=ON, USE_CUDNN=ON, USE_CUSPARSELT=OFF, USE_GFLAGS=OFF, USE_GLOG=OFF, USE_GLOO=ON, USE_MKL=ON, USE_MKLDNN=ON, USE_MPI=OFF, USE_NCCL=OFF, USE_NNPACK=OFF, USE_OPENMP=ON, USE_ROCM=OFF, USE_ROCM_KERNEL_ASSERT=OFF, USE_XCCL=OFF, USE_XPU=OFF, 

    TorchVision: 0.23.0.dev20250606+cu128
    OpenCV: 4.11.0
    MMEngine: 0.10.4

Runtime environment:
    cudnn_benchmark: True
    mp_cfg: {'mp_start_method': 'fork', 'opencv_num_threads': 0}
    dist_cfg: {'backend': 'nccl'}
    seed: 1953285297
    deterministic: False
    Distributed launcher: none
    Distributed training: False
    GPU number: 1
------------------------------------------------------------

2025/06/09 10:38:09 - mmengine - INFO - Config:
auto_scale_lr = dict(base_batch_size=32, enable=True)
backend_args = None
base_lr = 0.0005
batch_shapes_cfg = dict(
    batch_size=32,
    extra_pad_ratio=0.5,
    img_size=640,
    size_divisor=32,
    type='sscma.datasets.BatchShapePolicy')
batch_size = 32
checkpoint = 'https://download.openmmlab.com/mmdetection/v3.0/rtmdet/cspnext_rsb_pretrain/cspnext-s_imagenet_600e.pth'
custom_hooks = [
    dict(
        ema_type='sscma.models.ExpMomentumEMA',
        momentum=0.0002,
        priority=49,
        type='mmengine.hooks.EMAHook',
        update_buffers=True),
    dict(
        switch_epoch=280,
        switch_pipeline=[
            dict(
                backend_args=None,
                imdecode_backend='cv2',
                type='sscma.datasets.transforms.LoadImageFromFile'),
            dict(
                imdecode_backend='cv2',
                type='sscma.datasets.transforms.LoadAnnotations',
                with_bbox=True),
            dict(
                keep_ratio=True,
                ratio_range=(
                    0.5,
                    2.0,
                ),
                resize_type='sscma.datasets.transforms.Resize',
                scale=(
                    1280,
                    1280,
                ),
                type='sscma.datasets.transforms.RandomResize'),
            dict(
                crop_size=(
                    640,
                    640,
                ),
                type='sscma.datasets.transforms.RandomCrop'),
            dict(type='sscma.datasets.transforms.HSVRandomAug'),
            dict(prob=0.5, type='sscma.datasets.transforms.RandomFlip'),
            dict(
                pad_val=dict(img=(
                    114,
                    114,
                    114,
                )),
                size=(
                    640,
                    640,
                ),
                type='sscma.datasets.transforms.Pad'),
            dict(type='sscma.datasets.transforms.PackDetInputs'),
        ],
        type='sscma.engine.PipelineSwitchHook'),
]
d_factor = 0.33
data_root = '../datasets/motherboard_led_detection_coco/'
dataset_type = 'sscma.datasets.CustomFomoCocoDataset'
default_hooks = dict(
    checkpoint=dict(
        interval=10,
        max_keep_ckpts=3,
        save_best='auto',
        type='mmengine.hooks.CheckpointHook'),
    logger=dict(interval=100, type='mmengine.hooks.LoggerHook'),
    param_scheduler=dict(type='mmengine.hooks.ParamSchedulerHook'),
    sampler_seed=dict(type='mmengine.hooks.DistSamplerSeedHook'),
    timer=dict(type='mmengine.hooks.IterTimerHook'),
    visualization=dict(
        draw=True,
        test_out_dir='works',
        type='sscma.engine.DetVisualizationHook'))
default_scope = None
deploy = dict(
    data_preprocessor=dict(
        batch_augments=None,
        bgr_to_rgb=False,
        mean=[
            0,
            0,
            0,
        ],
        std=[
            255,
            255,
            255,
        ],
        type='sscma.datasets.DetDataPreprocessor'),
    type='sscma.deploy.models.RTMDetInfer')
dump_config = True
env_cfg = dict(
    cudnn_benchmark=True,
    dist_cfg=dict(backend='nccl'),
    mp_cfg=dict(mp_start_method='fork', opencv_num_threads=0))
epochs = 300
imdecode_backend = 'cv2'
imgsz = (
    640,
    640,
)
interval = 10
launcher = 'none'
load_from = None
log_level = 'INFO'
log_processor = dict(
    by_epoch=True, type='mmengine.runner.LogProcessor', window_size=50)
mixup_max_cached_images = 10
model = dict(
    backbone=dict(
        act_cfg=dict(inplace=True, type='torch.nn.ReLU6'),
        arch='P5',
        channel_attention=False,
        deepen_factor=0.33,
        expand_ratio=0.5,
        init_cfg=dict(
            checkpoint=
            'https://download.openmmlab.com/mmdetection/v3.0/rtmdet/cspnext_rsb_pretrain/cspnext-s_imagenet_600e.pth',
            prefix='backbone.',
            type='Pretrained'),
        norm_cfg=dict(type='torch.nn.BatchNorm2d'),
        split_max_pool_kernel=False,
        type='sscma.models.CSPNeXt',
        widen_factor=0.25),
    bbox_head=dict(
        bbox_coder=dict(type='sscma.models.DistancePointBBoxCoder'),
        head_module=dict(
            act_cfg=dict(inplace=True, type='torch.nn.ReLU6'),
            feat_channels=256,
            featmap_strides=[
                8,
                16,
                32,
            ],
            in_channels=256,
            norm_cfg=dict(type='torch.nn.BatchNorm2d'),
            num_classes=2,
            pred_kernel_size=1,
            share_conv=False,
            stacked_convs=2,
            type='sscma.models.RTMDetSepBNHeadModule',
            widen_factor=0.25),
        loss_bbox=dict(loss_weight=2.0, type='sscma.models.GIoULoss'),
        loss_cls=dict(
            beta=2.0,
            loss_weight=1.0,
            type='sscma.models.QualityFocalLoss',
            use_sigmoid=True),
        prior_generator=dict(
            offset=0,
            strides=[
                8,
                16,
                32,
            ],
            type='sscma.models.MlvlPointGenerator'),
        test_cfg=dict(
            max_per_img=300,
            min_bbox_size=0,
            multi_label=True,
            nms=dict(iou_threshold=0.65, type='torchvision.ops.nms'),
            nms_pre=30000,
            score_thr=0.001),
        train_cfg=dict(
            allowed_border=-1,
            assigner=dict(
                iou_calculator=dict(type='sscma.models.BboxOverlaps2D'),
                num_classes=2,
                topk=13,
                type='sscma.models.BatchDynamicSoftLabelAssigner'),
            debug=False,
            pos_weight=-1),
        type='sscma.models.RTMDetHead'),
    data_preprocessor=dict(
        batch_augments=None,
        bgr_to_rgb=False,
        mean=[
            0,
            0,
            0,
        ],
        std=[
            255,
            255,
            255,
        ],
        type='sscma.datasets.DetDataPreprocessor'),
    neck=dict(
        act_cfg=dict(inplace=True, type='torch.nn.ReLU6'),
        deepen_factor=0.33,
        expand_ratio=0.5,
        in_channels=[
            256,
            512,
            1024,
        ],
        norm_cfg=dict(type='torch.nn.BatchNorm2d'),
        num_csp_blocks=3,
        out_channels=256,
        type='sscma.models.CSPNeXtPAFPN',
        widen_factor=0.25),
    test_cfg=dict(
        max_per_img=300,
        min_bbox_size=0,
        multi_label=True,
        nms=dict(iou_threshold=0.65, type='torchvision.ops.nms'),
        nms_pre=30000,
        score_thr=0.001),
    train_cfg=dict(
        allowed_border=-1,
        assigner=dict(
            iou_calculator=dict(type='sscma.models.BboxOverlaps2D'),
            num_classes=2,
            topk=13,
            type='sscma.models.BatchDynamicSoftLabelAssigner'),
        debug=False,
        pos_weight=-1),
    type='sscma.models.RTMDet')
mosaic_max_cached_images = 20
num_classes = 2
num_workers = 16
optim_wrapper = dict(
    optimizer=dict(
        lr=0.0005, type='torch.optim.adamw.AdamW', weight_decay=0.05),
    paramwise_cfg=dict(
        bias_decay_mult=0, bypass_duplicate=True, norm_decay_mult=0),
    type='mmengine.optim.AmpOptimWrapper')
param_scheduler = [
    dict(
        begin=0,
        by_epoch=False,
        end=2000,
        start_factor=1e-05,
        type='mmengine.optim.LinearLR'),
    dict(
        T_max=150,
        begin=150,
        by_epoch=True,
        convert_to_iter_based=True,
        end=300,
        eta_min=2.5e-05,
        type='mmengine.optim.CosineAnnealingLR'),
]
quantizer_config = dict(
    bbox_head=dict(
        bbox_coder=dict(type='sscma.models.DistancePointBBoxCoder'),
        head_module=dict(
            act_cfg=dict(inplace=True, type='torch.nn.ReLU6'),
            feat_channels=256,
            featmap_strides=[
                8,
                16,
                32,
            ],
            in_channels=256,
            norm_cfg=dict(type='torch.nn.BatchNorm2d'),
            num_classes=2,
            pred_kernel_size=1,
            share_conv=False,
            stacked_convs=2,
            type='sscma.models.RTMDetSepBNHeadModule',
            widen_factor=0.25),
        loss_bbox=dict(loss_weight=2.0, type='sscma.models.GIoULoss'),
        loss_cls=dict(
            beta=2.0,
            loss_weight=1.0,
            type='sscma.models.QualityFocalLoss',
            use_sigmoid=True),
        prior_generator=dict(
            offset=0,
            strides=[
                8,
                16,
                32,
            ],
            type='sscma.models.MlvlPointGenerator'),
        test_cfg=dict(
            max_per_img=300,
            min_bbox_size=0,
            multi_label=True,
            nms=dict(iou_threshold=0.65, type='torchvision.ops.nms'),
            nms_pre=30000,
            score_thr=0.001),
        train_cfg=dict(
            allowed_border=-1,
            assigner=dict(
                iou_calculator=dict(type='sscma.models.BboxOverlaps2D'),
                num_classes=2,
                topk=13,
                type='sscma.models.BatchDynamicSoftLabelAssigner'),
            debug=False,
            pos_weight=-1),
        type='sscma.models.RTMDetHead'),
    data_preprocessor=dict(
        batch_augments=None,
        bgr_to_rgb=False,
        mean=[
            0,
            0,
            0,
        ],
        std=[
            255,
            255,
            255,
        ],
        type='sscma.datasets.DetDataPreprocessor'),
    type='sscma.quantizer.RtmdetQuantModel')
random_resize_ratio_range = (
    0.5,
    2.0,
)
randomness = dict(deterministic=False, seed=None)
resume = False
stage2_num_epochs = 20
test_cfg = dict(type='mmengine.runner.loops.TestLoop')
test_dataloader = dict(
    batch_size=16,
    dataset=dict(
        ann_file='annotations/instances_val2017.json',
        batch_shapes_cfg=dict(
            batch_size=32,
            extra_pad_ratio=0.5,
            img_size=640,
            size_divisor=32,
            type='sscma.datasets.BatchShapePolicy'),
        data_prefix=dict(img='val2017/'),
        data_root='../datasets/motherboard_led_detection_coco/',
        pipeline=[
            dict(
                backend_args=None,
                imdecode_backend='cv2',
                type='sscma.datasets.transforms.LoadImageFromFile'),
            dict(
                imdecode_backend='cv2',
                type='sscma.datasets.transforms.LoadAnnotations',
                with_bbox=True),
            dict(
                keep_ratio=True,
                scale=(
                    640,
                    640,
                ),
                type='sscma.datasets.transforms.Resize'),
            dict(
                pad_val=dict(img=(
                    114,
                    114,
                    114,
                )),
                size=(
                    640,
                    640,
                ),
                type='sscma.datasets.transforms.Pad'),
            dict(
                meta_keys=(
                    'img_id',
                    'img_path',
                    'ori_shape',
                    'img_shape',
                    'scale_factor',
                ),
                type='sscma.datasets.transforms.PackDetInputs'),
        ],
        test_mode=True,
        type='sscma.datasets.CustomFomoCocoDataset'),
    drop_last=False,
    num_workers=8,
    persistent_workers=True,
    pin_memory=True,
    sampler=dict(
        shuffle=False, type='mmengine.dataset.sampler.DefaultSampler'))
test_evaluator = dict(
    ann_file=
    '../datasets/motherboard_led_detection_coco/annotations/instances_val2017.json',
    backend_args=None,
    format_only=False,
    metric='bbox',
    proposal_nums=(
        100,
        1,
        10,
    ),
    sort_categories=True,
    type='sscma.evaluation.CocoMetric')
test_pipeline = [
    dict(
        backend_args=None,
        imdecode_backend='cv2',
        type='sscma.datasets.transforms.LoadImageFromFile'),
    dict(
        imdecode_backend='cv2',
        type='sscma.datasets.transforms.LoadAnnotations',
        with_bbox=True),
    dict(
        keep_ratio=True,
        scale=(
            640,
            640,
        ),
        type='sscma.datasets.transforms.Resize'),
    dict(
        pad_val=dict(img=(
            114,
            114,
            114,
        )),
        size=(
            640,
            640,
        ),
        type='sscma.datasets.transforms.Pad'),
    dict(
        meta_keys=(
            'img_id',
            'img_path',
            'ori_shape',
            'img_shape',
            'scale_factor',
        ),
        type='sscma.datasets.transforms.PackDetInputs'),
]
train_ann_file = 'annotations/instances_train2017.json'
train_cfg = dict(
    dynamic_intervals=[
        (
            280,
            1,
        ),
    ],
    max_epochs=300,
    type='mmengine.runner.loops.EpochBasedTrainLoop',
    val_interval=10)
train_dataloader = dict(
    batch_sampler=None,
    batch_size=32,
    collate_fn='sscma.datasets.coco_collate',
    dataset=dict(
        ann_file='annotations/instances_train2017.json',
        data_prefix=dict(img='train2017/'),
        data_root='../datasets/motherboard_led_detection_coco/',
        filter_cfg=dict(filter_empty_gt=True, min_size=32),
        pipeline=[
            dict(
                backend_args=None,
                imdecode_backend='cv2',
                type='sscma.datasets.transforms.LoadImageFromFile'),
            dict(
                imdecode_backend='cv2',
                type='sscma.datasets.transforms.LoadAnnotations',
                with_bbox=True),
            dict(
                img_scale=(
                    640,
                    640,
                ),
                max_cached_images=20,
                pad_val=114.0,
                random_pop=False,
                type='sscma.datasets.transforms.Mosaic',
                use_cached=True),
            dict(
                keep_ratio=True,
                ratio_range=(
                    0.5,
                    2.0,
                ),
                resize_type='sscma.datasets.transforms.Resize',
                scale=(
                    1280,
                    1280,
                ),
                type='sscma.datasets.transforms.RandomResize'),
            dict(
                crop_size=(
                    640,
                    640,
                ),
                type='sscma.datasets.transforms.RandomCrop'),
            dict(type='sscma.datasets.transforms.HSVRandomAug'),
            dict(prob=0.5, type='sscma.datasets.transforms.RandomFlip'),
            dict(
                pad_val=dict(img=(
                    114,
                    114,
                    114,
                )),
                size=(
                    640,
                    640,
                ),
                type='sscma.datasets.transforms.Pad'),
            dict(
                max_cached_images=10,
                prob=0.5,
                random_pop=False,
                type='sscma.datasets.transforms.MixUp',
                use_cached=True),
            dict(type='sscma.datasets.transforms.PackDetInputs'),
        ],
        type='sscma.datasets.CustomFomoCocoDataset'),
    num_workers=16,
    persistent_workers=True,
    pin_memory=True,
    sampler=dict(shuffle=True, type='mmengine.dataset.sampler.DefaultSampler'))
train_img_prefix = 'train2017/'
train_pipeline = [
    dict(
        backend_args=None,
        imdecode_backend='cv2',
        type='sscma.datasets.transforms.LoadImageFromFile'),
    dict(
        imdecode_backend='cv2',
        type='sscma.datasets.transforms.LoadAnnotations',
        with_bbox=True),
    dict(
        img_scale=(
            640,
            640,
        ),
        max_cached_images=20,
        pad_val=114.0,
        random_pop=False,
        type='sscma.datasets.transforms.Mosaic',
        use_cached=True),
    dict(
        keep_ratio=True,
        ratio_range=(
            0.5,
            2.0,
        ),
        resize_type='sscma.datasets.transforms.Resize',
        scale=(
            1280,
            1280,
        ),
        type='sscma.datasets.transforms.RandomResize'),
    dict(crop_size=(
        640,
        640,
    ), type='sscma.datasets.transforms.RandomCrop'),
    dict(type='sscma.datasets.transforms.HSVRandomAug'),
    dict(prob=0.5, type='sscma.datasets.transforms.RandomFlip'),
    dict(
        pad_val=dict(img=(
            114,
            114,
            114,
        )),
        size=(
            640,
            640,
        ),
        type='sscma.datasets.transforms.Pad'),
    dict(
        max_cached_images=10,
        prob=0.5,
        random_pop=False,
        type='sscma.datasets.transforms.MixUp',
        use_cached=True),
    dict(type='sscma.datasets.transforms.PackDetInputs'),
]
train_pipeline_stage2 = [
    dict(
        backend_args=None,
        imdecode_backend='cv2',
        type='sscma.datasets.transforms.LoadImageFromFile'),
    dict(
        imdecode_backend='cv2',
        type='sscma.datasets.transforms.LoadAnnotations',
        with_bbox=True),
    dict(
        keep_ratio=True,
        ratio_range=(
            0.5,
            2.0,
        ),
        resize_type='sscma.datasets.transforms.Resize',
        scale=(
            1280,
            1280,
        ),
        type='sscma.datasets.transforms.RandomResize'),
    dict(crop_size=(
        640,
        640,
    ), type='sscma.datasets.transforms.RandomCrop'),
    dict(type='sscma.datasets.transforms.HSVRandomAug'),
    dict(prob=0.5, type='sscma.datasets.transforms.RandomFlip'),
    dict(
        pad_val=dict(img=(
            114,
            114,
            114,
        )),
        size=(
            640,
            640,
        ),
        type='sscma.datasets.transforms.Pad'),
    dict(type='sscma.datasets.transforms.PackDetInputs'),
]
val_ann_file = 'annotations/instances_val2017.json'
val_cfg = dict(type='mmengine.runner.loops.ValLoop')
val_dataloader = dict(
    batch_size=16,
    dataset=dict(
        ann_file='annotations/instances_val2017.json',
        batch_shapes_cfg=dict(
            batch_size=32,
            extra_pad_ratio=0.5,
            img_size=640,
            size_divisor=32,
            type='sscma.datasets.BatchShapePolicy'),
        data_prefix=dict(img='val2017/'),
        data_root='../datasets/motherboard_led_detection_coco/',
        pipeline=[
            dict(
                backend_args=None,
                imdecode_backend='cv2',
                type='sscma.datasets.transforms.LoadImageFromFile'),
            dict(
                imdecode_backend='cv2',
                type='sscma.datasets.transforms.LoadAnnotations',
                with_bbox=True),
            dict(
                keep_ratio=True,
                scale=(
                    640,
                    640,
                ),
                type='sscma.datasets.transforms.Resize'),
            dict(
                pad_val=dict(img=(
                    114,
                    114,
                    114,
                )),
                size=(
                    640,
                    640,
                ),
                type='sscma.datasets.transforms.Pad'),
            dict(
                meta_keys=(
                    'img_id',
                    'img_path',
                    'ori_shape',
                    'img_shape',
                    'scale_factor',
                ),
                type='sscma.datasets.transforms.PackDetInputs'),
        ],
        test_mode=True,
        type='sscma.datasets.CustomFomoCocoDataset'),
    drop_last=False,
    num_workers=8,
    persistent_workers=True,
    pin_memory=True,
    sampler=dict(
        shuffle=False, type='mmengine.dataset.sampler.DefaultSampler'))
val_evaluator = dict(
    ann_file=
    '../datasets/motherboard_led_detection_coco/annotations/instances_val2017.json',
    backend_args=None,
    format_only=False,
    metric='bbox',
    proposal_nums=(
        100,
        1,
        10,
    ),
    sort_categories=True,
    type='sscma.evaluation.CocoMetric')
val_img_prefix = 'val2017/'
vis_backends = [
    dict(type='mmengine.visualization.LocalVisBackend'),
    dict(type='mmengine.visualization.TensorboardVisBackend'),
]
visualizer = dict(
    name='visualizer',
    type='sscma.visualization.DetLocalVisualizer',
    vis_backends=[
        dict(type='mmengine.visualization.LocalVisBackend'),
        dict(type='mmengine.visualization.TensorboardVisBackend'),
    ])
w_factor = 0.25
work_dir = 'work_dirs\\rtmdet_nano_motherboard_final'

2025/06/09 10:38:11 - mmengine - INFO - Distributed training is not used, all SyncBatchNorm (SyncBN) layers in the model will be automatically reverted to BatchNormXd layers if they are used.
2025/06/09 10:38:12 - mmengine - INFO - Hooks will be executed in the following order:
before_run:
(VERY_HIGH   ) RuntimeInfoHook                    
(49          ) EMAHook                            
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
after_load_checkpoint:
(49          ) EMAHook                            
 -------------------- 
before_train:
(VERY_HIGH   ) RuntimeInfoHook                    
(49          ) EMAHook                            
(NORMAL      ) IterTimerHook                      
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
before_train_epoch:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(NORMAL      ) DistSamplerSeedHook                
(NORMAL      ) PipelineSwitchHook                 
 -------------------- 
before_train_iter:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
 -------------------- 
after_train_iter:
(VERY_HIGH   ) RuntimeInfoHook                    
(49          ) EMAHook                            
(NORMAL      ) IterTimerHook                      
(BELOW_NORMAL) LoggerHook                         
(LOW         ) ParamSchedulerHook                 
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
after_train_epoch:
(NORMAL      ) IterTimerHook                      
(LOW         ) ParamSchedulerHook                 
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
before_val:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
before_val_epoch:
(49          ) EMAHook                            
(NORMAL      ) IterTimerHook                      
 -------------------- 
before_val_iter:
(NORMAL      ) IterTimerHook                      
 -------------------- 
after_val_iter:
(NORMAL      ) IterTimerHook                      
(NORMAL      ) DetVisualizationHook               
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
after_val_epoch:
(VERY_HIGH   ) RuntimeInfoHook                    
(49          ) EMAHook                            
(NORMAL      ) IterTimerHook                      
(BELOW_NORMAL) LoggerHook                         
(LOW         ) ParamSchedulerHook                 
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
after_val:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
before_save_checkpoint:
(49          ) EMAHook                            
 -------------------- 
after_train:
(VERY_HIGH   ) RuntimeInfoHook                    
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
before_test:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
before_test_epoch:
(49          ) EMAHook                            
(NORMAL      ) IterTimerHook                      
 -------------------- 
before_test_iter:
(NORMAL      ) IterTimerHook                      
 -------------------- 
after_test_iter:
(NORMAL      ) IterTimerHook                      
(NORMAL      ) DetVisualizationHook               
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
after_test_epoch:
(VERY_HIGH   ) RuntimeInfoHook                    
(49          ) EMAHook                            
(NORMAL      ) IterTimerHook                      
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
after_test:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
after_run:
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
2025/06/09 10:38:13 - mmengine - INFO - LR is set based on batch size of 32 and the current batch size is 32. Scaling the original LR by 1.0.
2025/06/09 10:38:13 - mmengine - INFO - load backbone. in model from: https://download.openmmlab.com/mmdetection/v3.0/rtmdet/cspnext_rsb_pretrain/cspnext-s_imagenet_600e.pth
2025/06/09 10:38:20 - mmengine - WARNING - The model and loaded state dict do not match exactly

size mismatch for stem.0.conv.weight: copying a param with shape torch.Size([16, 3, 3, 3]) from checkpoint, the shape in current model is torch.Size([8, 3, 3, 3]).
size mismatch for stem.0.bn.weight: copying a param with shape torch.Size([16]) from checkpoint, the shape in current model is torch.Size([8]).
size mismatch for stem.0.bn.bias: copying a param with shape torch.Size([16]) from checkpoint, the shape in current model is torch.Size([8]).
size mismatch for stem.0.bn.running_mean: copying a param with shape torch.Size([16]) from checkpoint, the shape in current model is torch.Size([8]).
size mismatch for stem.0.bn.running_var: copying a param with shape torch.Size([16]) from checkpoint, the shape in current model is torch.Size([8]).
size mismatch for stem.1.conv.weight: copying a param with shape torch.Size([16, 16, 3, 3]) from checkpoint, the shape in current model is torch.Size([8, 8, 3, 3]).
size mismatch for stem.1.bn.weight: copying a param with shape torch.Size([16]) from checkpoint, the shape in current model is torch.Size([8]).
size mismatch for stem.1.bn.bias: copying a param with shape torch.Size([16]) from checkpoint, the shape in current model is torch.Size([8]).
size mismatch for stem.1.bn.running_mean: copying a param with shape torch.Size([16]) from checkpoint, the shape in current model is torch.Size([8]).
size mismatch for stem.1.bn.running_var: copying a param with shape torch.Size([16]) from checkpoint, the shape in current model is torch.Size([8]).
size mismatch for stem.2.conv.weight: copying a param with shape torch.Size([32, 16, 3, 3]) from checkpoint, the shape in current model is torch.Size([16, 8, 3, 3]).
size mismatch for stem.2.bn.weight: copying a param with shape torch.Size([32]) from checkpoint, the shape in current model is torch.Size([16]).
size mismatch for stem.2.bn.bias: copying a param with shape torch.Size([32]) from checkpoint, the shape in current model is torch.Size([16]).
size mismatch for stem.2.bn.running_mean: copying a param with shape torch.Size([32]) from checkpoint, the shape in current model is torch.Size([16]).
size mismatch for stem.2.bn.running_var: copying a param with shape torch.Size([32]) from checkpoint, the shape in current model is torch.Size([16]).
size mismatch for stage1.0.conv.weight: copying a param with shape torch.Size([64, 32, 3, 3]) from checkpoint, the shape in current model is torch.Size([32, 16, 3, 3]).
size mismatch for stage1.0.bn.weight: copying a param with shape torch.Size([64]) from checkpoint, the shape in current model is torch.Size([32]).
size mismatch for stage1.0.bn.bias: copying a param with shape torch.Size([64]) from checkpoint, the shape in current model is torch.Size([32]).
size mismatch for stage1.0.bn.running_mean: copying a param with shape torch.Size([64]) from checkpoint, the shape in current model is torch.Size([32]).
size mismatch for stage1.0.bn.running_var: copying a param with shape torch.Size([64]) from checkpoint, the shape in current model is torch.Size([32]).
size mismatch for stage1.1.main_conv.conv.weight: copying a param with shape torch.Size([32, 64, 1, 1]) from checkpoint, the shape in current model is torch.Size([16, 32, 1, 1]).
size mismatch for stage1.1.main_conv.bn.weight: copying a param with shape torch.Size([32]) from checkpoint, the shape in current model is torch.Size([16]).
size mismatch for stage1.1.main_conv.bn.bias: copying a param with shape torch.Size([32]) from checkpoint, the shape in current model is torch.Size([16]).
size mismatch for stage1.1.main_conv.bn.running_mean: copying a param with shape torch.Size([32]) from checkpoint, the shape in current model is torch.Size([16]).
size mismatch for stage1.1.main_conv.bn.running_var: copying a param with shape torch.Size([32]) from checkpoint, the shape in current model is torch.Size([16]).
size mismatch for stage1.1.short_conv.conv.weight: copying a param with shape torch.Size([32, 64, 1, 1]) from checkpoint, the shape in current model is torch.Size([16, 32, 1, 1]).
size mismatch for stage1.1.short_conv.bn.weight: copying a param with shape torch.Size([32]) from checkpoint, the shape in current model is torch.Size([16]).
size mismatch for stage1.1.short_conv.bn.bias: copying a param with shape torch.Size([32]) from checkpoint, the shape in current model is torch.Size([16]).
size mismatch for stage1.1.short_conv.bn.running_mean: copying a param with shape torch.Size([32]) from checkpoint, the shape in current model is torch.Size([16]).
size mismatch for stage1.1.short_conv.bn.running_var: copying a param with shape torch.Size([32]) from checkpoint, the shape in current model is torch.Size([16]).
size mismatch for stage1.1.final_conv.conv.weight: copying a param with shape torch.Size([64, 64, 1, 1]) from checkpoint, the shape in current model is torch.Size([32, 32, 1, 1]).
size mismatch for stage1.1.final_conv.bn.weight: copying a param with shape torch.Size([64]) from checkpoint, the shape in current model is torch.Size([32]).
size mismatch for stage1.1.final_conv.bn.bias: copying a param with shape torch.Size([64]) from checkpoint, the shape in current model is torch.Size([32]).
size mismatch for stage1.1.final_conv.bn.running_mean: copying a param with shape torch.Size([64]) from checkpoint, the shape in current model is torch.Size([32]).
size mismatch for stage1.1.final_conv.bn.running_var: copying a param with shape torch.Size([64]) from checkpoint, the shape in current model is torch.Size([32]).
size mismatch for stage1.1.blocks.0.conv1.conv.weight: copying a param with shape torch.Size([32, 32, 3, 3]) from checkpoint, the shape in current model is torch.Size([16, 16, 3, 3]).
size mismatch for stage1.1.blocks.0.conv1.bn.weight: copying a param with shape torch.Size([32]) from checkpoint, the shape in current model is torch.Size([16]).
size mismatch for stage1.1.blocks.0.conv1.bn.bias: copying a param with shape torch.Size([32]) from checkpoint, the shape in current model is torch.Size([16]).
size mismatch for stage1.1.blocks.0.conv1.bn.running_mean: copying a param with shape torch.Size([32]) from checkpoint, the shape in current model is torch.Size([16]).
size mismatch for stage1.1.blocks.0.conv1.bn.running_var: copying a param with shape torch.Size([32]) from checkpoint, the shape in current model is torch.Size([16]).
size mismatch for stage1.1.blocks.0.conv2.depthwise_conv.conv.weight: copying a param with shape torch.Size([32, 1, 5, 5]) from checkpoint, the shape in current model is torch.Size([16, 1, 5, 5]).
size mismatch for stage1.1.blocks.0.conv2.depthwise_conv.bn.weight: copying a param with shape torch.Size([32]) from checkpoint, the shape in current model is torch.Size([16]).
size mismatch for stage1.1.blocks.0.conv2.depthwise_conv.bn.bias: copying a param with shape torch.Size([32]) from checkpoint, the shape in current model is torch.Size([16]).
size mismatch for stage1.1.blocks.0.conv2.depthwise_conv.bn.running_mean: copying a param with shape torch.Size([32]) from checkpoint, the shape in current model is torch.Size([16]).
size mismatch for stage1.1.blocks.0.conv2.depthwise_conv.bn.running_var: copying a param with shape torch.Size([32]) from checkpoint, the shape in current model is torch.Size([16]).
size mismatch for stage1.1.blocks.0.conv2.pointwise_conv.conv.weight: copying a param with shape torch.Size([32, 32, 1, 1]) from checkpoint, the shape in current model is torch.Size([16, 16, 1, 1]).
size mismatch for stage1.1.blocks.0.conv2.pointwise_conv.bn.weight: copying a param with shape torch.Size([32]) from checkpoint, the shape in current model is torch.Size([16]).
size mismatch for stage1.1.blocks.0.conv2.pointwise_conv.bn.bias: copying a param with shape torch.Size([32]) from checkpoint, the shape in current model is torch.Size([16]).
size mismatch for stage1.1.blocks.0.conv2.pointwise_conv.bn.running_mean: copying a param with shape torch.Size([32]) from checkpoint, the shape in current model is torch.Size([16]).
size mismatch for stage1.1.blocks.0.conv2.pointwise_conv.bn.running_var: copying a param with shape torch.Size([32]) from checkpoint, the shape in current model is torch.Size([16]).
size mismatch for stage2.0.conv.weight: copying a param with shape torch.Size([128, 64, 3, 3]) from checkpoint, the shape in current model is torch.Size([64, 32, 3, 3]).
size mismatch for stage2.0.bn.weight: copying a param with shape torch.Size([128]) from checkpoint, the shape in current model is torch.Size([64]).
size mismatch for stage2.0.bn.bias: copying a param with shape torch.Size([128]) from checkpoint, the shape in current model is torch.Size([64]).
size mismatch for stage2.0.bn.running_mean: copying a param with shape torch.Size([128]) from checkpoint, the shape in current model is torch.Size([64]).
size mismatch for stage2.0.bn.running_var: copying a param with shape torch.Size([128]) from checkpoint, the shape in current model is torch.Size([64]).
size mismatch for stage2.1.main_conv.conv.weight: copying a param with shape torch.Size([64, 128, 1, 1]) from checkpoint, the shape in current model is torch.Size([32, 64, 1, 1]).
size mismatch for stage2.1.main_conv.bn.weight: copying a param with shape torch.Size([64]) from checkpoint, the shape in current model is torch.Size([32]).
size mismatch for stage2.1.main_conv.bn.bias: copying a param with shape torch.Size([64]) from checkpoint, the shape in current model is torch.Size([32]).
size mismatch for stage2.1.main_conv.bn.running_mean: copying a param with shape torch.Size([64]) from checkpoint, the shape in current model is torch.Size([32]).
size mismatch for stage2.1.main_conv.bn.running_var: copying a param with shape torch.Size([64]) from checkpoint, the shape in current model is torch.Size([32]).
size mismatch for stage2.1.short_conv.conv.weight: copying a param with shape torch.Size([64, 128, 1, 1]) from checkpoint, the shape in current model is torch.Size([32, 64, 1, 1]).
size mismatch for stage2.1.short_conv.bn.weight: copying a param with shape torch.Size([64]) from checkpoint, the shape in current model is torch.Size([32]).
size mismatch for stage2.1.short_conv.bn.bias: copying a param with shape torch.Size([64]) from checkpoint, the shape in current model is torch.Size([32]).
size mismatch for stage2.1.short_conv.bn.running_mean: copying a param with shape torch.Size([64]) from checkpoint, the shape in current model is torch.Size([32]).
size mismatch for stage2.1.short_conv.bn.running_var: copying a param with shape torch.Size([64]) from checkpoint, the shape in current model is torch.Size([32]).
size mismatch for stage2.1.final_conv.conv.weight: copying a param with shape torch.Size([128, 128, 1, 1]) from checkpoint, the shape in current model is torch.Size([64, 64, 1, 1]).
size mismatch for stage2.1.final_conv.bn.weight: copying a param with shape torch.Size([128]) from checkpoint, the shape in current model is torch.Size([64]).
size mismatch for stage2.1.final_conv.bn.bias: copying a param with shape torch.Size([128]) from checkpoint, the shape in current model is torch.Size([64]).
size mismatch for stage2.1.final_conv.bn.running_mean: copying a param with shape torch.Size([128]) from checkpoint, the shape in current model is torch.Size([64]).
size mismatch for stage2.1.final_conv.bn.running_var: copying a param with shape torch.Size([128]) from checkpoint, the shape in current model is torch.Size([64]).
size mismatch for stage2.1.blocks.0.conv1.conv.weight: copying a param with shape torch.Size([64, 64, 3, 3]) from checkpoint, the shape in current model is torch.Size([32, 32, 3, 3]).
size mismatch for stage2.1.blocks.0.conv1.bn.weight: copying a param with shape torch.Size([64]) from checkpoint, the shape in current model is torch.Size([32]).
size mismatch for stage2.1.blocks.0.conv1.bn.bias: copying a param with shape torch.Size([64]) from checkpoint, the shape in current model is torch.Size([32]).
size mismatch for stage2.1.blocks.0.conv1.bn.running_mean: copying a param with shape torch.Size([64]) from checkpoint, the shape in current model is torch.Size([32]).
size mismatch for stage2.1.blocks.0.conv1.bn.running_var: copying a param with shape torch.Size([64]) from checkpoint, the shape in current model is torch.Size([32]).
size mismatch for stage2.1.blocks.0.conv2.depthwise_conv.conv.weight: copying a param with shape torch.Size([64, 1, 5, 5]) from checkpoint, the shape in current model is torch.Size([32, 1, 5, 5]).
size mismatch for stage2.1.blocks.0.conv2.depthwise_conv.bn.weight: copying a param with shape torch.Size([64]) from checkpoint, the shape in current model is torch.Size([32]).
size mismatch for stage2.1.blocks.0.conv2.depthwise_conv.bn.bias: copying a param with shape torch.Size([64]) from checkpoint, the shape in current model is torch.Size([32]).
size mismatch for stage2.1.blocks.0.conv2.depthwise_conv.bn.running_mean: copying a param with shape torch.Size([64]) from checkpoint, the shape in current model is torch.Size([32]).
size mismatch for stage2.1.blocks.0.conv2.depthwise_conv.bn.running_var: copying a param with shape torch.Size([64]) from checkpoint, the shape in current model is torch.Size([32]).
size mismatch for stage2.1.blocks.0.conv2.pointwise_conv.conv.weight: copying a param with shape torch.Size([64, 64, 1, 1]) from checkpoint, the shape in current model is torch.Size([32, 32, 1, 1]).
size mismatch for stage2.1.blocks.0.conv2.pointwise_conv.bn.weight: copying a param with shape torch.Size([64]) from checkpoint, the shape in current model is torch.Size([32]).
size mismatch for stage2.1.blocks.0.conv2.pointwise_conv.bn.bias: copying a param with shape torch.Size([64]) from checkpoint, the shape in current model is torch.Size([32]).
size mismatch for stage2.1.blocks.0.conv2.pointwise_conv.bn.running_mean: copying a param with shape torch.Size([64]) from checkpoint, the shape in current model is torch.Size([32]).
size mismatch for stage2.1.blocks.0.conv2.pointwise_conv.bn.running_var: copying a param with shape torch.Size([64]) from checkpoint, the shape in current model is torch.Size([32]).
size mismatch for stage2.1.blocks.1.conv1.conv.weight: copying a param with shape torch.Size([64, 64, 3, 3]) from checkpoint, the shape in current model is torch.Size([32, 32, 3, 3]).
size mismatch for stage2.1.blocks.1.conv1.bn.weight: copying a param with shape torch.Size([64]) from checkpoint, the shape in current model is torch.Size([32]).
size mismatch for stage2.1.blocks.1.conv1.bn.bias: copying a param with shape torch.Size([64]) from checkpoint, the shape in current model is torch.Size([32]).
size mismatch for stage2.1.blocks.1.conv1.bn.running_mean: copying a param with shape torch.Size([64]) from checkpoint, the shape in current model is torch.Size([32]).
size mismatch for stage2.1.blocks.1.conv1.bn.running_var: copying a param with shape torch.Size([64]) from checkpoint, the shape in current model is torch.Size([32]).
size mismatch for stage2.1.blocks.1.conv2.depthwise_conv.conv.weight: copying a param with shape torch.Size([64, 1, 5, 5]) from checkpoint, the shape in current model is torch.Size([32, 1, 5, 5]).
size mismatch for stage2.1.blocks.1.conv2.depthwise_conv.bn.weight: copying a param with shape torch.Size([64]) from checkpoint, the shape in current model is torch.Size([32]).
size mismatch for stage2.1.blocks.1.conv2.depthwise_conv.bn.bias: copying a param with shape torch.Size([64]) from checkpoint, the shape in current model is torch.Size([32]).
size mismatch for stage2.1.blocks.1.conv2.depthwise_conv.bn.running_mean: copying a param with shape torch.Size([64]) from checkpoint, the shape in current model is torch.Size([32]).
size mismatch for stage2.1.blocks.1.conv2.depthwise_conv.bn.running_var: copying a param with shape torch.Size([64]) from checkpoint, the shape in current model is torch.Size([32]).
size mismatch for stage2.1.blocks.1.conv2.pointwise_conv.conv.weight: copying a param with shape torch.Size([64, 64, 1, 1]) from checkpoint, the shape in current model is torch.Size([32, 32, 1, 1]).
size mismatch for stage2.1.blocks.1.conv2.pointwise_conv.bn.weight: copying a param with shape torch.Size([64]) from checkpoint, the shape in current model is torch.Size([32]).
size mismatch for stage2.1.blocks.1.conv2.pointwise_conv.bn.bias: copying a param with shape torch.Size([64]) from checkpoint, the shape in current model is torch.Size([32]).
size mismatch for stage2.1.blocks.1.conv2.pointwise_conv.bn.running_mean: copying a param with shape torch.Size([64]) from checkpoint, the shape in current model is torch.Size([32]).
size mismatch for stage2.1.blocks.1.conv2.pointwise_conv.bn.running_var: copying a param with shape torch.Size([64]) from checkpoint, the shape in current model is torch.Size([32]).
size mismatch for stage3.0.conv.weight: copying a param with shape torch.Size([256, 128, 3, 3]) from checkpoint, the shape in current model is torch.Size([128, 64, 3, 3]).
size mismatch for stage3.0.bn.weight: copying a param with shape torch.Size([256]) from checkpoint, the shape in current model is torch.Size([128]).
size mismatch for stage3.0.bn.bias: copying a param with shape torch.Size([256]) from checkpoint, the shape in current model is torch.Size([128]).
size mismatch for stage3.0.bn.running_mean: copying a param with shape torch.Size([256]) from checkpoint, the shape in current model is torch.Size([128]).
size mismatch for stage3.0.bn.running_var: copying a param with shape torch.Size([256]) from checkpoint, the shape in current model is torch.Size([128]).
size mismatch for stage3.1.main_conv.conv.weight: copying a param with shape torch.Size([128, 256, 1, 1]) from checkpoint, the shape in current model is torch.Size([64, 128, 1, 1]).
size mismatch for stage3.1.main_conv.bn.weight: copying a param with shape torch.Size([128]) from checkpoint, the shape in current model is torch.Size([64]).
size mismatch for stage3.1.main_conv.bn.bias: copying a param with shape torch.Size([128]) from checkpoint, the shape in current model is torch.Size([64]).
size mismatch for stage3.1.main_conv.bn.running_mean: copying a param with shape torch.Size([128]) from checkpoint, the shape in current model is torch.Size([64]).
size mismatch for stage3.1.main_conv.bn.running_var: copying a param with shape torch.Size([128]) from checkpoint, the shape in current model is torch.Size([64]).
size mismatch for stage3.1.short_conv.conv.weight: copying a param with shape torch.Size([128, 256, 1, 1]) from checkpoint, the shape in current model is torch.Size([64, 128, 1, 1]).
size mismatch for stage3.1.short_conv.bn.weight: copying a param with shape torch.Size([128]) from checkpoint, the shape in current model is torch.Size([64]).
size mismatch for stage3.1.short_conv.bn.bias: copying a param with shape torch.Size([128]) from checkpoint, the shape in current model is torch.Size([64]).
size mismatch for stage3.1.short_conv.bn.running_mean: copying a param with shape torch.Size([128]) from checkpoint, the shape in current model is torch.Size([64]).
size mismatch for stage3.1.short_conv.bn.running_var: copying a param with shape torch.Size([128]) from checkpoint, the shape in current model is torch.Size([64]).
size mismatch for stage3.1.final_conv.conv.weight: copying a param with shape torch.Size([256, 256, 1, 1]) from checkpoint, the shape in current model is torch.Size([128, 128, 1, 1]).
size mismatch for stage3.1.final_conv.bn.weight: copying a param with shape torch.Size([256]) from checkpoint, the shape in current model is torch.Size([128]).
size mismatch for stage3.1.final_conv.bn.bias: copying a param with shape torch.Size([256]) from checkpoint, the shape in current model is torch.Size([128]).
size mismatch for stage3.1.final_conv.bn.running_mean: copying a param with shape torch.Size([256]) from checkpoint, the shape in current model is torch.Size([128]).
size mismatch for stage3.1.final_conv.bn.running_var: copying a param with shape torch.Size([256]) from checkpoint, the shape in current model is torch.Size([128]).
size mismatch for stage3.1.blocks.0.conv1.conv.weight: copying a param with shape torch.Size([128, 128, 3, 3]) from checkpoint, the shape in current model is torch.Size([64, 64, 3, 3]).
size mismatch for stage3.1.blocks.0.conv1.bn.weight: copying a param with shape torch.Size([128]) from checkpoint, the shape in current model is torch.Size([64]).
size mismatch for stage3.1.blocks.0.conv1.bn.bias: copying a param with shape torch.Size([128]) from checkpoint, the shape in current model is torch.Size([64]).
size mismatch for stage3.1.blocks.0.conv1.bn.running_mean: copying a param with shape torch.Size([128]) from checkpoint, the shape in current model is torch.Size([64]).
size mismatch for stage3.1.blocks.0.conv1.bn.running_var: copying a param with shape torch.Size([128]) from checkpoint, the shape in current model is torch.Size([64]).
size mismatch for stage3.1.blocks.0.conv2.depthwise_conv.conv.weight: copying a param with shape torch.Size([128, 1, 5, 5]) from checkpoint, the shape in current model is torch.Size([64, 1, 5, 5]).
size mismatch for stage3.1.blocks.0.conv2.depthwise_conv.bn.weight: copying a param with shape torch.Size([128]) from checkpoint, the shape in current model is torch.Size([64]).
size mismatch for stage3.1.blocks.0.conv2.depthwise_conv.bn.bias: copying a param with shape torch.Size([128]) from checkpoint, the shape in current model is torch.Size([64]).
size mismatch for stage3.1.blocks.0.conv2.depthwise_conv.bn.running_mean: copying a param with shape torch.Size([128]) from checkpoint, the shape in current model is torch.Size([64]).
size mismatch for stage3.1.blocks.0.conv2.depthwise_conv.bn.running_var: copying a param with shape torch.Size([128]) from checkpoint, the shape in current model is torch.Size([64]).
size mismatch for stage3.1.blocks.0.conv2.pointwise_conv.conv.weight: copying a param with shape torch.Size([128, 128, 1, 1]) from checkpoint, the shape in current model is torch.Size([64, 64, 1, 1]).
size mismatch for stage3.1.blocks.0.conv2.pointwise_conv.bn.weight: copying a param with shape torch.Size([128]) from checkpoint, the shape in current model is torch.Size([64]).
size mismatch for stage3.1.blocks.0.conv2.pointwise_conv.bn.bias: copying a param with shape torch.Size([128]) from checkpoint, the shape in current model is torch.Size([64]).
size mismatch for stage3.1.blocks.0.conv2.pointwise_conv.bn.running_mean: copying a param with shape torch.Size([128]) from checkpoint, the shape in current model is torch.Size([64]).
size mismatch for stage3.1.blocks.0.conv2.pointwise_conv.bn.running_var: copying a param with shape torch.Size([128]) from checkpoint, the shape in current model is torch.Size([64]).
size mismatch for stage3.1.blocks.1.conv1.conv.weight: copying a param with shape torch.Size([128, 128, 3, 3]) from checkpoint, the shape in current model is torch.Size([64, 64, 3, 3]).
size mismatch for stage3.1.blocks.1.conv1.bn.weight: copying a param with shape torch.Size([128]) from checkpoint, the shape in current model is torch.Size([64]).
size mismatch for stage3.1.blocks.1.conv1.bn.bias: copying a param with shape torch.Size([128]) from checkpoint, the shape in current model is torch.Size([64]).
size mismatch for stage3.1.blocks.1.conv1.bn.running_mean: copying a param with shape torch.Size([128]) from checkpoint, the shape in current model is torch.Size([64]).
size mismatch for stage3.1.blocks.1.conv1.bn.running_var: copying a param with shape torch.Size([128]) from checkpoint, the shape in current model is torch.Size([64]).
size mismatch for stage3.1.blocks.1.conv2.depthwise_conv.conv.weight: copying a param with shape torch.Size([128, 1, 5, 5]) from checkpoint, the shape in current model is torch.Size([64, 1, 5, 5]).
size mismatch for stage3.1.blocks.1.conv2.depthwise_conv.bn.weight: copying a param with shape torch.Size([128]) from checkpoint, the shape in current model is torch.Size([64]).
size mismatch for stage3.1.blocks.1.conv2.depthwise_conv.bn.bias: copying a param with shape torch.Size([128]) from checkpoint, the shape in current model is torch.Size([64]).
size mismatch for stage3.1.blocks.1.conv2.depthwise_conv.bn.running_mean: copying a param with shape torch.Size([128]) from checkpoint, the shape in current model is torch.Size([64]).
size mismatch for stage3.1.blocks.1.conv2.depthwise_conv.bn.running_var: copying a param with shape torch.Size([128]) from checkpoint, the shape in current model is torch.Size([64]).
size mismatch for stage3.1.blocks.1.conv2.pointwise_conv.conv.weight: copying a param with shape torch.Size([128, 128, 1, 1]) from checkpoint, the shape in current model is torch.Size([64, 64, 1, 1]).
size mismatch for stage3.1.blocks.1.conv2.pointwise_conv.bn.weight: copying a param with shape torch.Size([128]) from checkpoint, the shape in current model is torch.Size([64]).
size mismatch for stage3.1.blocks.1.conv2.pointwise_conv.bn.bias: copying a param with shape torch.Size([128]) from checkpoint, the shape in current model is torch.Size([64]).
size mismatch for stage3.1.blocks.1.conv2.pointwise_conv.bn.running_mean: copying a param with shape torch.Size([128]) from checkpoint, the shape in current model is torch.Size([64]).
size mismatch for stage3.1.blocks.1.conv2.pointwise_conv.bn.running_var: copying a param with shape torch.Size([128]) from checkpoint, the shape in current model is torch.Size([64]).
size mismatch for stage4.0.conv.weight: copying a param with shape torch.Size([512, 256, 3, 3]) from checkpoint, the shape in current model is torch.Size([256, 128, 3, 3]).
size mismatch for stage4.0.bn.weight: copying a param with shape torch.Size([512]) from checkpoint, the shape in current model is torch.Size([256]).
size mismatch for stage4.0.bn.bias: copying a param with shape torch.Size([512]) from checkpoint, the shape in current model is torch.Size([256]).
size mismatch for stage4.0.bn.running_mean: copying a param with shape torch.Size([512]) from checkpoint, the shape in current model is torch.Size([256]).
size mismatch for stage4.0.bn.running_var: copying a param with shape torch.Size([512]) from checkpoint, the shape in current model is torch.Size([256]).
size mismatch for stage4.1.conv1.conv.weight: copying a param with shape torch.Size([256, 512, 1, 1]) from checkpoint, the shape in current model is torch.Size([128, 256, 1, 1]).
size mismatch for stage4.1.conv1.bn.weight: copying a param with shape torch.Size([256]) from checkpoint, the shape in current model is torch.Size([128]).
size mismatch for stage4.1.conv1.bn.bias: copying a param with shape torch.Size([256]) from checkpoint, the shape in current model is torch.Size([128]).
size mismatch for stage4.1.conv1.bn.running_mean: copying a param with shape torch.Size([256]) from checkpoint, the shape in current model is torch.Size([128]).
size mismatch for stage4.1.conv1.bn.running_var: copying a param with shape torch.Size([256]) from checkpoint, the shape in current model is torch.Size([128]).
size mismatch for stage4.1.conv2.conv.weight: copying a param with shape torch.Size([512, 1024, 1, 1]) from checkpoint, the shape in current model is torch.Size([256, 512, 1, 1]).
size mismatch for stage4.1.conv2.bn.weight: copying a param with shape torch.Size([512]) from checkpoint, the shape in current model is torch.Size([256]).
size mismatch for stage4.1.conv2.bn.bias: copying a param with shape torch.Size([512]) from checkpoint, the shape in current model is torch.Size([256]).
size mismatch for stage4.1.conv2.bn.running_mean: copying a param with shape torch.Size([512]) from checkpoint, the shape in current model is torch.Size([256]).
size mismatch for stage4.1.conv2.bn.running_var: copying a param with shape torch.Size([512]) from checkpoint, the shape in current model is torch.Size([256]).
size mismatch for stage4.2.main_conv.conv.weight: copying a param with shape torch.Size([256, 512, 1, 1]) from checkpoint, the shape in current model is torch.Size([128, 256, 1, 1]).
size mismatch for stage4.2.main_conv.bn.weight: copying a param with shape torch.Size([256]) from checkpoint, the shape in current model is torch.Size([128]).
size mismatch for stage4.2.main_conv.bn.bias: copying a param with shape torch.Size([256]) from checkpoint, the shape in current model is torch.Size([128]).
size mismatch for stage4.2.main_conv.bn.running_mean: copying a param with shape torch.Size([256]) from checkpoint, the shape in current model is torch.Size([128]).
size mismatch for stage4.2.main_conv.bn.running_var: copying a param with shape torch.Size([256]) from checkpoint, the shape in current model is torch.Size([128]).
size mismatch for stage4.2.short_conv.conv.weight: copying a param with shape torch.Size([256, 512, 1, 1]) from checkpoint, the shape in current model is torch.Size([128, 256, 1, 1]).
size mismatch for stage4.2.short_conv.bn.weight: copying a param with shape torch.Size([256]) from checkpoint, the shape in current model is torch.Size([128]).
size mismatch for stage4.2.short_conv.bn.bias: copying a param with shape torch.Size([256]) from checkpoint, the shape in current model is torch.Size([128]).
size mismatch for stage4.2.short_conv.bn.running_mean: copying a param with shape torch.Size([256]) from checkpoint, the shape in current model is torch.Size([128]).
size mismatch for stage4.2.short_conv.bn.running_var: copying a param with shape torch.Size([256]) from checkpoint, the shape in current model is torch.Size([128]).
size mismatch for stage4.2.final_conv.conv.weight: copying a param with shape torch.Size([512, 512, 1, 1]) from checkpoint, the shape in current model is torch.Size([256, 256, 1, 1]).
size mismatch for stage4.2.final_conv.bn.weight: copying a param with shape torch.Size([512]) from checkpoint, the shape in current model is torch.Size([256]).
size mismatch for stage4.2.final_conv.bn.bias: copying a param with shape torch.Size([512]) from checkpoint, the shape in current model is torch.Size([256]).
size mismatch for stage4.2.final_conv.bn.running_mean: copying a param with shape torch.Size([512]) from checkpoint, the shape in current model is torch.Size([256]).
size mismatch for stage4.2.final_conv.bn.running_var: copying a param with shape torch.Size([512]) from checkpoint, the shape in current model is torch.Size([256]).
size mismatch for stage4.2.blocks.0.conv1.conv.weight: copying a param with shape torch.Size([256, 256, 3, 3]) from checkpoint, the shape in current model is torch.Size([128, 128, 3, 3]).
size mismatch for stage4.2.blocks.0.conv1.bn.weight: copying a param with shape torch.Size([256]) from checkpoint, the shape in current model is torch.Size([128]).
size mismatch for stage4.2.blocks.0.conv1.bn.bias: copying a param with shape torch.Size([256]) from checkpoint, the shape in current model is torch.Size([128]).
size mismatch for stage4.2.blocks.0.conv1.bn.running_mean: copying a param with shape torch.Size([256]) from checkpoint, the shape in current model is torch.Size([128]).
size mismatch for stage4.2.blocks.0.conv1.bn.running_var: copying a param with shape torch.Size([256]) from checkpoint, the shape in current model is torch.Size([128]).
size mismatch for stage4.2.blocks.0.conv2.depthwise_conv.conv.weight: copying a param with shape torch.Size([256, 1, 5, 5]) from checkpoint, the shape in current model is torch.Size([128, 1, 5, 5]).
size mismatch for stage4.2.blocks.0.conv2.depthwise_conv.bn.weight: copying a param with shape torch.Size([256]) from checkpoint, the shape in current model is torch.Size([128]).
size mismatch for stage4.2.blocks.0.conv2.depthwise_conv.bn.bias: copying a param with shape torch.Size([256]) from checkpoint, the shape in current model is torch.Size([128]).
size mismatch for stage4.2.blocks.0.conv2.depthwise_conv.bn.running_mean: copying a param with shape torch.Size([256]) from checkpoint, the shape in current model is torch.Size([128]).
size mismatch for stage4.2.blocks.0.conv2.depthwise_conv.bn.running_var: copying a param with shape torch.Size([256]) from checkpoint, the shape in current model is torch.Size([128]).
size mismatch for stage4.2.blocks.0.conv2.pointwise_conv.conv.weight: copying a param with shape torch.Size([256, 256, 1, 1]) from checkpoint, the shape in current model is torch.Size([128, 128, 1, 1]).
size mismatch for stage4.2.blocks.0.conv2.pointwise_conv.bn.weight: copying a param with shape torch.Size([256]) from checkpoint, the shape in current model is torch.Size([128]).
size mismatch for stage4.2.blocks.0.conv2.pointwise_conv.bn.bias: copying a param with shape torch.Size([256]) from checkpoint, the shape in current model is torch.Size([128]).
size mismatch for stage4.2.blocks.0.conv2.pointwise_conv.bn.running_mean: copying a param with shape torch.Size([256]) from checkpoint, the shape in current model is torch.Size([128]).
size mismatch for stage4.2.blocks.0.conv2.pointwise_conv.bn.running_var: copying a param with shape torch.Size([256]) from checkpoint, the shape in current model is torch.Size([128]).
unexpected key in source state_dict: stage1.1.attention.fc.weight, stage1.1.attention.fc.bias, stage2.1.attention.fc.weight, stage2.1.attention.fc.bias, stage3.1.attention.fc.weight, stage3.1.attention.fc.bias, stage4.2.attention.fc.weight, stage4.2.attention.fc.bias

Name of parameter - Initialization information

backbone.stem.0.conv.weight - torch.Size([8, 3, 3, 3]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stem.0.bn.weight - torch.Size([8]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stem.0.bn.bias - torch.Size([8]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stem.1.conv.weight - torch.Size([8, 8, 3, 3]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stem.1.bn.weight - torch.Size([8]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stem.1.bn.bias - torch.Size([8]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stem.2.conv.weight - torch.Size([16, 8, 3, 3]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stem.2.bn.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stem.2.bn.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage1.0.conv.weight - torch.Size([32, 16, 3, 3]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage1.0.bn.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage1.0.bn.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage1.1.main_conv.conv.weight - torch.Size([16, 32, 1, 1]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage1.1.main_conv.bn.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage1.1.main_conv.bn.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage1.1.short_conv.conv.weight - torch.Size([16, 32, 1, 1]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage1.1.short_conv.bn.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage1.1.short_conv.bn.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage1.1.final_conv.conv.weight - torch.Size([32, 32, 1, 1]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage1.1.final_conv.bn.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage1.1.final_conv.bn.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage1.1.blocks.0.conv1.conv.weight - torch.Size([16, 16, 3, 3]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage1.1.blocks.0.conv1.bn.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage1.1.blocks.0.conv1.bn.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage1.1.blocks.0.conv2.depthwise_conv.conv.weight - torch.Size([16, 1, 5, 5]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage1.1.blocks.0.conv2.depthwise_conv.bn.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage1.1.blocks.0.conv2.depthwise_conv.bn.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage1.1.blocks.0.conv2.pointwise_conv.conv.weight - torch.Size([16, 16, 1, 1]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage1.1.blocks.0.conv2.pointwise_conv.bn.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage1.1.blocks.0.conv2.pointwise_conv.bn.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage2.0.conv.weight - torch.Size([64, 32, 3, 3]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage2.0.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage2.0.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage2.1.main_conv.conv.weight - torch.Size([32, 64, 1, 1]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage2.1.main_conv.bn.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage2.1.main_conv.bn.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage2.1.short_conv.conv.weight - torch.Size([32, 64, 1, 1]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage2.1.short_conv.bn.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage2.1.short_conv.bn.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage2.1.final_conv.conv.weight - torch.Size([64, 64, 1, 1]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage2.1.final_conv.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage2.1.final_conv.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage2.1.blocks.0.conv1.conv.weight - torch.Size([32, 32, 3, 3]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage2.1.blocks.0.conv1.bn.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage2.1.blocks.0.conv1.bn.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage2.1.blocks.0.conv2.depthwise_conv.conv.weight - torch.Size([32, 1, 5, 5]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage2.1.blocks.0.conv2.depthwise_conv.bn.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage2.1.blocks.0.conv2.depthwise_conv.bn.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage2.1.blocks.0.conv2.pointwise_conv.conv.weight - torch.Size([32, 32, 1, 1]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage2.1.blocks.0.conv2.pointwise_conv.bn.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage2.1.blocks.0.conv2.pointwise_conv.bn.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage2.1.blocks.1.conv1.conv.weight - torch.Size([32, 32, 3, 3]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage2.1.blocks.1.conv1.bn.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage2.1.blocks.1.conv1.bn.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage2.1.blocks.1.conv2.depthwise_conv.conv.weight - torch.Size([32, 1, 5, 5]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage2.1.blocks.1.conv2.depthwise_conv.bn.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage2.1.blocks.1.conv2.depthwise_conv.bn.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage2.1.blocks.1.conv2.pointwise_conv.conv.weight - torch.Size([32, 32, 1, 1]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage2.1.blocks.1.conv2.pointwise_conv.bn.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage2.1.blocks.1.conv2.pointwise_conv.bn.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage3.0.conv.weight - torch.Size([128, 64, 3, 3]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage3.0.bn.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage3.0.bn.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage3.1.main_conv.conv.weight - torch.Size([64, 128, 1, 1]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage3.1.main_conv.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage3.1.main_conv.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage3.1.short_conv.conv.weight - torch.Size([64, 128, 1, 1]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage3.1.short_conv.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage3.1.short_conv.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage3.1.final_conv.conv.weight - torch.Size([128, 128, 1, 1]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage3.1.final_conv.bn.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage3.1.final_conv.bn.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage3.1.blocks.0.conv1.conv.weight - torch.Size([64, 64, 3, 3]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage3.1.blocks.0.conv1.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage3.1.blocks.0.conv1.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage3.1.blocks.0.conv2.depthwise_conv.conv.weight - torch.Size([64, 1, 5, 5]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage3.1.blocks.0.conv2.depthwise_conv.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage3.1.blocks.0.conv2.depthwise_conv.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage3.1.blocks.0.conv2.pointwise_conv.conv.weight - torch.Size([64, 64, 1, 1]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage3.1.blocks.0.conv2.pointwise_conv.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage3.1.blocks.0.conv2.pointwise_conv.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage3.1.blocks.1.conv1.conv.weight - torch.Size([64, 64, 3, 3]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage3.1.blocks.1.conv1.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage3.1.blocks.1.conv1.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage3.1.blocks.1.conv2.depthwise_conv.conv.weight - torch.Size([64, 1, 5, 5]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage3.1.blocks.1.conv2.depthwise_conv.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage3.1.blocks.1.conv2.depthwise_conv.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage3.1.blocks.1.conv2.pointwise_conv.conv.weight - torch.Size([64, 64, 1, 1]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage3.1.blocks.1.conv2.pointwise_conv.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage3.1.blocks.1.conv2.pointwise_conv.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage4.0.conv.weight - torch.Size([256, 128, 3, 3]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage4.0.bn.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage4.0.bn.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage4.1.conv1.conv.weight - torch.Size([128, 256, 1, 1]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage4.1.conv1.bn.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage4.1.conv1.bn.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage4.1.conv2.conv.weight - torch.Size([256, 512, 1, 1]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage4.1.conv2.bn.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage4.1.conv2.bn.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage4.2.main_conv.conv.weight - torch.Size([128, 256, 1, 1]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage4.2.main_conv.bn.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage4.2.main_conv.bn.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage4.2.short_conv.conv.weight - torch.Size([128, 256, 1, 1]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage4.2.short_conv.bn.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage4.2.short_conv.bn.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage4.2.final_conv.conv.weight - torch.Size([256, 256, 1, 1]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage4.2.final_conv.bn.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage4.2.final_conv.bn.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage4.2.blocks.0.conv1.conv.weight - torch.Size([128, 128, 3, 3]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage4.2.blocks.0.conv1.bn.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage4.2.blocks.0.conv1.bn.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage4.2.blocks.0.conv2.depthwise_conv.conv.weight - torch.Size([128, 1, 5, 5]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage4.2.blocks.0.conv2.depthwise_conv.bn.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage4.2.blocks.0.conv2.depthwise_conv.bn.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage4.2.blocks.0.conv2.pointwise_conv.conv.weight - torch.Size([128, 128, 1, 1]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage4.2.blocks.0.conv2.pointwise_conv.bn.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage4.2.blocks.0.conv2.pointwise_conv.bn.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.reduce_layers.2.conv.weight - torch.Size([128, 256, 1, 1]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.reduce_layers.2.bn.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.reduce_layers.2.bn.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.top_down_layers.0.0.main_conv.conv.weight - torch.Size([64, 256, 1, 1]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.top_down_layers.0.0.main_conv.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.top_down_layers.0.0.main_conv.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.top_down_layers.0.0.short_conv.conv.weight - torch.Size([64, 256, 1, 1]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.top_down_layers.0.0.short_conv.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.top_down_layers.0.0.short_conv.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.top_down_layers.0.0.final_conv.conv.weight - torch.Size([128, 128, 1, 1]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.top_down_layers.0.0.final_conv.bn.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.top_down_layers.0.0.final_conv.bn.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.top_down_layers.0.0.blocks.0.conv1.conv.weight - torch.Size([64, 64, 3, 3]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.top_down_layers.0.0.blocks.0.conv1.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.top_down_layers.0.0.blocks.0.conv1.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.top_down_layers.0.0.blocks.0.conv2.depthwise_conv.conv.weight - torch.Size([64, 1, 5, 5]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.top_down_layers.0.0.blocks.0.conv2.depthwise_conv.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.top_down_layers.0.0.blocks.0.conv2.depthwise_conv.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.top_down_layers.0.0.blocks.0.conv2.pointwise_conv.conv.weight - torch.Size([64, 64, 1, 1]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.top_down_layers.0.0.blocks.0.conv2.pointwise_conv.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.top_down_layers.0.0.blocks.0.conv2.pointwise_conv.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.top_down_layers.0.1.conv.weight - torch.Size([64, 128, 1, 1]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.top_down_layers.0.1.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.top_down_layers.0.1.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.top_down_layers.1.main_conv.conv.weight - torch.Size([32, 128, 1, 1]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.top_down_layers.1.main_conv.bn.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.top_down_layers.1.main_conv.bn.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.top_down_layers.1.short_conv.conv.weight - torch.Size([32, 128, 1, 1]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.top_down_layers.1.short_conv.bn.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.top_down_layers.1.short_conv.bn.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.top_down_layers.1.final_conv.conv.weight - torch.Size([64, 64, 1, 1]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.top_down_layers.1.final_conv.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.top_down_layers.1.final_conv.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.top_down_layers.1.blocks.0.conv1.conv.weight - torch.Size([32, 32, 3, 3]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.top_down_layers.1.blocks.0.conv1.bn.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.top_down_layers.1.blocks.0.conv1.bn.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.top_down_layers.1.blocks.0.conv2.depthwise_conv.conv.weight - torch.Size([32, 1, 5, 5]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.top_down_layers.1.blocks.0.conv2.depthwise_conv.bn.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.top_down_layers.1.blocks.0.conv2.depthwise_conv.bn.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.top_down_layers.1.blocks.0.conv2.pointwise_conv.conv.weight - torch.Size([32, 32, 1, 1]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.top_down_layers.1.blocks.0.conv2.pointwise_conv.bn.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.top_down_layers.1.blocks.0.conv2.pointwise_conv.bn.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.downsample_layers.0.conv.weight - torch.Size([64, 64, 3, 3]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.downsample_layers.0.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.downsample_layers.0.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.downsample_layers.1.conv.weight - torch.Size([128, 128, 3, 3]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.downsample_layers.1.bn.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.downsample_layers.1.bn.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.bottom_up_layers.0.main_conv.conv.weight - torch.Size([64, 128, 1, 1]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.bottom_up_layers.0.main_conv.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.bottom_up_layers.0.main_conv.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.bottom_up_layers.0.short_conv.conv.weight - torch.Size([64, 128, 1, 1]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.bottom_up_layers.0.short_conv.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.bottom_up_layers.0.short_conv.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.bottom_up_layers.0.final_conv.conv.weight - torch.Size([128, 128, 1, 1]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.bottom_up_layers.0.final_conv.bn.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.bottom_up_layers.0.final_conv.bn.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.bottom_up_layers.0.blocks.0.conv1.conv.weight - torch.Size([64, 64, 3, 3]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.bottom_up_layers.0.blocks.0.conv1.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.bottom_up_layers.0.blocks.0.conv1.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.bottom_up_layers.0.blocks.0.conv2.depthwise_conv.conv.weight - torch.Size([64, 1, 5, 5]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.bottom_up_layers.0.blocks.0.conv2.depthwise_conv.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.bottom_up_layers.0.blocks.0.conv2.depthwise_conv.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.bottom_up_layers.0.blocks.0.conv2.pointwise_conv.conv.weight - torch.Size([64, 64, 1, 1]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.bottom_up_layers.0.blocks.0.conv2.pointwise_conv.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.bottom_up_layers.0.blocks.0.conv2.pointwise_conv.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.bottom_up_layers.1.main_conv.conv.weight - torch.Size([128, 256, 1, 1]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.bottom_up_layers.1.main_conv.bn.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.bottom_up_layers.1.main_conv.bn.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.bottom_up_layers.1.short_conv.conv.weight - torch.Size([128, 256, 1, 1]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.bottom_up_layers.1.short_conv.bn.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.bottom_up_layers.1.short_conv.bn.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.bottom_up_layers.1.final_conv.conv.weight - torch.Size([256, 256, 1, 1]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.bottom_up_layers.1.final_conv.bn.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.bottom_up_layers.1.final_conv.bn.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.bottom_up_layers.1.blocks.0.conv1.conv.weight - torch.Size([128, 128, 3, 3]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.bottom_up_layers.1.blocks.0.conv1.bn.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.bottom_up_layers.1.blocks.0.conv1.bn.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.bottom_up_layers.1.blocks.0.conv2.depthwise_conv.conv.weight - torch.Size([128, 1, 5, 5]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.bottom_up_layers.1.blocks.0.conv2.depthwise_conv.bn.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.bottom_up_layers.1.blocks.0.conv2.depthwise_conv.bn.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.bottom_up_layers.1.blocks.0.conv2.pointwise_conv.conv.weight - torch.Size([128, 128, 1, 1]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.bottom_up_layers.1.blocks.0.conv2.pointwise_conv.bn.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.bottom_up_layers.1.blocks.0.conv2.pointwise_conv.bn.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.out_layers.0.conv.weight - torch.Size([64, 64, 3, 3]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.out_layers.0.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.out_layers.0.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.out_layers.1.conv.weight - torch.Size([64, 128, 3, 3]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.out_layers.1.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.out_layers.1.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.out_layers.2.conv.weight - torch.Size([64, 256, 3, 3]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.out_layers.2.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.out_layers.2.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

bbox_head.head_module.cls_convs.0.0.conv.weight - torch.Size([64, 64, 3, 3]): 
Initialized by user-defined `init_weights` in RTMDetSepBNHeadModule  

bbox_head.head_module.cls_convs.0.0.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

bbox_head.head_module.cls_convs.0.0.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

bbox_head.head_module.cls_convs.0.1.conv.weight - torch.Size([64, 64, 3, 3]): 
Initialized by user-defined `init_weights` in RTMDetSepBNHeadModule  

bbox_head.head_module.cls_convs.0.1.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

bbox_head.head_module.cls_convs.0.1.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

bbox_head.head_module.cls_convs.1.0.conv.weight - torch.Size([64, 64, 3, 3]): 
Initialized by user-defined `init_weights` in RTMDetSepBNHeadModule  

bbox_head.head_module.cls_convs.1.0.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

bbox_head.head_module.cls_convs.1.0.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

bbox_head.head_module.cls_convs.1.1.conv.weight - torch.Size([64, 64, 3, 3]): 
Initialized by user-defined `init_weights` in RTMDetSepBNHeadModule  

bbox_head.head_module.cls_convs.1.1.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

bbox_head.head_module.cls_convs.1.1.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

bbox_head.head_module.cls_convs.2.0.conv.weight - torch.Size([64, 64, 3, 3]): 
Initialized by user-defined `init_weights` in RTMDetSepBNHeadModule  

bbox_head.head_module.cls_convs.2.0.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

bbox_head.head_module.cls_convs.2.0.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

bbox_head.head_module.cls_convs.2.1.conv.weight - torch.Size([64, 64, 3, 3]): 
Initialized by user-defined `init_weights` in RTMDetSepBNHeadModule  

bbox_head.head_module.cls_convs.2.1.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

bbox_head.head_module.cls_convs.2.1.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

bbox_head.head_module.reg_convs.0.0.conv.weight - torch.Size([64, 64, 3, 3]): 
Initialized by user-defined `init_weights` in RTMDetSepBNHeadModule  

bbox_head.head_module.reg_convs.0.0.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

bbox_head.head_module.reg_convs.0.0.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

bbox_head.head_module.reg_convs.0.1.conv.weight - torch.Size([64, 64, 3, 3]): 
Initialized by user-defined `init_weights` in RTMDetSepBNHeadModule  

bbox_head.head_module.reg_convs.0.1.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

bbox_head.head_module.reg_convs.0.1.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

bbox_head.head_module.reg_convs.1.0.conv.weight - torch.Size([64, 64, 3, 3]): 
Initialized by user-defined `init_weights` in RTMDetSepBNHeadModule  

bbox_head.head_module.reg_convs.1.0.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

bbox_head.head_module.reg_convs.1.0.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

bbox_head.head_module.reg_convs.1.1.conv.weight - torch.Size([64, 64, 3, 3]): 
Initialized by user-defined `init_weights` in RTMDetSepBNHeadModule  

bbox_head.head_module.reg_convs.1.1.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

bbox_head.head_module.reg_convs.1.1.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

bbox_head.head_module.reg_convs.2.0.conv.weight - torch.Size([64, 64, 3, 3]): 
Initialized by user-defined `init_weights` in RTMDetSepBNHeadModule  

bbox_head.head_module.reg_convs.2.0.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

bbox_head.head_module.reg_convs.2.0.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

bbox_head.head_module.reg_convs.2.1.conv.weight - torch.Size([64, 64, 3, 3]): 
Initialized by user-defined `init_weights` in RTMDetSepBNHeadModule  

bbox_head.head_module.reg_convs.2.1.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

bbox_head.head_module.reg_convs.2.1.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

bbox_head.head_module.rtm_cls.0.weight - torch.Size([2, 64, 1, 1]): 
Initialized by user-defined `init_weights` in RTMDetSepBNHeadModule  

bbox_head.head_module.rtm_cls.0.bias - torch.Size([2]): 
Initialized by user-defined `init_weights` in RTMDetSepBNHeadModule  

bbox_head.head_module.rtm_cls.1.weight - torch.Size([2, 64, 1, 1]): 
Initialized by user-defined `init_weights` in RTMDetSepBNHeadModule  

bbox_head.head_module.rtm_cls.1.bias - torch.Size([2]): 
Initialized by user-defined `init_weights` in RTMDetSepBNHeadModule  

bbox_head.head_module.rtm_cls.2.weight - torch.Size([2, 64, 1, 1]): 
Initialized by user-defined `init_weights` in RTMDetSepBNHeadModule  

bbox_head.head_module.rtm_cls.2.bias - torch.Size([2]): 
Initialized by user-defined `init_weights` in RTMDetSepBNHeadModule  

bbox_head.head_module.rtm_reg.0.weight - torch.Size([4, 64, 1, 1]): 
Initialized by user-defined `init_weights` in RTMDetSepBNHeadModule  

bbox_head.head_module.rtm_reg.0.bias - torch.Size([4]): 
Initialized by user-defined `init_weights` in RTMDetSepBNHeadModule  

bbox_head.head_module.rtm_reg.1.weight - torch.Size([4, 64, 1, 1]): 
Initialized by user-defined `init_weights` in RTMDetSepBNHeadModule  

bbox_head.head_module.rtm_reg.1.bias - torch.Size([4]): 
Initialized by user-defined `init_weights` in RTMDetSepBNHeadModule  

bbox_head.head_module.rtm_reg.2.weight - torch.Size([4, 64, 1, 1]): 
Initialized by user-defined `init_weights` in RTMDetSepBNHeadModule  

bbox_head.head_module.rtm_reg.2.bias - torch.Size([4]): 
Initialized by user-defined `init_weights` in RTMDetSepBNHeadModule  
2025/06/09 10:38:20 - mmengine - WARNING - "FileClient" will be deprecated in future. Please use io functions in https://mmengine.readthedocs.io/en/latest/api/fileio.html#file-io
2025/06/09 10:38:20 - mmengine - WARNING - "HardDiskBackend" is the alias of "LocalBackend" and the former will be deprecated in future.
2025/06/09 10:38:20 - mmengine - INFO - Checkpoints will be saved to D:\OBJECT_DETECTION\sscma\ModelAssistant\work_dirs\rtmdet_nano_motherboard_final.
2025/06/09 10:39:49 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:39:49 - mmengine - INFO - Epoch(train)   [1][31/31]  base_lr: 7.5087e-06 lr: 7.5087e-06  eta: 7:19:46  time: 2.8467  data_time: 2.4049  memory: 4298  loss: 0.5801  loss_cls: 0.5202  loss_bbox: 0.0599
2025/06/09 10:40:01 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:40:01 - mmengine - INFO - Epoch(train)   [2][31/31]  base_lr: 1.5262e-05 lr: 1.5262e-05  eta: 4:08:47  time: 0.3518  data_time: 0.0684  memory: 4379  loss: 0.5761  loss_cls: 0.5107  loss_bbox: 0.0655
2025/06/09 10:40:13 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:40:13 - mmengine - INFO - Epoch(train)   [3][31/31]  base_lr: 2.3016e-05 lr: 2.3016e-05  eta: 3:04:48  time: 0.3278  data_time: 0.0736  memory: 4379  loss: 0.5848  loss_cls: 0.4881  loss_bbox: 0.0967
2025/06/09 10:40:24 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:40:24 - mmengine - INFO - Epoch(train)   [4][31/31]  base_lr: 3.0770e-05 lr: 3.0770e-05  eta: 2:31:57  time: 0.3121  data_time: 0.0574  memory: 4379  loss: 0.9445  loss_cls: 0.4778  loss_bbox: 0.4667
2025/06/09 10:40:36 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:40:36 - mmengine - INFO - Epoch(train)   [5][31/31]  base_lr: 3.8524e-05 lr: 3.8524e-05  eta: 2:13:00  time: 0.3306  data_time: 0.0761  memory: 4603  loss: 1.6351  loss_cls: 0.4823  loss_bbox: 1.1528
2025/06/09 10:40:48 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:40:48 - mmengine - INFO - Epoch(train)   [6][31/31]  base_lr: 4.6278e-05 lr: 4.6278e-05  eta: 2:00:17  time: 0.3336  data_time: 0.0782  memory: 4490  loss: 1.9419  loss_cls: 0.4860  loss_bbox: 1.4559
2025/06/09 10:40:59 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:40:59 - mmengine - INFO - Epoch(train)   [7][31/31]  base_lr: 5.4031e-05 lr: 5.4031e-05  eta: 1:50:52  time: 0.3223  data_time: 0.0700  memory: 4736  loss: 2.0581  loss_cls: 0.5008  loss_bbox: 1.5572
2025/06/09 10:41:11 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:41:11 - mmengine - INFO - Epoch(train)   [8][31/31]  base_lr: 6.1785e-05 lr: 6.1785e-05  eta: 1:43:25  time: 0.3098  data_time: 0.0603  memory: 4335  loss: 2.0979  loss_cls: 0.5720  loss_bbox: 1.5259
2025/06/09 10:41:23 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:41:23 - mmengine - INFO - Epoch(train)   [9][31/31]  base_lr: 6.9539e-05 lr: 6.9539e-05  eta: 1:38:09  time: 0.3293  data_time: 0.0747  memory: 4445  loss: 2.0947  loss_cls: 0.6139  loss_bbox: 1.4808
2025/06/09 10:41:35 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:41:35 - mmengine - INFO - Epoch(train)  [10][31/31]  base_lr: 7.7293e-05 lr: 7.7293e-05  eta: 1:33:59  time: 0.3410  data_time: 0.0890  memory: 4379  loss: 2.0442  loss_cls: 0.5976  loss_bbox: 1.4466
2025/06/09 10:41:35 - mmengine - INFO - Saving checkpoint at 10 epochs
2025/06/09 10:42:19 - mmengine - INFO - Evaluating bbox...
2025/06/09 10:42:20 - mmengine - INFO - bbox_mAP_copypaste: 0.000 0.000 0.000 0.000 0.000 0.000
2025/06/09 10:42:20 - mmengine - INFO - Epoch(val) [10][20/20]    coco/bbox_mAP: 0.0000  coco/bbox_mAP_50: 0.0000  coco/bbox_mAP_75: 0.0000  coco/bbox_mAP_s: 0.0000  coco/bbox_mAP_m: 0.0000  coco/bbox_mAP_l: 0.0000  data_time: 1.8937  time: 2.1066
2025/06/09 10:42:20 - mmengine - INFO - The best checkpoint with 0.0000 coco/bbox_mAP at 10 epoch is saved to best_coco_bbox_mAP_epoch_10.pth.
2025/06/09 10:42:33 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:42:33 - mmengine - INFO - Epoch(train)  [11][31/31]  base_lr: 8.5047e-05 lr: 8.5047e-05  eta: 1:30:36  time: 0.3384  data_time: 0.0830  memory: 4427  loss: 2.0063  loss_cls: 0.6090  loss_bbox: 1.3973
2025/06/09 10:42:45 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:42:45 - mmengine - INFO - Epoch(train)  [12][31/31]  base_lr: 9.2800e-05 lr: 9.2800e-05  eta: 1:27:34  time: 0.3312  data_time: 0.0678  memory: 5008  loss: 1.9817  loss_cls: 0.5903  loss_bbox: 1.3914
2025/06/09 10:42:57 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:42:57 - mmengine - INFO - Epoch(train)  [13][31/31]  base_lr: 1.0055e-04 lr: 1.0055e-04  eta: 1:24:57  time: 0.3320  data_time: 0.0711  memory: 4249  loss: 1.9793  loss_cls: 0.5787  loss_bbox: 1.4007
2025/06/09 10:43:09 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:43:09 - mmengine - INFO - Epoch(train)  [14][31/31]  base_lr: 1.0831e-04 lr: 1.0831e-04  eta: 1:22:45  time: 0.3361  data_time: 0.0755  memory: 4383  loss: 1.9537  loss_cls: 0.6011  loss_bbox: 1.3526
2025/06/09 10:43:21 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:43:21 - mmengine - INFO - Epoch(train)  [15][31/31]  base_lr: 1.1606e-04 lr: 1.1606e-04  eta: 1:20:39  time: 0.3241  data_time: 0.0602  memory: 4249  loss: 1.9683  loss_cls: 0.6216  loss_bbox: 1.3467
2025/06/09 10:43:33 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:43:33 - mmengine - INFO - Epoch(train)  [16][31/31]  base_lr: 1.2382e-04 lr: 1.2382e-04  eta: 1:18:50  time: 0.3274  data_time: 0.0684  memory: 4271  loss: 1.9544  loss_cls: 0.6072  loss_bbox: 1.3473
2025/06/09 10:43:44 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:43:44 - mmengine - INFO - Epoch(train)  [17][31/31]  base_lr: 1.3157e-04 lr: 1.3157e-04  eta: 1:17:09  time: 0.3221  data_time: 0.0782  memory: 4383  loss: 1.9294  loss_cls: 0.5789  loss_bbox: 1.3505
2025/06/09 10:43:56 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:43:56 - mmengine - INFO - Epoch(train)  [18][31/31]  base_lr: 1.3932e-04 lr: 1.3932e-04  eta: 1:15:32  time: 0.3092  data_time: 0.0659  memory: 4427  loss: 1.9193  loss_cls: 0.5856  loss_bbox: 1.3337
2025/06/09 10:44:07 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:44:07 - mmengine - INFO - Epoch(train)  [19][31/31]  base_lr: 1.4708e-04 lr: 1.4708e-04  eta: 1:14:05  time: 0.3111  data_time: 0.0668  memory: 4517  loss: 1.8626  loss_cls: 0.6807  loss_bbox: 1.1819
2025/06/09 10:44:18 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:44:18 - mmengine - INFO - Epoch(train)  [20][31/31]  base_lr: 1.5483e-04 lr: 1.5483e-04  eta: 1:12:50  time: 0.3203  data_time: 0.0710  memory: 4695  loss: 1.6841  loss_cls: 0.6209  loss_bbox: 1.0632
2025/06/09 10:44:18 - mmengine - INFO - Saving checkpoint at 20 epochs
2025/06/09 10:44:23 - mmengine - INFO - Evaluating bbox...
2025/06/09 10:44:24 - mmengine - INFO - bbox_mAP_copypaste: 0.001 0.005 0.000 0.001 0.004 0.000
2025/06/09 10:44:25 - mmengine - INFO - Epoch(val) [20][20/20]    coco/bbox_mAP: 0.0010  coco/bbox_mAP_50: 0.0050  coco/bbox_mAP_75: 0.0000  coco/bbox_mAP_s: 0.0010  coco/bbox_mAP_m: 0.0040  coco/bbox_mAP_l: 0.0000  data_time: 0.0204  time: 0.1844
2025/06/09 10:44:25 - mmengine - INFO - The previous best checkpoint D:\OBJECT_DETECTION\sscma\ModelAssistant\work_dirs\rtmdet_nano_motherboard_final\best_coco_bbox_mAP_epoch_10.pth is removed
2025/06/09 10:44:25 - mmengine - INFO - The best checkpoint with 0.0010 coco/bbox_mAP at 20 epoch is saved to best_coco_bbox_mAP_epoch_20.pth.
2025/06/09 10:44:37 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:44:37 - mmengine - INFO - Epoch(train)  [21][31/31]  base_lr: 1.6258e-04 lr: 1.6258e-04  eta: 1:11:42  time: 0.3218  data_time: 0.0740  memory: 4383  loss: 1.5107  loss_cls: 0.4922  loss_bbox: 1.0185
2025/06/09 10:44:48 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:44:48 - mmengine - INFO - Epoch(train)  [22][31/31]  base_lr: 1.7034e-04 lr: 1.7034e-04  eta: 1:10:33  time: 0.3082  data_time: 0.0666  memory: 4472  loss: 1.4646  loss_cls: 0.4594  loss_bbox: 1.0052
2025/06/09 10:45:00 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:45:00 - mmengine - INFO - Epoch(train)  [23][31/31]  base_lr: 1.7809e-04 lr: 1.7809e-04  eta: 1:09:30  time: 0.3103  data_time: 0.0666  memory: 4606  loss: 1.4651  loss_cls: 0.4537  loss_bbox: 1.0114
2025/06/09 10:45:12 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:45:12 - mmengine - INFO - Epoch(train)  [24][31/31]  base_lr: 1.8585e-04 lr: 1.8585e-04  eta: 1:08:41  time: 0.3249  data_time: 0.0789  memory: 4383  loss: 1.4723  loss_cls: 0.4602  loss_bbox: 1.0122
2025/06/09 10:45:23 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:45:23 - mmengine - INFO - Epoch(train)  [25][31/31]  base_lr: 1.9360e-04 lr: 1.9360e-04  eta: 1:07:50  time: 0.3232  data_time: 0.0741  memory: 4204  loss: 1.4735  loss_cls: 0.4684  loss_bbox: 1.0051
2025/06/09 10:45:35 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:45:35 - mmengine - INFO - Epoch(train)  [26][31/31]  base_lr: 2.0135e-04 lr: 2.0135e-04  eta: 1:07:02  time: 0.3165  data_time: 0.0814  memory: 4472  loss: 1.4586  loss_cls: 0.4610  loss_bbox: 0.9975
2025/06/09 10:45:46 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:45:46 - mmengine - INFO - Epoch(train)  [27][31/31]  base_lr: 2.0911e-04 lr: 2.0911e-04  eta: 1:06:14  time: 0.3109  data_time: 0.0705  memory: 4249  loss: 1.4471  loss_cls: 0.4532  loss_bbox: 0.9939
2025/06/09 10:45:58 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:45:58 - mmengine - INFO - Epoch(train)  [28][31/31]  base_lr: 2.1686e-04 lr: 2.1686e-04  eta: 1:05:28  time: 0.3123  data_time: 0.0587  memory: 4427  loss: 1.4423  loss_cls: 0.4747  loss_bbox: 0.9676
2025/06/09 10:46:09 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:46:09 - mmengine - INFO - Epoch(train)  [29][31/31]  base_lr: 2.2462e-04 lr: 2.2462e-04  eta: 1:04:45  time: 0.3149  data_time: 0.0663  memory: 4383  loss: 1.4352  loss_cls: 0.4714  loss_bbox: 0.9637
2025/06/09 10:46:21 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:46:21 - mmengine - INFO - Epoch(train)  [30][31/31]  base_lr: 2.3237e-04 lr: 2.3237e-04  eta: 1:04:04  time: 0.3132  data_time: 0.0617  memory: 4338  loss: 1.4259  loss_cls: 0.4511  loss_bbox: 0.9749
2025/06/09 10:46:21 - mmengine - INFO - Saving checkpoint at 30 epochs
2025/06/09 10:46:26 - mmengine - INFO - Evaluating bbox...
2025/06/09 10:46:27 - mmengine - INFO - bbox_mAP_copypaste: 0.002 0.007 0.000 0.001 0.011 0.000
2025/06/09 10:46:27 - mmengine - INFO - Epoch(val) [30][20/20]    coco/bbox_mAP: 0.0020  coco/bbox_mAP_50: 0.0070  coco/bbox_mAP_75: 0.0000  coco/bbox_mAP_s: 0.0010  coco/bbox_mAP_m: 0.0110  coco/bbox_mAP_l: 0.0000  data_time: 0.0206  time: 0.1918
2025/06/09 10:46:27 - mmengine - INFO - The previous best checkpoint D:\OBJECT_DETECTION\sscma\ModelAssistant\work_dirs\rtmdet_nano_motherboard_final\best_coco_bbox_mAP_epoch_20.pth is removed
2025/06/09 10:46:28 - mmengine - INFO - The best checkpoint with 0.0020 coco/bbox_mAP at 30 epoch is saved to best_coco_bbox_mAP_epoch_30.pth.
2025/06/09 10:46:40 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:46:40 - mmengine - INFO - Epoch(train)  [31][31/31]  base_lr: 2.4012e-04 lr: 2.4012e-04  eta: 1:03:28  time: 0.3210  data_time: 0.0731  memory: 4271  loss: 1.4094  loss_cls: 0.4485  loss_bbox: 0.9609
2025/06/09 10:46:52 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:46:52 - mmengine - INFO - Epoch(train)  [32][31/31]  base_lr: 2.4788e-04 lr: 2.4788e-04  eta: 1:02:53  time: 0.3228  data_time: 0.0784  memory: 4539  loss: 1.4158  loss_cls: 0.4718  loss_bbox: 0.9441
2025/06/09 10:46:57 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:47:03 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:47:03 - mmengine - INFO - Epoch(train)  [33][31/31]  base_lr: 2.5563e-04 lr: 2.5563e-04  eta: 1:02:16  time: 0.3088  data_time: 0.0672  memory: 4293  loss: 1.4277  loss_cls: 0.4762  loss_bbox: 0.9515
2025/06/09 10:47:14 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:47:14 - mmengine - INFO - Epoch(train)  [34][31/31]  base_lr: 2.6338e-04 lr: 2.6338e-04  eta: 1:01:43  time: 0.3204  data_time: 0.0714  memory: 4606  loss: 1.4252  loss_cls: 0.4741  loss_bbox: 0.9512
2025/06/09 10:47:26 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:47:26 - mmengine - INFO - Epoch(train)  [35][31/31]  base_lr: 2.7114e-04 lr: 2.7114e-04  eta: 1:01:14  time: 0.3234  data_time: 0.0784  memory: 4249  loss: 1.4226  loss_cls: 0.4812  loss_bbox: 0.9414
2025/06/09 10:47:38 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:47:38 - mmengine - INFO - Epoch(train)  [36][31/31]  base_lr: 2.7889e-04 lr: 2.7889e-04  eta: 1:00:43  time: 0.3180  data_time: 0.0660  memory: 4517  loss: 1.4143  loss_cls: 0.4792  loss_bbox: 0.9350
2025/06/09 10:47:49 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:47:49 - mmengine - INFO - Epoch(train)  [37][31/31]  base_lr: 2.8665e-04 lr: 2.8665e-04  eta: 1:00:12  time: 0.3199  data_time: 0.0725  memory: 4695  loss: 1.4181  loss_cls: 0.4781  loss_bbox: 0.9400
2025/06/09 10:48:01 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:48:01 - mmengine - INFO - Epoch(train)  [38][31/31]  base_lr: 2.9440e-04 lr: 2.9440e-04  eta: 0:59:43  time: 0.3165  data_time: 0.0653  memory: 4606  loss: 1.4325  loss_cls: 0.4973  loss_bbox: 0.9352
2025/06/09 10:48:12 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:48:12 - mmengine - INFO - Epoch(train)  [39][31/31]  base_lr: 3.0215e-04 lr: 3.0215e-04  eta: 0:59:16  time: 0.3233  data_time: 0.0719  memory: 4293  loss: 1.4089  loss_cls: 0.5030  loss_bbox: 0.9059
2025/06/09 10:48:24 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:48:24 - mmengine - INFO - Epoch(train)  [40][31/31]  base_lr: 3.0991e-04 lr: 3.0991e-04  eta: 0:58:49  time: 0.3218  data_time: 0.0724  memory: 4561  loss: 1.4075  loss_cls: 0.5048  loss_bbox: 0.9027
2025/06/09 10:48:24 - mmengine - INFO - Saving checkpoint at 40 epochs
2025/06/09 10:48:30 - mmengine - INFO - Evaluating bbox...
2025/06/09 10:48:31 - mmengine - INFO - bbox_mAP_copypaste: 0.001 0.005 0.000 0.003 0.009 0.000
2025/06/09 10:48:31 - mmengine - INFO - Epoch(val) [40][20/20]    coco/bbox_mAP: 0.0010  coco/bbox_mAP_50: 0.0050  coco/bbox_mAP_75: 0.0000  coco/bbox_mAP_s: 0.0030  coco/bbox_mAP_m: 0.0090  coco/bbox_mAP_l: 0.0000  data_time: 0.0288  time: 0.2107
2025/06/09 10:48:43 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:48:43 - mmengine - INFO - Epoch(train)  [41][31/31]  base_lr: 3.1766e-04 lr: 3.1766e-04  eta: 0:58:23  time: 0.3136  data_time: 0.0677  memory: 4204  loss: 1.3958  loss_cls: 0.4780  loss_bbox: 0.9178
2025/06/09 10:48:54 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:48:54 - mmengine - INFO - Epoch(train)  [42][31/31]  base_lr: 3.2541e-04 lr: 3.2541e-04  eta: 0:57:56  time: 0.3158  data_time: 0.0687  memory: 4293  loss: 1.4631  loss_cls: 0.5066  loss_bbox: 0.9566
2025/06/09 10:49:05 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:49:05 - mmengine - INFO - Epoch(train)  [43][31/31]  base_lr: 3.3317e-04 lr: 3.3317e-04  eta: 0:57:30  time: 0.3156  data_time: 0.0673  memory: 4293  loss: 1.4563  loss_cls: 0.5196  loss_bbox: 0.9366
2025/06/09 10:49:17 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:49:17 - mmengine - INFO - Epoch(train)  [44][31/31]  base_lr: 3.4092e-04 lr: 3.4092e-04  eta: 0:57:08  time: 0.3234  data_time: 0.0717  memory: 4338  loss: 1.3151  loss_cls: 0.4534  loss_bbox: 0.8617
2025/06/09 10:49:29 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:49:29 - mmengine - INFO - Epoch(train)  [45][31/31]  base_lr: 3.4868e-04 lr: 3.4868e-04  eta: 0:56:45  time: 0.3278  data_time: 0.0686  memory: 4383  loss: 1.2557  loss_cls: 0.4351  loss_bbox: 0.8206
2025/06/09 10:49:41 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:49:41 - mmengine - INFO - Epoch(train)  [46][31/31]  base_lr: 3.5643e-04 lr: 3.5643e-04  eta: 0:56:23  time: 0.3278  data_time: 0.0783  memory: 4427  loss: 1.2257  loss_cls: 0.4347  loss_bbox: 0.7911
2025/06/09 10:49:52 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:49:52 - mmengine - INFO - Epoch(train)  [47][31/31]  base_lr: 3.6418e-04 lr: 3.6418e-04  eta: 0:56:01  time: 0.3144  data_time: 0.0687  memory: 4405  loss: 1.1951  loss_cls: 0.4392  loss_bbox: 0.7558
2025/06/09 10:50:04 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:50:04 - mmengine - INFO - Epoch(train)  [48][31/31]  base_lr: 3.7194e-04 lr: 3.7194e-04  eta: 0:55:40  time: 0.3200  data_time: 0.0807  memory: 4249  loss: 1.1783  loss_cls: 0.4429  loss_bbox: 0.7354
2025/06/09 10:50:16 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:50:16 - mmengine - INFO - Epoch(train)  [49][31/31]  base_lr: 3.7969e-04 lr: 3.7969e-04  eta: 0:55:18  time: 0.3137  data_time: 0.0588  memory: 4293  loss: 1.1533  loss_cls: 0.4418  loss_bbox: 0.7116
2025/06/09 10:50:28 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:50:28 - mmengine - INFO - Epoch(train)  [50][31/31]  base_lr: 3.8744e-04 lr: 3.8744e-04  eta: 0:54:58  time: 0.3233  data_time: 0.0906  memory: 4873  loss: 1.1523  loss_cls: 0.4529  loss_bbox: 0.6994
2025/06/09 10:50:28 - mmengine - INFO - Saving checkpoint at 50 epochs
2025/06/09 10:50:34 - mmengine - INFO - Evaluating bbox...
2025/06/09 10:50:35 - mmengine - INFO - bbox_mAP_copypaste: 0.108 0.288 0.063 0.002 0.006 0.215
2025/06/09 10:50:35 - mmengine - INFO - Epoch(val) [50][20/20]    coco/bbox_mAP: 0.1080  coco/bbox_mAP_50: 0.2880  coco/bbox_mAP_75: 0.0630  coco/bbox_mAP_s: 0.0020  coco/bbox_mAP_m: 0.0060  coco/bbox_mAP_l: 0.2150  data_time: 0.0630  time: 0.2339
2025/06/09 10:50:35 - mmengine - INFO - The previous best checkpoint D:\OBJECT_DETECTION\sscma\ModelAssistant\work_dirs\rtmdet_nano_motherboard_final\best_coco_bbox_mAP_epoch_30.pth is removed
2025/06/09 10:50:36 - mmengine - INFO - The best checkpoint with 0.1080 coco/bbox_mAP at 50 epoch is saved to best_coco_bbox_mAP_epoch_50.pth.
2025/06/09 10:50:48 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:50:48 - mmengine - INFO - Epoch(train)  [51][31/31]  base_lr: 3.9520e-04 lr: 3.9520e-04  eta: 0:54:39  time: 0.3230  data_time: 0.0775  memory: 4517  loss: 1.1377  loss_cls: 0.4580  loss_bbox: 0.6797
2025/06/09 10:51:00 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:51:00 - mmengine - INFO - Epoch(train)  [52][31/31]  base_lr: 4.0295e-04 lr: 4.0295e-04  eta: 0:54:18  time: 0.3142  data_time: 0.0610  memory: 4316  loss: 1.1416  loss_cls: 0.4603  loss_bbox: 0.6813
2025/06/09 10:51:12 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:51:12 - mmengine - INFO - Epoch(train)  [53][31/31]  base_lr: 4.1071e-04 lr: 4.1071e-04  eta: 0:53:58  time: 0.3173  data_time: 0.0656  memory: 4427  loss: 1.1338  loss_cls: 0.4618  loss_bbox: 0.6720
2025/06/09 10:51:24 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:51:24 - mmengine - INFO - Epoch(train)  [54][31/31]  base_lr: 4.1846e-04 lr: 4.1846e-04  eta: 0:53:40  time: 0.3254  data_time: 0.0750  memory: 4271  loss: 1.1198  loss_cls: 0.4567  loss_bbox: 0.6631
2025/06/09 10:51:36 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:51:36 - mmengine - INFO - Epoch(train)  [55][31/31]  base_lr: 4.2621e-04 lr: 4.2621e-04  eta: 0:53:25  time: 0.3376  data_time: 0.0905  memory: 4561  loss: 1.1058  loss_cls: 0.4600  loss_bbox: 0.6458
2025/06/09 10:51:48 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:51:48 - mmengine - INFO - Epoch(train)  [56][31/31]  base_lr: 4.3397e-04 lr: 4.3397e-04  eta: 0:53:07  time: 0.3217  data_time: 0.0656  memory: 4293  loss: 1.0934  loss_cls: 0.4658  loss_bbox: 0.6277
2025/06/09 10:52:00 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:52:00 - mmengine - INFO - Epoch(train)  [57][31/31]  base_lr: 4.4172e-04 lr: 4.4172e-04  eta: 0:52:49  time: 0.3223  data_time: 0.0720  memory: 4405  loss: 1.0826  loss_cls: 0.4621  loss_bbox: 0.6205
2025/06/09 10:52:12 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:52:12 - mmengine - INFO - Epoch(train)  [58][31/31]  base_lr: 4.4948e-04 lr: 4.4948e-04  eta: 0:52:30  time: 0.3168  data_time: 0.0721  memory: 4293  loss: 1.0755  loss_cls: 0.4600  loss_bbox: 0.6156
2025/06/09 10:52:23 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:52:23 - mmengine - INFO - Epoch(train)  [59][31/31]  base_lr: 4.5723e-04 lr: 4.5723e-04  eta: 0:52:10  time: 0.3115  data_time: 0.0603  memory: 4182  loss: 1.0778  loss_cls: 0.4662  loss_bbox: 0.6116
2025/06/09 10:52:35 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:52:35 - mmengine - INFO - Epoch(train)  [60][31/31]  base_lr: 4.6498e-04 lr: 4.6498e-04  eta: 0:51:52  time: 0.3167  data_time: 0.0644  memory: 4450  loss: 1.0612  loss_cls: 0.4606  loss_bbox: 0.6007
2025/06/09 10:52:35 - mmengine - INFO - Saving checkpoint at 60 epochs
2025/06/09 10:52:40 - mmengine - INFO - Evaluating bbox...
2025/06/09 10:52:41 - mmengine - INFO - bbox_mAP_copypaste: 0.193 0.371 0.198 0.000 0.025 0.384
2025/06/09 10:52:41 - mmengine - INFO - Epoch(val) [60][20/20]    coco/bbox_mAP: 0.1930  coco/bbox_mAP_50: 0.3710  coco/bbox_mAP_75: 0.1980  coco/bbox_mAP_s: 0.0000  coco/bbox_mAP_m: 0.0250  coco/bbox_mAP_l: 0.3840  data_time: 0.0194  time: 0.1857
2025/06/09 10:52:41 - mmengine - INFO - The previous best checkpoint D:\OBJECT_DETECTION\sscma\ModelAssistant\work_dirs\rtmdet_nano_motherboard_final\best_coco_bbox_mAP_epoch_50.pth is removed
2025/06/09 10:52:42 - mmengine - INFO - The best checkpoint with 0.1930 coco/bbox_mAP at 60 epoch is saved to best_coco_bbox_mAP_epoch_60.pth.
2025/06/09 10:52:55 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:52:55 - mmengine - INFO - Epoch(train)  [61][31/31]  base_lr: 4.7274e-04 lr: 4.7274e-04  eta: 0:51:38  time: 0.3365  data_time: 0.0858  memory: 4495  loss: 1.0540  loss_cls: 0.4581  loss_bbox: 0.5958
2025/06/09 10:53:07 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:53:07 - mmengine - INFO - Epoch(train)  [62][31/31]  base_lr: 4.8049e-04 lr: 4.8049e-04  eta: 0:51:21  time: 0.3219  data_time: 0.0765  memory: 4561  loss: 1.0575  loss_cls: 0.4636  loss_bbox: 0.5939
2025/06/09 10:53:19 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:53:19 - mmengine - INFO - Epoch(train)  [63][31/31]  base_lr: 4.8824e-04 lr: 4.8824e-04  eta: 0:51:03  time: 0.3168  data_time: 0.0725  memory: 4427  loss: 1.0398  loss_cls: 0.4635  loss_bbox: 0.5763
2025/06/09 10:53:30 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:53:30 - mmengine - INFO - Epoch(train)  [64][31/31]  base_lr: 4.9600e-04 lr: 4.9600e-04  eta: 0:50:46  time: 0.3209  data_time: 0.0670  memory: 4472  loss: 1.0289  loss_cls: 0.4559  loss_bbox: 0.5730
2025/06/09 10:53:38 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:53:41 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:53:41 - mmengine - INFO - Epoch(train)  [65][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:50:26  time: 0.3055  data_time: 0.0622  memory: 4695  loss: 1.0296  loss_cls: 0.4521  loss_bbox: 0.5775
2025/06/09 10:53:54 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:53:54 - mmengine - INFO - Epoch(train)  [66][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:50:10  time: 0.3292  data_time: 0.0775  memory: 4204  loss: 1.0306  loss_cls: 0.4570  loss_bbox: 0.5736
2025/06/09 10:54:06 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:54:06 - mmengine - INFO - Epoch(train)  [67][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:49:55  time: 0.3248  data_time: 0.0809  memory: 4561  loss: 1.0182  loss_cls: 0.4546  loss_bbox: 0.5636
2025/06/09 10:54:18 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:54:18 - mmengine - INFO - Epoch(train)  [68][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:49:41  time: 0.3342  data_time: 0.0735  memory: 4293  loss: 1.0149  loss_cls: 0.4524  loss_bbox: 0.5625
2025/06/09 10:54:31 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:54:31 - mmengine - INFO - Epoch(train)  [69][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:49:26  time: 0.3339  data_time: 0.0786  memory: 4293  loss: 1.0132  loss_cls: 0.4531  loss_bbox: 0.5601
2025/06/09 10:54:43 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:54:43 - mmengine - INFO - Epoch(train)  [70][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:49:12  time: 0.3307  data_time: 0.0839  memory: 4271  loss: 1.0110  loss_cls: 0.4586  loss_bbox: 0.5524
2025/06/09 10:54:43 - mmengine - INFO - Saving checkpoint at 70 epochs
2025/06/09 10:54:50 - mmengine - INFO - Evaluating bbox...
2025/06/09 10:54:51 - mmengine - INFO - bbox_mAP_copypaste: 0.214 0.401 0.209 0.001 0.015 0.426
2025/06/09 10:54:51 - mmengine - INFO - Epoch(val) [70][20/20]    coco/bbox_mAP: 0.2140  coco/bbox_mAP_50: 0.4010  coco/bbox_mAP_75: 0.2090  coco/bbox_mAP_s: 0.0010  coco/bbox_mAP_m: 0.0150  coco/bbox_mAP_l: 0.4260  data_time: 0.0192  time: 0.2541
2025/06/09 10:54:51 - mmengine - INFO - The previous best checkpoint D:\OBJECT_DETECTION\sscma\ModelAssistant\work_dirs\rtmdet_nano_motherboard_final\best_coco_bbox_mAP_epoch_60.pth is removed
2025/06/09 10:54:51 - mmengine - INFO - The best checkpoint with 0.2140 coco/bbox_mAP at 70 epoch is saved to best_coco_bbox_mAP_epoch_70.pth.
2025/06/09 10:55:04 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:55:04 - mmengine - INFO - Epoch(train)  [71][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:48:57  time: 0.3309  data_time: 0.0874  memory: 4405  loss: 1.0023  loss_cls: 0.4488  loss_bbox: 0.5535
2025/06/09 10:55:16 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:55:16 - mmengine - INFO - Epoch(train)  [72][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:48:41  time: 0.3245  data_time: 0.0677  memory: 4472  loss: 0.9945  loss_cls: 0.4478  loss_bbox: 0.5467
2025/06/09 10:55:29 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:55:29 - mmengine - INFO - Epoch(train)  [73][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:48:28  time: 0.3363  data_time: 0.0923  memory: 4383  loss: 0.9976  loss_cls: 0.4531  loss_bbox: 0.5445
2025/06/09 10:55:41 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:55:41 - mmengine - INFO - Epoch(train)  [74][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:48:13  time: 0.3317  data_time: 0.0794  memory: 4316  loss: 0.9830  loss_cls: 0.4408  loss_bbox: 0.5422
2025/06/09 10:55:52 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:55:52 - mmengine - INFO - Epoch(train)  [75][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:47:56  time: 0.3132  data_time: 0.0557  memory: 4405  loss: 0.9700  loss_cls: 0.4358  loss_bbox: 0.5342
2025/06/09 10:56:04 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:56:04 - mmengine - INFO - Epoch(train)  [76][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:47:41  time: 0.3226  data_time: 0.0710  memory: 4405  loss: 0.9763  loss_cls: 0.4392  loss_bbox: 0.5371
2025/06/09 10:56:16 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:56:16 - mmengine - INFO - Epoch(train)  [77][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:47:26  time: 0.3210  data_time: 0.0623  memory: 4517  loss: 0.9766  loss_cls: 0.4413  loss_bbox: 0.5354
2025/06/09 10:56:28 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:56:28 - mmengine - INFO - Epoch(train)  [78][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:47:11  time: 0.3305  data_time: 0.0836  memory: 4249  loss: 0.9769  loss_cls: 0.4453  loss_bbox: 0.5316
2025/06/09 10:56:40 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:56:40 - mmengine - INFO - Epoch(train)  [79][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:46:56  time: 0.3185  data_time: 0.0622  memory: 4517  loss: 0.9672  loss_cls: 0.4401  loss_bbox: 0.5271
2025/06/09 10:56:52 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:56:52 - mmengine - INFO - Epoch(train)  [80][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:46:40  time: 0.3162  data_time: 0.0600  memory: 4361  loss: 0.9718  loss_cls: 0.4420  loss_bbox: 0.5299
2025/06/09 10:56:52 - mmengine - INFO - Saving checkpoint at 80 epochs
2025/06/09 10:56:58 - mmengine - INFO - Evaluating bbox...
2025/06/09 10:56:59 - mmengine - INFO - bbox_mAP_copypaste: 0.237 0.414 0.248 0.001 0.030 0.474
2025/06/09 10:56:59 - mmengine - INFO - Epoch(val) [80][20/20]    coco/bbox_mAP: 0.2370  coco/bbox_mAP_50: 0.4140  coco/bbox_mAP_75: 0.2480  coco/bbox_mAP_s: 0.0010  coco/bbox_mAP_m: 0.0300  coco/bbox_mAP_l: 0.4740  data_time: 0.0183  time: 0.2273
2025/06/09 10:56:59 - mmengine - INFO - The previous best checkpoint D:\OBJECT_DETECTION\sscma\ModelAssistant\work_dirs\rtmdet_nano_motherboard_final\best_coco_bbox_mAP_epoch_70.pth is removed
2025/06/09 10:57:00 - mmengine - INFO - The best checkpoint with 0.2370 coco/bbox_mAP at 80 epoch is saved to best_coco_bbox_mAP_epoch_80.pth.
2025/06/09 10:57:12 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:57:12 - mmengine - INFO - Epoch(train)  [81][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:46:26  time: 0.3293  data_time: 0.0785  memory: 4383  loss: 0.9571  loss_cls: 0.4364  loss_bbox: 0.5208
2025/06/09 10:57:25 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:57:25 - mmengine - INFO - Epoch(train)  [82][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:46:12  time: 0.3351  data_time: 0.0736  memory: 4383  loss: 0.9566  loss_cls: 0.4359  loss_bbox: 0.5207
2025/06/09 10:57:37 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:57:37 - mmengine - INFO - Epoch(train)  [83][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:45:57  time: 0.3266  data_time: 0.0745  memory: 4517  loss: 0.9594  loss_cls: 0.4350  loss_bbox: 0.5244
2025/06/09 10:57:49 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:57:49 - mmengine - INFO - Epoch(train)  [84][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:45:44  time: 0.3341  data_time: 0.0739  memory: 4784  loss: 0.9451  loss_cls: 0.4280  loss_bbox: 0.5171
2025/06/09 10:58:01 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:58:01 - mmengine - INFO - Epoch(train)  [85][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:45:29  time: 0.3286  data_time: 0.0608  memory: 4517  loss: 0.9523  loss_cls: 0.4353  loss_bbox: 0.5169
2025/06/09 10:58:13 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:58:13 - mmengine - INFO - Epoch(train)  [86][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:45:14  time: 0.3204  data_time: 0.0680  memory: 4271  loss: 0.9611  loss_cls: 0.4418  loss_bbox: 0.5194
2025/06/09 10:58:25 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:58:25 - mmengine - INFO - Epoch(train)  [87][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:45:00  time: 0.3266  data_time: 0.0722  memory: 4271  loss: 0.9539  loss_cls: 0.4385  loss_bbox: 0.5154
2025/06/09 10:58:37 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:58:37 - mmengine - INFO - Epoch(train)  [88][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:44:45  time: 0.3183  data_time: 0.0678  memory: 4517  loss: 0.9428  loss_cls: 0.4321  loss_bbox: 0.5107
2025/06/09 10:58:49 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:58:49 - mmengine - INFO - Epoch(train)  [89][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:44:31  time: 0.3294  data_time: 0.0841  memory: 4606  loss: 0.9413  loss_cls: 0.4327  loss_bbox: 0.5086
2025/06/09 10:59:01 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:59:01 - mmengine - INFO - Epoch(train)  [90][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:44:18  time: 0.3353  data_time: 0.0629  memory: 4204  loss: 0.9250  loss_cls: 0.4237  loss_bbox: 0.5013
2025/06/09 10:59:01 - mmengine - INFO - Saving checkpoint at 90 epochs
2025/06/09 10:59:08 - mmengine - INFO - Evaluating bbox...
2025/06/09 10:59:09 - mmengine - INFO - bbox_mAP_copypaste: 0.259 0.421 0.271 0.001 0.028 0.514
2025/06/09 10:59:09 - mmengine - INFO - Epoch(val) [90][20/20]    coco/bbox_mAP: 0.2590  coco/bbox_mAP_50: 0.4210  coco/bbox_mAP_75: 0.2710  coco/bbox_mAP_s: 0.0010  coco/bbox_mAP_m: 0.0280  coco/bbox_mAP_l: 0.5140  data_time: 0.0289  time: 0.2426
2025/06/09 10:59:09 - mmengine - INFO - The previous best checkpoint D:\OBJECT_DETECTION\sscma\ModelAssistant\work_dirs\rtmdet_nano_motherboard_final\best_coco_bbox_mAP_epoch_80.pth is removed
2025/06/09 10:59:10 - mmengine - INFO - The best checkpoint with 0.2590 coco/bbox_mAP at 90 epoch is saved to best_coco_bbox_mAP_epoch_90.pth.
2025/06/09 10:59:23 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:59:23 - mmengine - INFO - Epoch(train)  [91][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:44:05  time: 0.3371  data_time: 0.0854  memory: 4606  loss: 0.9240  loss_cls: 0.4178  loss_bbox: 0.5062
2025/06/09 10:59:34 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:59:34 - mmengine - INFO - Epoch(train)  [92][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:43:51  time: 0.3243  data_time: 0.0720  memory: 4182  loss: 0.9250  loss_cls: 0.4197  loss_bbox: 0.5053
2025/06/09 10:59:47 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:59:47 - mmengine - INFO - Epoch(train)  [93][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:43:37  time: 0.3297  data_time: 0.0829  memory: 4204  loss: 0.9350  loss_cls: 0.4246  loss_bbox: 0.5104
2025/06/09 10:59:58 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 10:59:58 - mmengine - INFO - Epoch(train)  [94][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:43:22  time: 0.3178  data_time: 0.0750  memory: 4316  loss: 0.9255  loss_cls: 0.4211  loss_bbox: 0.5044
2025/06/09 11:00:10 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:00:10 - mmengine - INFO - Epoch(train)  [95][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:43:08  time: 0.3220  data_time: 0.0794  memory: 4495  loss: 0.9112  loss_cls: 0.4145  loss_bbox: 0.4966
2025/06/09 11:00:22 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:00:22 - mmengine - INFO - Epoch(train)  [96][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:42:54  time: 0.3188  data_time: 0.0607  memory: 4226  loss: 0.9156  loss_cls: 0.4176  loss_bbox: 0.4980
2025/06/09 11:00:33 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:00:34 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:00:34 - mmengine - INFO - Epoch(train)  [97][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:42:40  time: 0.3270  data_time: 0.0839  memory: 4338  loss: 0.9144  loss_cls: 0.4163  loss_bbox: 0.4981
2025/06/09 11:00:46 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:00:46 - mmengine - INFO - Epoch(train)  [98][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:42:26  time: 0.3215  data_time: 0.0797  memory: 4450  loss: 0.9128  loss_cls: 0.4155  loss_bbox: 0.4973
2025/06/09 11:00:58 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:00:58 - mmengine - INFO - Epoch(train)  [99][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:42:12  time: 0.3244  data_time: 0.0723  memory: 4472  loss: 0.9160  loss_cls: 0.4152  loss_bbox: 0.5008
2025/06/09 11:01:09 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:01:09 - mmengine - INFO - Epoch(train) [100][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:41:57  time: 0.3112  data_time: 0.0571  memory: 4361  loss: 0.9046  loss_cls: 0.4121  loss_bbox: 0.4925
2025/06/09 11:01:09 - mmengine - INFO - Saving checkpoint at 100 epochs
2025/06/09 11:01:17 - mmengine - INFO - Evaluating bbox...
2025/06/09 11:01:18 - mmengine - INFO - bbox_mAP_copypaste: 0.268 0.422 0.276 0.001 0.024 0.534
2025/06/09 11:01:18 - mmengine - INFO - Epoch(val) [100][20/20]    coco/bbox_mAP: 0.2680  coco/bbox_mAP_50: 0.4220  coco/bbox_mAP_75: 0.2760  coco/bbox_mAP_s: 0.0010  coco/bbox_mAP_m: 0.0240  coco/bbox_mAP_l: 0.5340  data_time: 0.0629  time: 0.2714
2025/06/09 11:01:18 - mmengine - INFO - The previous best checkpoint D:\OBJECT_DETECTION\sscma\ModelAssistant\work_dirs\rtmdet_nano_motherboard_final\best_coco_bbox_mAP_epoch_90.pth is removed
2025/06/09 11:01:18 - mmengine - INFO - The best checkpoint with 0.2680 coco/bbox_mAP at 100 epoch is saved to best_coco_bbox_mAP_epoch_100.pth.
2025/06/09 11:01:31 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:01:31 - mmengine - INFO - Epoch(train) [101][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:41:43  time: 0.3228  data_time: 0.0741  memory: 4293  loss: 0.9072  loss_cls: 0.4146  loss_bbox: 0.4926
2025/06/09 11:01:42 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:01:42 - mmengine - INFO - Epoch(train) [102][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:41:28  time: 0.3165  data_time: 0.0594  memory: 4338  loss: 0.9098  loss_cls: 0.4180  loss_bbox: 0.4918
2025/06/09 11:01:54 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:01:54 - mmengine - INFO - Epoch(train) [103][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:41:14  time: 0.3205  data_time: 0.0732  memory: 4561  loss: 0.9050  loss_cls: 0.4117  loss_bbox: 0.4934
2025/06/09 11:02:06 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:02:06 - mmengine - INFO - Epoch(train) [104][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:41:01  time: 0.3287  data_time: 0.0849  memory: 4293  loss: 0.8977  loss_cls: 0.4077  loss_bbox: 0.4900
2025/06/09 11:02:19 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:02:19 - mmengine - INFO - Epoch(train) [105][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:40:48  time: 0.3319  data_time: 0.0792  memory: 4427  loss: 0.8808  loss_cls: 0.4000  loss_bbox: 0.4807
2025/06/09 11:02:31 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:02:31 - mmengine - INFO - Epoch(train) [106][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:40:35  time: 0.3292  data_time: 0.0705  memory: 4405  loss: 0.8890  loss_cls: 0.4032  loss_bbox: 0.4859
2025/06/09 11:02:43 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:02:43 - mmengine - INFO - Epoch(train) [107][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:40:22  time: 0.3340  data_time: 0.0789  memory: 4427  loss: 0.8937  loss_cls: 0.4099  loss_bbox: 0.4838
2025/06/09 11:02:56 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:02:56 - mmengine - INFO - Epoch(train) [108][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:40:09  time: 0.3391  data_time: 0.0839  memory: 4249  loss: 0.8938  loss_cls: 0.4079  loss_bbox: 0.4859
2025/06/09 11:03:08 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:03:08 - mmengine - INFO - Epoch(train) [109][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:39:56  time: 0.3292  data_time: 0.0732  memory: 4293  loss: 0.8807  loss_cls: 0.3995  loss_bbox: 0.4811
2025/06/09 11:03:20 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:03:20 - mmengine - INFO - Epoch(train) [110][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:39:42  time: 0.3209  data_time: 0.0659  memory: 4606  loss: 0.8909  loss_cls: 0.4058  loss_bbox: 0.4850
2025/06/09 11:03:20 - mmengine - INFO - Saving checkpoint at 110 epochs
2025/06/09 11:03:26 - mmengine - INFO - Evaluating bbox...
2025/06/09 11:03:28 - mmengine - INFO - bbox_mAP_copypaste: 0.287 0.437 0.306 0.001 0.034 0.571
2025/06/09 11:03:28 - mmengine - INFO - Epoch(val) [110][20/20]    coco/bbox_mAP: 0.2870  coco/bbox_mAP_50: 0.4370  coco/bbox_mAP_75: 0.3060  coco/bbox_mAP_s: 0.0010  coco/bbox_mAP_m: 0.0340  coco/bbox_mAP_l: 0.5710  data_time: 0.0187  time: 0.2503
2025/06/09 11:03:28 - mmengine - INFO - The previous best checkpoint D:\OBJECT_DETECTION\sscma\ModelAssistant\work_dirs\rtmdet_nano_motherboard_final\best_coco_bbox_mAP_epoch_100.pth is removed
2025/06/09 11:03:28 - mmengine - INFO - The best checkpoint with 0.2870 coco/bbox_mAP at 110 epoch is saved to best_coco_bbox_mAP_epoch_110.pth.
2025/06/09 11:03:41 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:03:41 - mmengine - INFO - Epoch(train) [111][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:39:29  time: 0.3331  data_time: 0.0719  memory: 4517  loss: 0.8876  loss_cls: 0.4050  loss_bbox: 0.4826
2025/06/09 11:03:54 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:03:54 - mmengine - INFO - Epoch(train) [112][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:39:17  time: 0.3362  data_time: 0.0824  memory: 4495  loss: 0.8713  loss_cls: 0.3974  loss_bbox: 0.4739
2025/06/09 11:04:06 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:04:06 - mmengine - INFO - Epoch(train) [113][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:39:04  time: 0.3277  data_time: 0.0804  memory: 4383  loss: 0.8726  loss_cls: 0.3986  loss_bbox: 0.4740
2025/06/09 11:04:18 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:04:18 - mmengine - INFO - Epoch(train) [114][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:38:50  time: 0.3293  data_time: 0.0776  memory: 4606  loss: 0.8715  loss_cls: 0.3953  loss_bbox: 0.4763
2025/06/09 11:04:30 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:04:30 - mmengine - INFO - Epoch(train) [115][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:38:37  time: 0.3299  data_time: 0.0861  memory: 4427  loss: 0.8694  loss_cls: 0.3925  loss_bbox: 0.4769
2025/06/09 11:04:42 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:04:42 - mmengine - INFO - Epoch(train) [116][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:38:23  time: 0.3177  data_time: 0.0624  memory: 4427  loss: 0.8766  loss_cls: 0.3974  loss_bbox: 0.4792
2025/06/09 11:04:54 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:04:54 - mmengine - INFO - Epoch(train) [117][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:38:10  time: 0.3199  data_time: 0.0756  memory: 4383  loss: 0.8647  loss_cls: 0.3930  loss_bbox: 0.4717
2025/06/09 11:05:05 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:05:05 - mmengine - INFO - Epoch(train) [118][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:37:56  time: 0.3201  data_time: 0.0760  memory: 4204  loss: 0.8616  loss_cls: 0.3877  loss_bbox: 0.4740
2025/06/09 11:05:17 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:05:17 - mmengine - INFO - Epoch(train) [119][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:37:42  time: 0.3168  data_time: 0.0610  memory: 4427  loss: 0.8649  loss_cls: 0.3920  loss_bbox: 0.4730
2025/06/09 11:05:29 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:05:29 - mmengine - INFO - Epoch(train) [120][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:37:29  time: 0.3231  data_time: 0.0719  memory: 4383  loss: 0.8613  loss_cls: 0.3897  loss_bbox: 0.4716
2025/06/09 11:05:29 - mmengine - INFO - Saving checkpoint at 120 epochs
2025/06/09 11:05:35 - mmengine - INFO - Evaluating bbox...
2025/06/09 11:05:36 - mmengine - INFO - bbox_mAP_copypaste: 0.300 0.442 0.326 0.001 0.022 0.599
2025/06/09 11:05:36 - mmengine - INFO - Epoch(val) [120][20/20]    coco/bbox_mAP: 0.3000  coco/bbox_mAP_50: 0.4420  coco/bbox_mAP_75: 0.3260  coco/bbox_mAP_s: 0.0010  coco/bbox_mAP_m: 0.0220  coco/bbox_mAP_l: 0.5990  data_time: 0.0199  time: 0.2201
2025/06/09 11:05:36 - mmengine - INFO - The previous best checkpoint D:\OBJECT_DETECTION\sscma\ModelAssistant\work_dirs\rtmdet_nano_motherboard_final\best_coco_bbox_mAP_epoch_110.pth is removed
2025/06/09 11:05:37 - mmengine - INFO - The best checkpoint with 0.3000 coco/bbox_mAP at 120 epoch is saved to best_coco_bbox_mAP_epoch_120.pth.
2025/06/09 11:05:49 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:05:49 - mmengine - INFO - Epoch(train) [121][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:37:15  time: 0.3207  data_time: 0.0715  memory: 4316  loss: 0.8585  loss_cls: 0.3893  loss_bbox: 0.4692
2025/06/09 11:06:01 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:06:01 - mmengine - INFO - Epoch(train) [122][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:37:02  time: 0.3178  data_time: 0.0690  memory: 4293  loss: 0.8707  loss_cls: 0.3937  loss_bbox: 0.4770
2025/06/09 11:06:12 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:06:12 - mmengine - INFO - Epoch(train) [123][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:36:48  time: 0.3189  data_time: 0.0658  memory: 4361  loss: 0.8625  loss_cls: 0.3886  loss_bbox: 0.4739
2025/06/09 11:06:25 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:06:25 - mmengine - INFO - Epoch(train) [124][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:36:35  time: 0.3271  data_time: 0.0887  memory: 4606  loss: 0.8750  loss_cls: 0.3992  loss_bbox: 0.4758
2025/06/09 11:06:36 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:06:36 - mmengine - INFO - Epoch(train) [125][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:36:22  time: 0.3229  data_time: 0.0730  memory: 4338  loss: 0.8611  loss_cls: 0.3932  loss_bbox: 0.4679
2025/06/09 11:06:49 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:06:49 - mmengine - INFO - Epoch(train) [126][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:36:09  time: 0.3261  data_time: 0.0869  memory: 4338  loss: 0.8543  loss_cls: 0.3882  loss_bbox: 0.4661
2025/06/09 11:07:00 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:07:00 - mmengine - INFO - Epoch(train) [127][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:35:55  time: 0.3170  data_time: 0.0659  memory: 4561  loss: 0.8510  loss_cls: 0.3863  loss_bbox: 0.4647
2025/06/09 11:07:12 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:07:12 - mmengine - INFO - Epoch(train) [128][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:35:42  time: 0.3236  data_time: 0.0635  memory: 4383  loss: 0.8546  loss_cls: 0.3854  loss_bbox: 0.4692
2025/06/09 11:07:24 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:07:24 - mmengine - INFO - Epoch(train) [129][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:35:28  time: 0.3159  data_time: 0.0669  memory: 4383  loss: 0.8490  loss_cls: 0.3818  loss_bbox: 0.4672
2025/06/09 11:07:28 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:07:35 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:07:35 - mmengine - INFO - Epoch(train) [130][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:35:15  time: 0.3164  data_time: 0.0730  memory: 4427  loss: 0.8508  loss_cls: 0.3817  loss_bbox: 0.4691
2025/06/09 11:07:35 - mmengine - INFO - Saving checkpoint at 130 epochs
2025/06/09 11:07:41 - mmengine - INFO - Evaluating bbox...
2025/06/09 11:07:43 - mmengine - INFO - bbox_mAP_copypaste: 0.323 0.454 0.357 0.001 0.026 0.645
2025/06/09 11:07:43 - mmengine - INFO - Epoch(val) [130][20/20]    coco/bbox_mAP: 0.3230  coco/bbox_mAP_50: 0.4540  coco/bbox_mAP_75: 0.3570  coco/bbox_mAP_s: 0.0010  coco/bbox_mAP_m: 0.0260  coco/bbox_mAP_l: 0.6450  data_time: 0.0189  time: 0.2201
2025/06/09 11:07:43 - mmengine - INFO - The previous best checkpoint D:\OBJECT_DETECTION\sscma\ModelAssistant\work_dirs\rtmdet_nano_motherboard_final\best_coco_bbox_mAP_epoch_120.pth is removed
2025/06/09 11:07:43 - mmengine - INFO - The best checkpoint with 0.3230 coco/bbox_mAP at 130 epoch is saved to best_coco_bbox_mAP_epoch_130.pth.
2025/06/09 11:07:56 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:07:56 - mmengine - INFO - Epoch(train) [131][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:35:02  time: 0.3249  data_time: 0.0763  memory: 4740  loss: 0.8566  loss_cls: 0.3868  loss_bbox: 0.4699
2025/06/09 11:08:07 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:08:07 - mmengine - INFO - Epoch(train) [132][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:34:48  time: 0.3172  data_time: 0.0647  memory: 4472  loss: 0.8501  loss_cls: 0.3835  loss_bbox: 0.4666
2025/06/09 11:08:19 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:08:19 - mmengine - INFO - Epoch(train) [133][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:34:35  time: 0.3201  data_time: 0.0742  memory: 4427  loss: 0.8334  loss_cls: 0.3755  loss_bbox: 0.4579
2025/06/09 11:08:31 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:08:31 - mmengine - INFO - Epoch(train) [134][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:34:22  time: 0.3269  data_time: 0.0824  memory: 4338  loss: 0.8428  loss_cls: 0.3781  loss_bbox: 0.4648
2025/06/09 11:08:43 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:08:43 - mmengine - INFO - Epoch(train) [135][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:34:09  time: 0.3250  data_time: 0.0787  memory: 4361  loss: 0.8583  loss_cls: 0.3870  loss_bbox: 0.4714
2025/06/09 11:08:55 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:08:55 - mmengine - INFO - Epoch(train) [136][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:33:56  time: 0.3176  data_time: 0.0642  memory: 4338  loss: 0.8470  loss_cls: 0.3775  loss_bbox: 0.4695
2025/06/09 11:09:06 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:09:06 - mmengine - INFO - Epoch(train) [137][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:33:43  time: 0.3185  data_time: 0.0768  memory: 4338  loss: 0.8365  loss_cls: 0.3673  loss_bbox: 0.4692
2025/06/09 11:09:18 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:09:18 - mmengine - INFO - Epoch(train) [138][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:33:29  time: 0.3172  data_time: 0.0722  memory: 4383  loss: 0.8381  loss_cls: 0.3734  loss_bbox: 0.4647
2025/06/09 11:09:30 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:09:30 - mmengine - INFO - Epoch(train) [139][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:33:16  time: 0.3186  data_time: 0.0666  memory: 4427  loss: 0.8438  loss_cls: 0.3818  loss_bbox: 0.4620
2025/06/09 11:09:42 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:09:42 - mmengine - INFO - Epoch(train) [140][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:33:03  time: 0.3192  data_time: 0.0773  memory: 4249  loss: 0.8442  loss_cls: 0.3824  loss_bbox: 0.4618
2025/06/09 11:09:42 - mmengine - INFO - Saving checkpoint at 140 epochs
2025/06/09 11:09:48 - mmengine - INFO - Evaluating bbox...
2025/06/09 11:09:49 - mmengine - INFO - bbox_mAP_copypaste: 0.316 0.458 0.344 0.002 0.047 0.625
2025/06/09 11:09:49 - mmengine - INFO - Epoch(val) [140][20/20]    coco/bbox_mAP: 0.3160  coco/bbox_mAP_50: 0.4580  coco/bbox_mAP_75: 0.3440  coco/bbox_mAP_s: 0.0020  coco/bbox_mAP_m: 0.0470  coco/bbox_mAP_l: 0.6250  data_time: 0.0259  time: 0.2277
2025/06/09 11:10:01 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:10:01 - mmengine - INFO - Epoch(train) [141][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:32:50  time: 0.3139  data_time: 0.0663  memory: 4606  loss: 0.8338  loss_cls: 0.3772  loss_bbox: 0.4566
2025/06/09 11:10:13 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:10:13 - mmengine - INFO - Epoch(train) [142][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:32:37  time: 0.3211  data_time: 0.0661  memory: 4338  loss: 0.8239  loss_cls: 0.3683  loss_bbox: 0.4556
2025/06/09 11:10:25 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:10:25 - mmengine - INFO - Epoch(train) [143][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:32:24  time: 0.3251  data_time: 0.0776  memory: 4316  loss: 0.8326  loss_cls: 0.3746  loss_bbox: 0.4581
2025/06/09 11:10:36 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:10:36 - mmengine - INFO - Epoch(train) [144][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:32:11  time: 0.3202  data_time: 0.0641  memory: 4383  loss: 0.8335  loss_cls: 0.3735  loss_bbox: 0.4600
2025/06/09 11:10:48 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:10:48 - mmengine - INFO - Epoch(train) [145][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:31:58  time: 0.3238  data_time: 0.0735  memory: 4383  loss: 0.8299  loss_cls: 0.3705  loss_bbox: 0.4594
2025/06/09 11:11:00 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:11:00 - mmengine - INFO - Epoch(train) [146][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:31:45  time: 0.3238  data_time: 0.0825  memory: 4628  loss: 0.8362  loss_cls: 0.3780  loss_bbox: 0.4582
2025/06/09 11:11:12 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:11:12 - mmengine - INFO - Epoch(train) [147][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:31:32  time: 0.3165  data_time: 0.0661  memory: 4249  loss: 0.8190  loss_cls: 0.3709  loss_bbox: 0.4482
2025/06/09 11:11:24 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:11:24 - mmengine - INFO - Epoch(train) [148][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:31:19  time: 0.3186  data_time: 0.0731  memory: 4361  loss: 0.8238  loss_cls: 0.3722  loss_bbox: 0.4517
2025/06/09 11:11:35 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:11:35 - mmengine - INFO - Epoch(train) [149][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:31:06  time: 0.3219  data_time: 0.0799  memory: 4361  loss: 0.8219  loss_cls: 0.3738  loss_bbox: 0.4481
2025/06/09 11:11:47 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:11:47 - mmengine - INFO - Epoch(train) [150][31/31]  base_lr: 5.0000e-04 lr: 5.0000e-04  eta: 0:30:53  time: 0.3248  data_time: 0.0788  memory: 4517  loss: 0.8157  loss_cls: 0.3692  loss_bbox: 0.4465
2025/06/09 11:11:47 - mmengine - INFO - Saving checkpoint at 150 epochs
2025/06/09 11:11:54 - mmengine - INFO - Evaluating bbox...
2025/06/09 11:11:55 - mmengine - INFO - bbox_mAP_copypaste: 0.327 0.464 0.347 0.002 0.040 0.648
2025/06/09 11:11:55 - mmengine - INFO - Epoch(val) [150][20/20]    coco/bbox_mAP: 0.3270  coco/bbox_mAP_50: 0.4640  coco/bbox_mAP_75: 0.3470  coco/bbox_mAP_s: 0.0020  coco/bbox_mAP_m: 0.0400  coco/bbox_mAP_l: 0.6480  data_time: 0.0609  time: 0.2626
2025/06/09 11:11:55 - mmengine - INFO - The previous best checkpoint D:\OBJECT_DETECTION\sscma\ModelAssistant\work_dirs\rtmdet_nano_motherboard_final\best_coco_bbox_mAP_epoch_130.pth is removed
2025/06/09 11:11:56 - mmengine - INFO - The best checkpoint with 0.3270 coco/bbox_mAP at 150 epoch is saved to best_coco_bbox_mAP_epoch_150.pth.
2025/06/09 11:12:08 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:12:08 - mmengine - INFO - Epoch(train) [151][31/31]  base_lr: 4.9995e-04 lr: 4.9995e-04  eta: 0:30:40  time: 0.3217  data_time: 0.0696  memory: 4338  loss: 0.8168  loss_cls: 0.3669  loss_bbox: 0.4499
2025/06/09 11:12:20 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:12:20 - mmengine - INFO - Epoch(train) [152][31/31]  base_lr: 4.9980e-04 lr: 4.9980e-04  eta: 0:30:27  time: 0.3211  data_time: 0.0601  memory: 4383  loss: 0.8315  loss_cls: 0.3728  loss_bbox: 0.4587
2025/06/09 11:12:32 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:12:32 - mmengine - INFO - Epoch(train) [153][31/31]  base_lr: 4.9954e-04 lr: 4.9954e-04  eta: 0:30:15  time: 0.3226  data_time: 0.0645  memory: 4293  loss: 0.8121  loss_cls: 0.3668  loss_bbox: 0.4453
2025/06/09 11:12:44 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:12:44 - mmengine - INFO - Epoch(train) [154][31/31]  base_lr: 4.9918e-04 lr: 4.9918e-04  eta: 0:30:02  time: 0.3304  data_time: 0.0845  memory: 4249  loss: 0.8200  loss_cls: 0.3707  loss_bbox: 0.4493
2025/06/09 11:12:56 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:12:56 - mmengine - INFO - Epoch(train) [155][31/31]  base_lr: 4.9872e-04 lr: 4.9872e-04  eta: 0:29:49  time: 0.3283  data_time: 0.0830  memory: 4784  loss: 0.8221  loss_cls: 0.3705  loss_bbox: 0.4516
2025/06/09 11:13:07 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:13:07 - mmengine - INFO - Epoch(train) [156][31/31]  base_lr: 4.9815e-04 lr: 4.9815e-04  eta: 0:29:36  time: 0.3116  data_time: 0.0653  memory: 4293  loss: 0.8156  loss_cls: 0.3702  loss_bbox: 0.4454
2025/06/09 11:13:20 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:13:20 - mmengine - INFO - Epoch(train) [157][31/31]  base_lr: 4.9748e-04 lr: 4.9748e-04  eta: 0:29:24  time: 0.3426  data_time: 0.0768  memory: 4338  loss: 0.8159  loss_cls: 0.3711  loss_bbox: 0.4447
2025/06/09 11:13:31 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:13:31 - mmengine - INFO - Epoch(train) [158][31/31]  base_lr: 4.9670e-04 lr: 4.9670e-04  eta: 0:29:11  time: 0.3138  data_time: 0.0667  memory: 4338  loss: 0.8086  loss_cls: 0.3632  loss_bbox: 0.4453
2025/06/09 11:13:43 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:13:43 - mmengine - INFO - Epoch(train) [159][31/31]  base_lr: 4.9582e-04 lr: 4.9582e-04  eta: 0:28:58  time: 0.3148  data_time: 0.0742  memory: 4427  loss: 0.8053  loss_cls: 0.3616  loss_bbox: 0.4436
2025/06/09 11:13:54 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:13:54 - mmengine - INFO - Epoch(train) [160][31/31]  base_lr: 4.9484e-04 lr: 4.9484e-04  eta: 0:28:44  time: 0.3129  data_time: 0.0688  memory: 4338  loss: 0.8032  loss_cls: 0.3622  loss_bbox: 0.4410
2025/06/09 11:13:54 - mmengine - INFO - Saving checkpoint at 160 epochs
2025/06/09 11:14:00 - mmengine - INFO - Evaluating bbox...
2025/06/09 11:14:01 - mmengine - INFO - bbox_mAP_copypaste: 0.333 0.465 0.354 0.002 0.032 0.664
2025/06/09 11:14:02 - mmengine - INFO - Epoch(val) [160][20/20]    coco/bbox_mAP: 0.3330  coco/bbox_mAP_50: 0.4650  coco/bbox_mAP_75: 0.3540  coco/bbox_mAP_s: 0.0020  coco/bbox_mAP_m: 0.0320  coco/bbox_mAP_l: 0.6640  data_time: 0.0194  time: 0.2325
2025/06/09 11:14:02 - mmengine - INFO - The previous best checkpoint D:\OBJECT_DETECTION\sscma\ModelAssistant\work_dirs\rtmdet_nano_motherboard_final\best_coco_bbox_mAP_epoch_150.pth is removed
2025/06/09 11:14:02 - mmengine - INFO - The best checkpoint with 0.3330 coco/bbox_mAP at 160 epoch is saved to best_coco_bbox_mAP_epoch_160.pth.
2025/06/09 11:14:14 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:14:14 - mmengine - INFO - Epoch(train) [161][31/31]  base_lr: 4.9376e-04 lr: 4.9376e-04  eta: 0:28:31  time: 0.3161  data_time: 0.0662  memory: 4517  loss: 0.8180  loss_cls: 0.3729  loss_bbox: 0.4451
2025/06/09 11:14:20 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:14:25 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:14:25 - mmengine - INFO - Epoch(train) [162][31/31]  base_lr: 4.9258e-04 lr: 4.9258e-04  eta: 0:28:18  time: 0.3159  data_time: 0.0701  memory: 4472  loss: 0.8215  loss_cls: 0.3727  loss_bbox: 0.4487
2025/06/09 11:14:37 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:14:37 - mmengine - INFO - Epoch(train) [163][31/31]  base_lr: 4.9129e-04 lr: 4.9129e-04  eta: 0:28:05  time: 0.3192  data_time: 0.0664  memory: 4271  loss: 0.8167  loss_cls: 0.3717  loss_bbox: 0.4449
2025/06/09 11:14:48 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:14:48 - mmengine - INFO - Epoch(train) [164][31/31]  base_lr: 4.8991e-04 lr: 4.8991e-04  eta: 0:27:52  time: 0.3223  data_time: 0.0679  memory: 4293  loss: 0.7961  loss_cls: 0.3602  loss_bbox: 0.4359
2025/06/09 11:15:00 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:15:00 - mmengine - INFO - Epoch(train) [165][31/31]  base_lr: 4.8843e-04 lr: 4.8843e-04  eta: 0:27:39  time: 0.3201  data_time: 0.0690  memory: 4583  loss: 0.7945  loss_cls: 0.3555  loss_bbox: 0.4390
2025/06/09 11:15:12 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:15:12 - mmengine - INFO - Epoch(train) [166][31/31]  base_lr: 4.8684e-04 lr: 4.8684e-04  eta: 0:27:27  time: 0.3193  data_time: 0.0749  memory: 4651  loss: 0.7888  loss_cls: 0.3544  loss_bbox: 0.4344
2025/06/09 11:15:23 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:15:23 - mmengine - INFO - Epoch(train) [167][31/31]  base_lr: 4.8516e-04 lr: 4.8516e-04  eta: 0:27:14  time: 0.3181  data_time: 0.0601  memory: 4583  loss: 0.7933  loss_cls: 0.3539  loss_bbox: 0.4394
2025/06/09 11:15:35 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:15:35 - mmengine - INFO - Epoch(train) [168][31/31]  base_lr: 4.8338e-04 lr: 4.8338e-04  eta: 0:27:01  time: 0.3231  data_time: 0.0657  memory: 4740  loss: 0.7998  loss_cls: 0.3581  loss_bbox: 0.4417
2025/06/09 11:15:47 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:15:47 - mmengine - INFO - Epoch(train) [169][31/31]  base_lr: 4.8150e-04 lr: 4.8150e-04  eta: 0:26:48  time: 0.3284  data_time: 0.0798  memory: 4651  loss: 0.8028  loss_cls: 0.3609  loss_bbox: 0.4419
2025/06/09 11:15:58 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:15:58 - mmengine - INFO - Epoch(train) [170][31/31]  base_lr: 4.7953e-04 lr: 4.7953e-04  eta: 0:26:35  time: 0.3087  data_time: 0.0566  memory: 4472  loss: 0.7959  loss_cls: 0.3563  loss_bbox: 0.4396
2025/06/09 11:15:58 - mmengine - INFO - Saving checkpoint at 170 epochs
2025/06/09 11:16:04 - mmengine - INFO - Evaluating bbox...
2025/06/09 11:16:05 - mmengine - INFO - bbox_mAP_copypaste: 0.336 0.467 0.366 0.002 0.053 0.666
2025/06/09 11:16:05 - mmengine - INFO - Epoch(val) [170][20/20]    coco/bbox_mAP: 0.3360  coco/bbox_mAP_50: 0.4670  coco/bbox_mAP_75: 0.3660  coco/bbox_mAP_s: 0.0020  coco/bbox_mAP_m: 0.0530  coco/bbox_mAP_l: 0.6660  data_time: 0.0174  time: 0.2173
2025/06/09 11:16:05 - mmengine - INFO - The previous best checkpoint D:\OBJECT_DETECTION\sscma\ModelAssistant\work_dirs\rtmdet_nano_motherboard_final\best_coco_bbox_mAP_epoch_160.pth is removed
2025/06/09 11:16:05 - mmengine - INFO - The best checkpoint with 0.3360 coco/bbox_mAP at 170 epoch is saved to best_coco_bbox_mAP_epoch_170.pth.
2025/06/09 11:16:17 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:16:17 - mmengine - INFO - Epoch(train) [171][31/31]  base_lr: 4.7746e-04 lr: 4.7746e-04  eta: 0:26:22  time: 0.3125  data_time: 0.0690  memory: 4383  loss: 0.7946  loss_cls: 0.3556  loss_bbox: 0.4390
2025/06/09 11:16:29 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:16:29 - mmengine - INFO - Epoch(train) [172][31/31]  base_lr: 4.7530e-04 lr: 4.7530e-04  eta: 0:26:09  time: 0.3129  data_time: 0.0780  memory: 4427  loss: 0.8054  loss_cls: 0.3650  loss_bbox: 0.4404
2025/06/09 11:16:41 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:16:41 - mmengine - INFO - Epoch(train) [173][31/31]  base_lr: 4.7305e-04 lr: 4.7305e-04  eta: 0:25:57  time: 0.3277  data_time: 0.0817  memory: 4517  loss: 0.7965  loss_cls: 0.3628  loss_bbox: 0.4337
2025/06/09 11:16:53 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:16:53 - mmengine - INFO - Epoch(train) [174][31/31]  base_lr: 4.7070e-04 lr: 4.7070e-04  eta: 0:25:44  time: 0.3272  data_time: 0.0746  memory: 4293  loss: 0.7899  loss_cls: 0.3567  loss_bbox: 0.4332
2025/06/09 11:17:04 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:17:04 - mmengine - INFO - Epoch(train) [175][31/31]  base_lr: 4.6826e-04 lr: 4.6826e-04  eta: 0:25:32  time: 0.3218  data_time: 0.0639  memory: 4427  loss: 0.7994  loss_cls: 0.3574  loss_bbox: 0.4421
2025/06/09 11:17:15 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:17:15 - mmengine - INFO - Epoch(train) [176][31/31]  base_lr: 4.6573e-04 lr: 4.6573e-04  eta: 0:25:19  time: 0.3120  data_time: 0.0700  memory: 4517  loss: 0.8073  loss_cls: 0.3663  loss_bbox: 0.4410
2025/06/09 11:17:27 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:17:27 - mmengine - INFO - Epoch(train) [177][31/31]  base_lr: 4.6311e-04 lr: 4.6311e-04  eta: 0:25:06  time: 0.3123  data_time: 0.0713  memory: 4338  loss: 0.7946  loss_cls: 0.3643  loss_bbox: 0.4303
2025/06/09 11:17:38 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:17:38 - mmengine - INFO - Epoch(train) [178][31/31]  base_lr: 4.6041e-04 lr: 4.6041e-04  eta: 0:24:53  time: 0.3123  data_time: 0.0635  memory: 4249  loss: 0.7812  loss_cls: 0.3542  loss_bbox: 0.4269
2025/06/09 11:17:49 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:17:49 - mmengine - INFO - Epoch(train) [179][31/31]  base_lr: 4.5761e-04 lr: 4.5761e-04  eta: 0:24:40  time: 0.3111  data_time: 0.0694  memory: 4784  loss: 0.7792  loss_cls: 0.3542  loss_bbox: 0.4249
2025/06/09 11:18:00 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:18:00 - mmengine - INFO - Epoch(train) [180][31/31]  base_lr: 4.5474e-04 lr: 4.5474e-04  eta: 0:24:27  time: 0.3111  data_time: 0.0683  memory: 4517  loss: 0.7926  loss_cls: 0.3621  loss_bbox: 0.4305
2025/06/09 11:18:00 - mmengine - INFO - Saving checkpoint at 180 epochs
2025/06/09 11:18:06 - mmengine - INFO - Evaluating bbox...
2025/06/09 11:18:07 - mmengine - INFO - bbox_mAP_copypaste: 0.347 0.480 0.371 0.002 0.043 0.686
2025/06/09 11:18:08 - mmengine - INFO - Epoch(val) [180][20/20]    coco/bbox_mAP: 0.3470  coco/bbox_mAP_50: 0.4800  coco/bbox_mAP_75: 0.3710  coco/bbox_mAP_s: 0.0020  coco/bbox_mAP_m: 0.0430  coco/bbox_mAP_l: 0.6860  data_time: 0.0180  time: 0.2178
2025/06/09 11:18:08 - mmengine - INFO - The previous best checkpoint D:\OBJECT_DETECTION\sscma\ModelAssistant\work_dirs\rtmdet_nano_motherboard_final\best_coco_bbox_mAP_epoch_170.pth is removed
2025/06/09 11:18:08 - mmengine - INFO - The best checkpoint with 0.3470 coco/bbox_mAP at 180 epoch is saved to best_coco_bbox_mAP_epoch_180.pth.
2025/06/09 11:18:20 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:18:20 - mmengine - INFO - Epoch(train) [181][31/31]  base_lr: 4.5177e-04 lr: 4.5177e-04  eta: 0:24:14  time: 0.3084  data_time: 0.0588  memory: 4383  loss: 0.7971  loss_cls: 0.3605  loss_bbox: 0.4366
2025/06/09 11:18:31 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:18:31 - mmengine - INFO - Epoch(train) [182][31/31]  base_lr: 4.4873e-04 lr: 4.4873e-04  eta: 0:24:01  time: 0.3131  data_time: 0.0671  memory: 4561  loss: 0.7842  loss_cls: 0.3527  loss_bbox: 0.4316
2025/06/09 11:18:42 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:18:42 - mmengine - INFO - Epoch(train) [183][31/31]  base_lr: 4.4560e-04 lr: 4.4560e-04  eta: 0:23:48  time: 0.3038  data_time: 0.0558  memory: 4271  loss: 0.7837  loss_cls: 0.3550  loss_bbox: 0.4287
2025/06/09 11:18:52 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:18:52 - mmengine - INFO - Epoch(train) [184][31/31]  base_lr: 4.4239e-04 lr: 4.4239e-04  eta: 0:23:35  time: 0.3032  data_time: 0.0544  memory: 4495  loss: 0.7751  loss_cls: 0.3484  loss_bbox: 0.4267
2025/06/09 11:19:04 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:19:04 - mmengine - INFO - Epoch(train) [185][31/31]  base_lr: 4.3910e-04 lr: 4.3910e-04  eta: 0:23:22  time: 0.3095  data_time: 0.0626  memory: 4427  loss: 0.7878  loss_cls: 0.3529  loss_bbox: 0.4349
2025/06/09 11:19:15 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:19:15 - mmengine - INFO - Epoch(train) [186][31/31]  base_lr: 4.3574e-04 lr: 4.3574e-04  eta: 0:23:09  time: 0.3082  data_time: 0.0622  memory: 4226  loss: 0.7840  loss_cls: 0.3554  loss_bbox: 0.4285
2025/06/09 11:19:26 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:19:26 - mmengine - INFO - Epoch(train) [187][31/31]  base_lr: 4.3230e-04 lr: 4.3230e-04  eta: 0:22:57  time: 0.3196  data_time: 0.0737  memory: 4293  loss: 0.7862  loss_cls: 0.3586  loss_bbox: 0.4276
2025/06/09 11:19:38 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:19:38 - mmengine - INFO - Epoch(train) [188][31/31]  base_lr: 4.2878e-04 lr: 4.2878e-04  eta: 0:22:44  time: 0.3168  data_time: 0.0708  memory: 4450  loss: 0.7815  loss_cls: 0.3540  loss_bbox: 0.4275
2025/06/09 11:19:49 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:19:49 - mmengine - INFO - Epoch(train) [189][31/31]  base_lr: 4.2520e-04 lr: 4.2520e-04  eta: 0:22:31  time: 0.3107  data_time: 0.0637  memory: 4606  loss: 0.7818  loss_cls: 0.3541  loss_bbox: 0.4277
2025/06/09 11:20:00 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:20:00 - mmengine - INFO - Epoch(train) [190][31/31]  base_lr: 4.2154e-04 lr: 4.2154e-04  eta: 0:22:18  time: 0.3077  data_time: 0.0645  memory: 4651  loss: 0.7757  loss_cls: 0.3503  loss_bbox: 0.4255
2025/06/09 11:20:00 - mmengine - INFO - Saving checkpoint at 190 epochs
2025/06/09 11:20:06 - mmengine - INFO - Evaluating bbox...
2025/06/09 11:20:07 - mmengine - INFO - bbox_mAP_copypaste: 0.354 0.484 0.386 0.004 0.044 0.696
2025/06/09 11:20:07 - mmengine - INFO - Epoch(val) [190][20/20]    coco/bbox_mAP: 0.3540  coco/bbox_mAP_50: 0.4840  coco/bbox_mAP_75: 0.3860  coco/bbox_mAP_s: 0.0040  coco/bbox_mAP_m: 0.0440  coco/bbox_mAP_l: 0.6960  data_time: 0.0271  time: 0.2285
2025/06/09 11:20:07 - mmengine - INFO - The previous best checkpoint D:\OBJECT_DETECTION\sscma\ModelAssistant\work_dirs\rtmdet_nano_motherboard_final\best_coco_bbox_mAP_epoch_180.pth is removed
2025/06/09 11:20:08 - mmengine - INFO - The best checkpoint with 0.3540 coco/bbox_mAP at 190 epoch is saved to best_coco_bbox_mAP_epoch_190.pth.
2025/06/09 11:20:19 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:20:19 - mmengine - INFO - Epoch(train) [191][31/31]  base_lr: 4.1781e-04 lr: 4.1781e-04  eta: 0:22:06  time: 0.3137  data_time: 0.0685  memory: 4293  loss: 0.7757  loss_cls: 0.3517  loss_bbox: 0.4240
2025/06/09 11:20:31 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:20:31 - mmengine - INFO - Epoch(train) [192][31/31]  base_lr: 4.1401e-04 lr: 4.1401e-04  eta: 0:21:53  time: 0.3082  data_time: 0.0609  memory: 4427  loss: 0.7728  loss_cls: 0.3552  loss_bbox: 0.4176
2025/06/09 11:20:43 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:20:43 - mmengine - INFO - Epoch(train) [193][31/31]  base_lr: 4.1015e-04 lr: 4.1015e-04  eta: 0:21:41  time: 0.3341  data_time: 0.0929  memory: 4450  loss: 0.7754  loss_cls: 0.3545  loss_bbox: 0.4209
2025/06/09 11:20:51 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:20:54 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:20:54 - mmengine - INFO - Epoch(train) [194][31/31]  base_lr: 4.0622e-04 lr: 4.0622e-04  eta: 0:21:28  time: 0.3130  data_time: 0.0619  memory: 4427  loss: 0.7730  loss_cls: 0.3492  loss_bbox: 0.4238
2025/06/09 11:21:05 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:21:05 - mmengine - INFO - Epoch(train) [195][31/31]  base_lr: 4.0223e-04 lr: 4.0223e-04  eta: 0:21:15  time: 0.3030  data_time: 0.0568  memory: 4405  loss: 0.7666  loss_cls: 0.3488  loss_bbox: 0.4177
2025/06/09 11:21:17 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:21:17 - mmengine - INFO - Epoch(train) [196][31/31]  base_lr: 3.9818e-04 lr: 3.9818e-04  eta: 0:21:03  time: 0.3190  data_time: 0.0770  memory: 4293  loss: 0.7666  loss_cls: 0.3481  loss_bbox: 0.4185
2025/06/09 11:21:28 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:21:28 - mmengine - INFO - Epoch(train) [197][31/31]  base_lr: 3.9406e-04 lr: 3.9406e-04  eta: 0:20:50  time: 0.3153  data_time: 0.0719  memory: 4405  loss: 0.7759  loss_cls: 0.3550  loss_bbox: 0.4209
2025/06/09 11:21:39 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:21:39 - mmengine - INFO - Epoch(train) [198][31/31]  base_lr: 3.8989e-04 lr: 3.8989e-04  eta: 0:20:38  time: 0.3109  data_time: 0.0625  memory: 4361  loss: 0.7791  loss_cls: 0.3558  loss_bbox: 0.4233
2025/06/09 11:21:50 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:21:50 - mmengine - INFO - Epoch(train) [199][31/31]  base_lr: 3.8567e-04 lr: 3.8567e-04  eta: 0:20:25  time: 0.3087  data_time: 0.0660  memory: 4383  loss: 0.7779  loss_cls: 0.3537  loss_bbox: 0.4242
2025/06/09 11:22:02 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:22:02 - mmengine - INFO - Epoch(train) [200][31/31]  base_lr: 3.8139e-04 lr: 3.8139e-04  eta: 0:20:13  time: 0.3124  data_time: 0.0708  memory: 4361  loss: 0.7840  loss_cls: 0.3572  loss_bbox: 0.4268
2025/06/09 11:22:02 - mmengine - INFO - Saving checkpoint at 200 epochs
2025/06/09 11:22:09 - mmengine - INFO - Evaluating bbox...
2025/06/09 11:22:10 - mmengine - INFO - bbox_mAP_copypaste: 0.357 0.483 0.382 0.005 0.054 0.703
2025/06/09 11:22:10 - mmengine - INFO - Epoch(val) [200][20/20]    coco/bbox_mAP: 0.3570  coco/bbox_mAP_50: 0.4830  coco/bbox_mAP_75: 0.3820  coco/bbox_mAP_s: 0.0050  coco/bbox_mAP_m: 0.0540  coco/bbox_mAP_l: 0.7030  data_time: 0.0631  time: 0.2629
2025/06/09 11:22:10 - mmengine - INFO - The previous best checkpoint D:\OBJECT_DETECTION\sscma\ModelAssistant\work_dirs\rtmdet_nano_motherboard_final\best_coco_bbox_mAP_epoch_190.pth is removed
2025/06/09 11:22:10 - mmengine - INFO - The best checkpoint with 0.3570 coco/bbox_mAP at 200 epoch is saved to best_coco_bbox_mAP_epoch_200.pth.
2025/06/09 11:22:22 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:22:22 - mmengine - INFO - Epoch(train) [201][31/31]  base_lr: 3.7706e-04 lr: 3.7706e-04  eta: 0:20:00  time: 0.3146  data_time: 0.0686  memory: 4249  loss: 0.7747  loss_cls: 0.3537  loss_bbox: 0.4209
2025/06/09 11:22:33 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:22:33 - mmengine - INFO - Epoch(train) [202][31/31]  base_lr: 3.7267e-04 lr: 3.7267e-04  eta: 0:19:48  time: 0.3089  data_time: 0.0681  memory: 4249  loss: 0.7576  loss_cls: 0.3443  loss_bbox: 0.4133
2025/06/09 11:22:44 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:22:44 - mmengine - INFO - Epoch(train) [203][31/31]  base_lr: 3.6824e-04 lr: 3.6824e-04  eta: 0:19:35  time: 0.3057  data_time: 0.0627  memory: 4405  loss: 0.7640  loss_cls: 0.3511  loss_bbox: 0.4129
2025/06/09 11:22:56 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:22:56 - mmengine - INFO - Epoch(train) [204][31/31]  base_lr: 3.6377e-04 lr: 3.6377e-04  eta: 0:19:22  time: 0.3157  data_time: 0.0712  memory: 4539  loss: 0.7641  loss_cls: 0.3520  loss_bbox: 0.4121
2025/06/09 11:23:07 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:23:07 - mmengine - INFO - Epoch(train) [205][31/31]  base_lr: 3.5925e-04 lr: 3.5925e-04  eta: 0:19:10  time: 0.3136  data_time: 0.0720  memory: 4472  loss: 0.7541  loss_cls: 0.3430  loss_bbox: 0.4111
2025/06/09 11:23:19 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:23:19 - mmengine - INFO - Epoch(train) [206][31/31]  base_lr: 3.5468e-04 lr: 3.5468e-04  eta: 0:18:58  time: 0.3138  data_time: 0.0698  memory: 4517  loss: 0.7619  loss_cls: 0.3458  loss_bbox: 0.4161
2025/06/09 11:23:30 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:23:30 - mmengine - INFO - Epoch(train) [207][31/31]  base_lr: 3.5008e-04 lr: 3.5008e-04  eta: 0:18:45  time: 0.3083  data_time: 0.0609  memory: 4472  loss: 0.7401  loss_cls: 0.3336  loss_bbox: 0.4065
2025/06/09 11:23:41 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:23:41 - mmengine - INFO - Epoch(train) [208][31/31]  base_lr: 3.4544e-04 lr: 3.4544e-04  eta: 0:18:32  time: 0.3069  data_time: 0.0603  memory: 4517  loss: 0.7350  loss_cls: 0.3313  loss_bbox: 0.4037
2025/06/09 11:23:52 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:23:52 - mmengine - INFO - Epoch(train) [209][31/31]  base_lr: 3.4076e-04 lr: 3.4076e-04  eta: 0:18:20  time: 0.3079  data_time: 0.0643  memory: 4517  loss: 0.7512  loss_cls: 0.3414  loss_bbox: 0.4099
2025/06/09 11:24:03 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:24:03 - mmengine - INFO - Epoch(train) [210][31/31]  base_lr: 3.3604e-04 lr: 3.3604e-04  eta: 0:18:07  time: 0.3055  data_time: 0.0618  memory: 4383  loss: 0.7648  loss_cls: 0.3505  loss_bbox: 0.4143
2025/06/09 11:24:03 - mmengine - INFO - Saving checkpoint at 210 epochs
2025/06/09 11:24:09 - mmengine - INFO - Evaluating bbox...
2025/06/09 11:24:10 - mmengine - INFO - bbox_mAP_copypaste: 0.361 0.481 0.383 0.004 0.061 0.711
2025/06/09 11:24:10 - mmengine - INFO - Epoch(val) [210][20/20]    coco/bbox_mAP: 0.3610  coco/bbox_mAP_50: 0.4810  coco/bbox_mAP_75: 0.3830  coco/bbox_mAP_s: 0.0040  coco/bbox_mAP_m: 0.0610  coco/bbox_mAP_l: 0.7110  data_time: 0.0168  time: 0.2191
2025/06/09 11:24:10 - mmengine - INFO - The previous best checkpoint D:\OBJECT_DETECTION\sscma\ModelAssistant\work_dirs\rtmdet_nano_motherboard_final\best_coco_bbox_mAP_epoch_200.pth is removed
2025/06/09 11:24:10 - mmengine - INFO - The best checkpoint with 0.3610 coco/bbox_mAP at 210 epoch is saved to best_coco_bbox_mAP_epoch_210.pth.
2025/06/09 11:24:22 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:24:22 - mmengine - INFO - Epoch(train) [211][31/31]  base_lr: 3.3130e-04 lr: 3.3130e-04  eta: 0:17:55  time: 0.3115  data_time: 0.0636  memory: 4293  loss: 0.7538  loss_cls: 0.3457  loss_bbox: 0.4081
2025/06/09 11:24:34 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:24:34 - mmengine - INFO - Epoch(train) [212][31/31]  base_lr: 3.2652e-04 lr: 3.2652e-04  eta: 0:17:43  time: 0.3190  data_time: 0.0754  memory: 4383  loss: 0.7425  loss_cls: 0.3387  loss_bbox: 0.4039
2025/06/09 11:24:45 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:24:45 - mmengine - INFO - Epoch(train) [213][31/31]  base_lr: 3.2172e-04 lr: 3.2172e-04  eta: 0:17:30  time: 0.3119  data_time: 0.0609  memory: 4293  loss: 0.7414  loss_cls: 0.3371  loss_bbox: 0.4043
2025/06/09 11:24:56 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:24:56 - mmengine - INFO - Epoch(train) [214][31/31]  base_lr: 3.1689e-04 lr: 3.1689e-04  eta: 0:17:18  time: 0.3121  data_time: 0.0598  memory: 4450  loss: 0.7448  loss_cls: 0.3375  loss_bbox: 0.4073
2025/06/09 11:25:07 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:25:07 - mmengine - INFO - Epoch(train) [215][31/31]  base_lr: 3.1204e-04 lr: 3.1204e-04  eta: 0:17:05  time: 0.3159  data_time: 0.0653  memory: 4316  loss: 0.7427  loss_cls: 0.3374  loss_bbox: 0.4053
2025/06/09 11:25:19 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:25:19 - mmengine - INFO - Epoch(train) [216][31/31]  base_lr: 3.0716e-04 lr: 3.0716e-04  eta: 0:16:53  time: 0.3114  data_time: 0.0602  memory: 4338  loss: 0.7579  loss_cls: 0.3461  loss_bbox: 0.4118
2025/06/09 11:25:30 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:25:30 - mmengine - INFO - Epoch(train) [217][31/31]  base_lr: 3.0227e-04 lr: 3.0227e-04  eta: 0:16:40  time: 0.3127  data_time: 0.0660  memory: 4427  loss: 0.7521  loss_cls: 0.3419  loss_bbox: 0.4103
2025/06/09 11:25:41 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:25:41 - mmengine - INFO - Epoch(train) [218][31/31]  base_lr: 2.9735e-04 lr: 2.9735e-04  eta: 0:16:28  time: 0.3136  data_time: 0.0681  memory: 4271  loss: 0.7409  loss_cls: 0.3381  loss_bbox: 0.4028
2025/06/09 11:25:53 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:25:53 - mmengine - INFO - Epoch(train) [219][31/31]  base_lr: 2.9243e-04 lr: 2.9243e-04  eta: 0:16:16  time: 0.3135  data_time: 0.0706  memory: 4383  loss: 0.7396  loss_cls: 0.3377  loss_bbox: 0.4019
2025/06/09 11:26:04 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:26:04 - mmengine - INFO - Epoch(train) [220][31/31]  base_lr: 2.8749e-04 lr: 2.8749e-04  eta: 0:16:03  time: 0.3099  data_time: 0.0600  memory: 4606  loss: 0.7401  loss_cls: 0.3384  loss_bbox: 0.4017
2025/06/09 11:26:04 - mmengine - INFO - Saving checkpoint at 220 epochs
2025/06/09 11:26:10 - mmengine - INFO - Evaluating bbox...
2025/06/09 11:26:11 - mmengine - INFO - bbox_mAP_copypaste: 0.366 0.490 0.392 0.005 0.056 0.716
2025/06/09 11:26:11 - mmengine - INFO - Epoch(val) [220][20/20]    coco/bbox_mAP: 0.3660  coco/bbox_mAP_50: 0.4900  coco/bbox_mAP_75: 0.3920  coco/bbox_mAP_s: 0.0050  coco/bbox_mAP_m: 0.0560  coco/bbox_mAP_l: 0.7160  data_time: 0.0199  time: 0.2231
2025/06/09 11:26:11 - mmengine - INFO - The previous best checkpoint D:\OBJECT_DETECTION\sscma\ModelAssistant\work_dirs\rtmdet_nano_motherboard_final\best_coco_bbox_mAP_epoch_210.pth is removed
2025/06/09 11:26:12 - mmengine - INFO - The best checkpoint with 0.3660 coco/bbox_mAP at 220 epoch is saved to best_coco_bbox_mAP_epoch_220.pth.
2025/06/09 11:26:24 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:26:24 - mmengine - INFO - Epoch(train) [221][31/31]  base_lr: 2.8253e-04 lr: 2.8253e-04  eta: 0:15:51  time: 0.3170  data_time: 0.0638  memory: 4361  loss: 0.7322  loss_cls: 0.3366  loss_bbox: 0.3955
2025/06/09 11:26:35 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:26:35 - mmengine - INFO - Epoch(train) [222][31/31]  base_lr: 2.7757e-04 lr: 2.7757e-04  eta: 0:15:39  time: 0.3185  data_time: 0.0753  memory: 4271  loss: 0.7194  loss_cls: 0.3266  loss_bbox: 0.3928
2025/06/09 11:26:47 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:26:47 - mmengine - INFO - Epoch(train) [223][31/31]  base_lr: 2.7261e-04 lr: 2.7261e-04  eta: 0:15:27  time: 0.3217  data_time: 0.0793  memory: 4450  loss: 0.7411  loss_cls: 0.3371  loss_bbox: 0.4040
2025/06/09 11:26:59 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:26:59 - mmengine - INFO - Epoch(train) [224][31/31]  base_lr: 2.6763e-04 lr: 2.6763e-04  eta: 0:15:15  time: 0.3162  data_time: 0.0681  memory: 4606  loss: 0.7473  loss_cls: 0.3383  loss_bbox: 0.4089
2025/06/09 11:27:10 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:27:10 - mmengine - INFO - Epoch(train) [225][31/31]  base_lr: 2.6266e-04 lr: 2.6266e-04  eta: 0:15:02  time: 0.3151  data_time: 0.0632  memory: 4472  loss: 0.7396  loss_cls: 0.3341  loss_bbox: 0.4055
2025/06/09 11:27:20 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:27:21 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:27:21 - mmengine - INFO - Epoch(train) [226][31/31]  base_lr: 2.5769e-04 lr: 2.5769e-04  eta: 0:14:50  time: 0.3166  data_time: 0.0680  memory: 4383  loss: 0.7304  loss_cls: 0.3330  loss_bbox: 0.3974
2025/06/09 11:27:33 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:27:33 - mmengine - INFO - Epoch(train) [227][31/31]  base_lr: 2.5271e-04 lr: 2.5271e-04  eta: 0:14:38  time: 0.3124  data_time: 0.0627  memory: 4472  loss: 0.7276  loss_cls: 0.3314  loss_bbox: 0.3962
2025/06/09 11:27:44 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:27:44 - mmengine - INFO - Epoch(train) [228][31/31]  base_lr: 2.4775e-04 lr: 2.4775e-04  eta: 0:14:26  time: 0.3159  data_time: 0.0702  memory: 4293  loss: 0.7269  loss_cls: 0.3282  loss_bbox: 0.3987
2025/06/09 11:27:56 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:27:56 - mmengine - INFO - Epoch(train) [229][31/31]  base_lr: 2.4279e-04 lr: 2.4279e-04  eta: 0:14:13  time: 0.3185  data_time: 0.0703  memory: 4271  loss: 0.7320  loss_cls: 0.3340  loss_bbox: 0.3980
2025/06/09 11:28:07 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:28:07 - mmengine - INFO - Epoch(train) [230][31/31]  base_lr: 2.3783e-04 lr: 2.3783e-04  eta: 0:14:01  time: 0.3175  data_time: 0.0746  memory: 4383  loss: 0.7330  loss_cls: 0.3334  loss_bbox: 0.3996
2025/06/09 11:28:07 - mmengine - INFO - Saving checkpoint at 230 epochs
2025/06/09 11:28:13 - mmengine - INFO - Evaluating bbox...
2025/06/09 11:28:15 - mmengine - INFO - bbox_mAP_copypaste: 0.371 0.491 0.396 0.006 0.065 0.727
2025/06/09 11:28:15 - mmengine - INFO - Epoch(val) [230][20/20]    coco/bbox_mAP: 0.3710  coco/bbox_mAP_50: 0.4910  coco/bbox_mAP_75: 0.3960  coco/bbox_mAP_s: 0.0060  coco/bbox_mAP_m: 0.0650  coco/bbox_mAP_l: 0.7270  data_time: 0.0206  time: 0.2259
2025/06/09 11:28:15 - mmengine - INFO - The previous best checkpoint D:\OBJECT_DETECTION\sscma\ModelAssistant\work_dirs\rtmdet_nano_motherboard_final\best_coco_bbox_mAP_epoch_220.pth is removed
2025/06/09 11:28:15 - mmengine - INFO - The best checkpoint with 0.3710 coco/bbox_mAP at 230 epoch is saved to best_coco_bbox_mAP_epoch_230.pth.
2025/06/09 11:28:27 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:28:27 - mmengine - INFO - Epoch(train) [231][31/31]  base_lr: 2.3289e-04 lr: 2.3289e-04  eta: 0:13:49  time: 0.3187  data_time: 0.0739  memory: 4338  loss: 0.7172  loss_cls: 0.3268  loss_bbox: 0.3904
2025/06/09 11:28:39 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:28:39 - mmengine - INFO - Epoch(train) [232][31/31]  base_lr: 2.2796e-04 lr: 2.2796e-04  eta: 0:13:37  time: 0.3146  data_time: 0.0710  memory: 4293  loss: 0.7171  loss_cls: 0.3251  loss_bbox: 0.3920
2025/06/09 11:28:49 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:28:49 - mmengine - INFO - Epoch(train) [233][31/31]  base_lr: 2.2305e-04 lr: 2.2305e-04  eta: 0:13:24  time: 0.3070  data_time: 0.0539  memory: 4517  loss: 0.7125  loss_cls: 0.3216  loss_bbox: 0.3909
2025/06/09 11:29:01 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:29:01 - mmengine - INFO - Epoch(train) [234][31/31]  base_lr: 2.1815e-04 lr: 2.1815e-04  eta: 0:13:12  time: 0.3121  data_time: 0.0623  memory: 4338  loss: 0.7112  loss_cls: 0.3214  loss_bbox: 0.3898
2025/06/09 11:29:12 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:29:12 - mmengine - INFO - Epoch(train) [235][31/31]  base_lr: 2.1328e-04 lr: 2.1328e-04  eta: 0:13:00  time: 0.3116  data_time: 0.0676  memory: 4383  loss: 0.7293  loss_cls: 0.3308  loss_bbox: 0.3985
2025/06/09 11:29:23 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:29:23 - mmengine - INFO - Epoch(train) [236][31/31]  base_lr: 2.0842e-04 lr: 2.0842e-04  eta: 0:12:48  time: 0.3101  data_time: 0.0566  memory: 4338  loss: 0.7214  loss_cls: 0.3269  loss_bbox: 0.3946
2025/06/09 11:29:35 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:29:35 - mmengine - INFO - Epoch(train) [237][31/31]  base_lr: 2.0359e-04 lr: 2.0359e-04  eta: 0:12:36  time: 0.3197  data_time: 0.0700  memory: 4472  loss: 0.7209  loss_cls: 0.3284  loss_bbox: 0.3925
2025/06/09 11:29:46 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:29:46 - mmengine - INFO - Epoch(train) [238][31/31]  base_lr: 1.9879e-04 lr: 1.9879e-04  eta: 0:12:23  time: 0.3175  data_time: 0.0658  memory: 4338  loss: 0.7177  loss_cls: 0.3267  loss_bbox: 0.3910
2025/06/09 11:29:57 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:29:57 - mmengine - INFO - Epoch(train) [239][31/31]  base_lr: 1.9401e-04 lr: 1.9401e-04  eta: 0:12:11  time: 0.3125  data_time: 0.0637  memory: 4450  loss: 0.7147  loss_cls: 0.3249  loss_bbox: 0.3898
2025/06/09 11:30:09 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:30:09 - mmengine - INFO - Epoch(train) [240][31/31]  base_lr: 1.8926e-04 lr: 1.8926e-04  eta: 0:11:59  time: 0.3141  data_time: 0.0608  memory: 4472  loss: 0.7145  loss_cls: 0.3291  loss_bbox: 0.3854
2025/06/09 11:30:09 - mmengine - INFO - Saving checkpoint at 240 epochs
2025/06/09 11:30:15 - mmengine - INFO - Evaluating bbox...
2025/06/09 11:30:16 - mmengine - INFO - bbox_mAP_copypaste: 0.378 0.502 0.404 0.009 0.081 0.736
2025/06/09 11:30:16 - mmengine - INFO - Epoch(val) [240][20/20]    coco/bbox_mAP: 0.3780  coco/bbox_mAP_50: 0.5020  coco/bbox_mAP_75: 0.4040  coco/bbox_mAP_s: 0.0090  coco/bbox_mAP_m: 0.0810  coco/bbox_mAP_l: 0.7360  data_time: 0.0258  time: 0.2333
2025/06/09 11:30:16 - mmengine - INFO - The previous best checkpoint D:\OBJECT_DETECTION\sscma\ModelAssistant\work_dirs\rtmdet_nano_motherboard_final\best_coco_bbox_mAP_epoch_230.pth is removed
2025/06/09 11:30:17 - mmengine - INFO - The best checkpoint with 0.3780 coco/bbox_mAP at 240 epoch is saved to best_coco_bbox_mAP_epoch_240.pth.
2025/06/09 11:30:28 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:30:28 - mmengine - INFO - Epoch(train) [241][31/31]  base_lr: 1.8455e-04 lr: 1.8455e-04  eta: 0:11:47  time: 0.3076  data_time: 0.0558  memory: 4405  loss: 0.7201  loss_cls: 0.3317  loss_bbox: 0.3885
2025/06/09 11:30:39 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:30:39 - mmengine - INFO - Epoch(train) [242][31/31]  base_lr: 1.7986e-04 lr: 1.7986e-04  eta: 0:11:35  time: 0.3073  data_time: 0.0595  memory: 4561  loss: 0.7198  loss_cls: 0.3302  loss_bbox: 0.3896
2025/06/09 11:30:50 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:30:50 - mmengine - INFO - Epoch(train) [243][31/31]  base_lr: 1.7522e-04 lr: 1.7522e-04  eta: 0:11:23  time: 0.3092  data_time: 0.0616  memory: 4361  loss: 0.7131  loss_cls: 0.3212  loss_bbox: 0.3919
2025/06/09 11:31:02 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:31:02 - mmengine - INFO - Epoch(train) [244][31/31]  base_lr: 1.7061e-04 lr: 1.7061e-04  eta: 0:11:10  time: 0.3110  data_time: 0.0593  memory: 4293  loss: 0.7171  loss_cls: 0.3250  loss_bbox: 0.3921
2025/06/09 11:31:13 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:31:13 - mmengine - INFO - Epoch(train) [245][31/31]  base_lr: 1.6605e-04 lr: 1.6605e-04  eta: 0:10:58  time: 0.3143  data_time: 0.0641  memory: 4383  loss: 0.7190  loss_cls: 0.3249  loss_bbox: 0.3941
2025/06/09 11:31:25 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:31:25 - mmengine - INFO - Epoch(train) [246][31/31]  base_lr: 1.6152e-04 lr: 1.6152e-04  eta: 0:10:46  time: 0.3239  data_time: 0.0750  memory: 4383  loss: 0.7187  loss_cls: 0.3251  loss_bbox: 0.3936
2025/06/09 11:31:36 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:31:36 - mmengine - INFO - Epoch(train) [247][31/31]  base_lr: 1.5704e-04 lr: 1.5704e-04  eta: 0:10:34  time: 0.3217  data_time: 0.0664  memory: 4427  loss: 0.7113  loss_cls: 0.3240  loss_bbox: 0.3873
2025/06/09 11:31:47 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:31:47 - mmengine - INFO - Epoch(train) [248][31/31]  base_lr: 1.5261e-04 lr: 1.5261e-04  eta: 0:10:22  time: 0.3090  data_time: 0.0583  memory: 4517  loss: 0.7130  loss_cls: 0.3232  loss_bbox: 0.3898
2025/06/09 11:31:59 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:31:59 - mmengine - INFO - Epoch(train) [249][31/31]  base_lr: 1.4822e-04 lr: 1.4822e-04  eta: 0:10:10  time: 0.3139  data_time: 0.0658  memory: 4338  loss: 0.7103  loss_cls: 0.3204  loss_bbox: 0.3899
2025/06/09 11:32:09 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:32:09 - mmengine - INFO - Epoch(train) [250][31/31]  base_lr: 1.4389e-04 lr: 1.4389e-04  eta: 0:09:58  time: 0.3057  data_time: 0.0595  memory: 4338  loss: 0.7195  loss_cls: 0.3276  loss_bbox: 0.3920
2025/06/09 11:32:09 - mmengine - INFO - Saving checkpoint at 250 epochs
2025/06/09 11:32:16 - mmengine - INFO - Evaluating bbox...
2025/06/09 11:32:17 - mmengine - INFO - bbox_mAP_copypaste: 0.377 0.507 0.412 0.010 0.079 0.731
2025/06/09 11:32:18 - mmengine - INFO - Epoch(val) [250][20/20]    coco/bbox_mAP: 0.3770  coco/bbox_mAP_50: 0.5070  coco/bbox_mAP_75: 0.4120  coco/bbox_mAP_s: 0.0100  coco/bbox_mAP_m: 0.0790  coco/bbox_mAP_l: 0.7310  data_time: 0.0598  time: 0.2642
2025/06/09 11:32:28 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:32:28 - mmengine - INFO - Epoch(train) [251][31/31]  base_lr: 1.3961e-04 lr: 1.3961e-04  eta: 0:09:45  time: 0.3040  data_time: 0.0601  memory: 4383  loss: 0.7124  loss_cls: 0.3241  loss_bbox: 0.3883
2025/06/09 11:32:40 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:32:40 - mmengine - INFO - Epoch(train) [252][31/31]  base_lr: 1.3538e-04 lr: 1.3538e-04  eta: 0:09:33  time: 0.3121  data_time: 0.0638  memory: 4427  loss: 0.7191  loss_cls: 0.3239  loss_bbox: 0.3952
2025/06/09 11:32:51 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:32:51 - mmengine - INFO - Epoch(train) [253][31/31]  base_lr: 1.3120e-04 lr: 1.3120e-04  eta: 0:09:21  time: 0.3116  data_time: 0.0677  memory: 4271  loss: 0.7257  loss_cls: 0.3293  loss_bbox: 0.3964
2025/06/09 11:33:02 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:33:02 - mmengine - INFO - Epoch(train) [254][31/31]  base_lr: 1.2709e-04 lr: 1.2709e-04  eta: 0:09:09  time: 0.3081  data_time: 0.0689  memory: 4249  loss: 0.6986  loss_cls: 0.3169  loss_bbox: 0.3816
2025/06/09 11:33:13 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:33:13 - mmengine - INFO - Epoch(train) [255][31/31]  base_lr: 1.2303e-04 lr: 1.2303e-04  eta: 0:08:57  time: 0.3074  data_time: 0.0625  memory: 4316  loss: 0.6932  loss_cls: 0.3078  loss_bbox: 0.3854
2025/06/09 11:33:24 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:33:24 - mmengine - INFO - Epoch(train) [256][31/31]  base_lr: 1.1904e-04 lr: 1.1904e-04  eta: 0:08:45  time: 0.3097  data_time: 0.0570  memory: 4606  loss: 0.7052  loss_cls: 0.3116  loss_bbox: 0.3937
2025/06/09 11:33:36 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:33:36 - mmengine - INFO - Epoch(train) [257][31/31]  base_lr: 1.1510e-04 lr: 1.1510e-04  eta: 0:08:33  time: 0.3148  data_time: 0.0666  memory: 4316  loss: 0.6870  loss_cls: 0.3076  loss_bbox: 0.3793
2025/06/09 11:33:46 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:33:46 - mmengine - INFO - Epoch(train) [258][31/31]  base_lr: 1.1124e-04 lr: 1.1124e-04  eta: 0:08:21  time: 0.3037  data_time: 0.0581  memory: 4338  loss: 0.6926  loss_cls: 0.3118  loss_bbox: 0.3808
2025/06/09 11:33:50 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:33:58 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:33:58 - mmengine - INFO - Epoch(train) [259][31/31]  base_lr: 1.0743e-04 lr: 1.0743e-04  eta: 0:08:09  time: 0.3075  data_time: 0.0640  memory: 4383  loss: 0.7019  loss_cls: 0.3205  loss_bbox: 0.3814
2025/06/09 11:34:09 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:34:09 - mmengine - INFO - Epoch(train) [260][31/31]  base_lr: 1.0370e-04 lr: 1.0370e-04  eta: 0:07:57  time: 0.3194  data_time: 0.0598  memory: 4427  loss: 0.7068  loss_cls: 0.3236  loss_bbox: 0.3832
2025/06/09 11:34:09 - mmengine - INFO - Saving checkpoint at 260 epochs
2025/06/09 11:34:15 - mmengine - INFO - Evaluating bbox...
2025/06/09 11:34:16 - mmengine - INFO - bbox_mAP_copypaste: 0.387 0.516 0.415 0.010 0.087 0.750
2025/06/09 11:34:16 - mmengine - INFO - Epoch(val) [260][20/20]    coco/bbox_mAP: 0.3870  coco/bbox_mAP_50: 0.5160  coco/bbox_mAP_75: 0.4150  coco/bbox_mAP_s: 0.0100  coco/bbox_mAP_m: 0.0870  coco/bbox_mAP_l: 0.7500  data_time: 0.0177  time: 0.2229
2025/06/09 11:34:16 - mmengine - INFO - The previous best checkpoint D:\OBJECT_DETECTION\sscma\ModelAssistant\work_dirs\rtmdet_nano_motherboard_final\best_coco_bbox_mAP_epoch_240.pth is removed
2025/06/09 11:34:17 - mmengine - INFO - The best checkpoint with 0.3870 coco/bbox_mAP at 260 epoch is saved to best_coco_bbox_mAP_epoch_260.pth.
2025/06/09 11:34:29 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:34:29 - mmengine - INFO - Epoch(train) [261][31/31]  base_lr: 1.0004e-04 lr: 1.0004e-04  eta: 0:07:45  time: 0.3146  data_time: 0.0726  memory: 4517  loss: 0.6989  loss_cls: 0.3174  loss_bbox: 0.3814
2025/06/09 11:34:40 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:34:40 - mmengine - INFO - Epoch(train) [262][31/31]  base_lr: 9.6445e-05 lr: 9.6445e-05  eta: 0:07:33  time: 0.3132  data_time: 0.0682  memory: 4226  loss: 0.7003  loss_cls: 0.3190  loss_bbox: 0.3813
2025/06/09 11:34:51 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:34:51 - mmengine - INFO - Epoch(train) [263][31/31]  base_lr: 9.2925e-05 lr: 9.2925e-05  eta: 0:07:21  time: 0.3139  data_time: 0.0585  memory: 4293  loss: 0.6999  loss_cls: 0.3186  loss_bbox: 0.3812
2025/06/09 11:35:03 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:35:03 - mmengine - INFO - Epoch(train) [264][31/31]  base_lr: 8.9480e-05 lr: 8.9480e-05  eta: 0:07:09  time: 0.3195  data_time: 0.0756  memory: 4316  loss: 0.6938  loss_cls: 0.3143  loss_bbox: 0.3795
2025/06/09 11:35:15 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:35:15 - mmengine - INFO - Epoch(train) [265][31/31]  base_lr: 8.6111e-05 lr: 8.6111e-05  eta: 0:06:57  time: 0.3282  data_time: 0.0790  memory: 4338  loss: 0.6960  loss_cls: 0.3151  loss_bbox: 0.3809
2025/06/09 11:35:27 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:35:27 - mmengine - INFO - Epoch(train) [266][31/31]  base_lr: 8.2819e-05 lr: 8.2819e-05  eta: 0:06:45  time: 0.3177  data_time: 0.0699  memory: 4427  loss: 0.6981  loss_cls: 0.3179  loss_bbox: 0.3802
2025/06/09 11:35:38 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:35:38 - mmengine - INFO - Epoch(train) [267][31/31]  base_lr: 7.9605e-05 lr: 7.9605e-05  eta: 0:06:33  time: 0.3193  data_time: 0.0701  memory: 4405  loss: 0.6905  loss_cls: 0.3142  loss_bbox: 0.3762
2025/06/09 11:35:50 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:35:50 - mmengine - INFO - Epoch(train) [268][31/31]  base_lr: 7.6473e-05 lr: 7.6473e-05  eta: 0:06:21  time: 0.3144  data_time: 0.0646  memory: 4271  loss: 0.6904  loss_cls: 0.3166  loss_bbox: 0.3738
2025/06/09 11:36:01 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:36:01 - mmengine - INFO - Epoch(train) [269][31/31]  base_lr: 7.3421e-05 lr: 7.3421e-05  eta: 0:06:09  time: 0.3088  data_time: 0.0630  memory: 4361  loss: 0.6861  loss_cls: 0.3102  loss_bbox: 0.3759
2025/06/09 11:36:12 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:36:12 - mmengine - INFO - Epoch(train) [270][31/31]  base_lr: 7.0453e-05 lr: 7.0453e-05  eta: 0:05:57  time: 0.3074  data_time: 0.0573  memory: 4606  loss: 0.6905  loss_cls: 0.3120  loss_bbox: 0.3784
2025/06/09 11:36:12 - mmengine - INFO - Saving checkpoint at 270 epochs
2025/06/09 11:36:18 - mmengine - INFO - Evaluating bbox...
2025/06/09 11:36:19 - mmengine - INFO - bbox_mAP_copypaste: 0.389 0.523 0.419 0.014 0.080 0.746
2025/06/09 11:36:19 - mmengine - INFO - Epoch(val) [270][20/20]    coco/bbox_mAP: 0.3890  coco/bbox_mAP_50: 0.5230  coco/bbox_mAP_75: 0.4190  coco/bbox_mAP_s: 0.0140  coco/bbox_mAP_m: 0.0800  coco/bbox_mAP_l: 0.7460  data_time: 0.0171  time: 0.2232
2025/06/09 11:36:19 - mmengine - INFO - The previous best checkpoint D:\OBJECT_DETECTION\sscma\ModelAssistant\work_dirs\rtmdet_nano_motherboard_final\best_coco_bbox_mAP_epoch_260.pth is removed
2025/06/09 11:36:20 - mmengine - INFO - The best checkpoint with 0.3890 coco/bbox_mAP at 270 epoch is saved to best_coco_bbox_mAP_epoch_270.pth.
2025/06/09 11:36:32 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:36:32 - mmengine - INFO - Epoch(train) [271][31/31]  base_lr: 6.7569e-05 lr: 6.7569e-05  eta: 0:05:45  time: 0.3192  data_time: 0.0723  memory: 4695  loss: 0.6939  loss_cls: 0.3154  loss_bbox: 0.3785
2025/06/09 11:36:43 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:36:43 - mmengine - INFO - Epoch(train) [272][31/31]  base_lr: 6.4770e-05 lr: 6.4770e-05  eta: 0:05:33  time: 0.3101  data_time: 0.0622  memory: 4338  loss: 0.6880  loss_cls: 0.3117  loss_bbox: 0.3763
2025/06/09 11:36:54 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:36:54 - mmengine - INFO - Epoch(train) [273][31/31]  base_lr: 6.2058e-05 lr: 6.2058e-05  eta: 0:05:21  time: 0.3118  data_time: 0.0629  memory: 4472  loss: 0.6862  loss_cls: 0.3099  loss_bbox: 0.3763
2025/06/09 11:37:06 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:37:06 - mmengine - INFO - Epoch(train) [274][31/31]  base_lr: 5.9434e-05 lr: 5.9434e-05  eta: 0:05:09  time: 0.3168  data_time: 0.0713  memory: 4405  loss: 0.6911  loss_cls: 0.3134  loss_bbox: 0.3777
2025/06/09 11:37:17 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:37:17 - mmengine - INFO - Epoch(train) [275][31/31]  base_lr: 5.6899e-05 lr: 5.6899e-05  eta: 0:04:57  time: 0.3181  data_time: 0.0662  memory: 4427  loss: 0.7063  loss_cls: 0.3191  loss_bbox: 0.3872
2025/06/09 11:37:29 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:37:29 - mmengine - INFO - Epoch(train) [276][31/31]  base_lr: 5.4455e-05 lr: 5.4455e-05  eta: 0:04:45  time: 0.3177  data_time: 0.0668  memory: 4450  loss: 0.6939  loss_cls: 0.3130  loss_bbox: 0.3808
2025/06/09 11:37:40 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:37:40 - mmengine - INFO - Epoch(train) [277][31/31]  base_lr: 5.2101e-05 lr: 5.2101e-05  eta: 0:04:33  time: 0.3131  data_time: 0.0645  memory: 4383  loss: 0.6830  loss_cls: 0.3094  loss_bbox: 0.3736
2025/06/09 11:37:52 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:37:52 - mmengine - INFO - Epoch(train) [278][31/31]  base_lr: 4.9840e-05 lr: 4.9840e-05  eta: 0:04:21  time: 0.3167  data_time: 0.0710  memory: 4338  loss: 0.6811  loss_cls: 0.3065  loss_bbox: 0.3745
2025/06/09 11:38:03 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:38:03 - mmengine - INFO - Epoch(train) [279][31/31]  base_lr: 4.7672e-05 lr: 4.7672e-05  eta: 0:04:09  time: 0.3074  data_time: 0.0621  memory: 4249  loss: 0.6826  loss_cls: 0.3060  loss_bbox: 0.3766
2025/06/09 11:38:09 - mmengine - INFO - Evaluating bbox...
2025/06/09 11:38:11 - mmengine - INFO - bbox_mAP_copypaste: 0.387 0.510 0.419 0.011 0.074 0.747
2025/06/09 11:38:11 - mmengine - INFO - Epoch(val) [279][20/20]    coco/bbox_mAP: 0.3870  coco/bbox_mAP_50: 0.5100  coco/bbox_mAP_75: 0.4190  coco/bbox_mAP_s: 0.0110  coco/bbox_mAP_m: 0.0740  coco/bbox_mAP_l: 0.7470  data_time: 0.0709  time: 0.2760
2025/06/09 11:38:22 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:38:22 - mmengine - INFO - Epoch(train) [280][31/31]  base_lr: 4.5598e-05 lr: 4.5598e-05  eta: 0:03:57  time: 0.3159  data_time: 0.0700  memory: 4383  loss: 0.6860  loss_cls: 0.3079  loss_bbox: 0.3781
2025/06/09 11:38:22 - mmengine - INFO - Saving checkpoint at 280 epochs
2025/06/09 11:38:28 - mmengine - INFO - Evaluating bbox...
2025/06/09 11:38:30 - mmengine - INFO - bbox_mAP_copypaste: 0.386 0.508 0.418 0.011 0.071 0.748
2025/06/09 11:38:30 - mmengine - INFO - Epoch(val) [280][20/20]    coco/bbox_mAP: 0.3860  coco/bbox_mAP_50: 0.5080  coco/bbox_mAP_75: 0.4180  coco/bbox_mAP_s: 0.0110  coco/bbox_mAP_m: 0.0710  coco/bbox_mAP_l: 0.7480  data_time: 0.0181  time: 0.2319
2025/06/09 11:38:30 - mmengine - INFO - Switch pipeline now!
2025/06/09 11:39:49 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:39:49 - mmengine - INFO - Epoch(train) [281][31/31]  base_lr: 4.3620e-05 lr: 4.3620e-05  eta: 0:03:50  time: 1.6668  data_time: 1.4390  memory: 3981  loss: 0.8260  loss_cls: 0.4232  loss_bbox: 0.4029
2025/06/09 11:39:54 - mmengine - INFO - Evaluating bbox...
2025/06/09 11:39:56 - mmengine - INFO - bbox_mAP_copypaste: 0.389 0.524 0.417 0.013 0.080 0.747
2025/06/09 11:39:56 - mmengine - INFO - Epoch(val) [281][20/20]    coco/bbox_mAP: 0.3890  coco/bbox_mAP_50: 0.5240  coco/bbox_mAP_75: 0.4170  coco/bbox_mAP_s: 0.0130  coco/bbox_mAP_m: 0.0800  coco/bbox_mAP_l: 0.7470  data_time: 0.0184  time: 0.2215
2025/06/09 11:40:04 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:40:04 - mmengine - INFO - Epoch(train) [282][31/31]  base_lr: 4.1737e-05 lr: 4.1737e-05  eta: 0:03:38  time: 0.2560  data_time: 0.0378  memory: 4004  loss: 0.7557  loss_cls: 0.3808  loss_bbox: 0.3748
2025/06/09 11:40:10 - mmengine - INFO - Evaluating bbox...
2025/06/09 11:40:12 - mmengine - INFO - bbox_mAP_copypaste: 0.384 0.518 0.410 0.014 0.078 0.736
2025/06/09 11:40:12 - mmengine - INFO - Epoch(val) [282][20/20]    coco/bbox_mAP: 0.3840  coco/bbox_mAP_50: 0.5180  coco/bbox_mAP_75: 0.4100  coco/bbox_mAP_s: 0.0140  coco/bbox_mAP_m: 0.0780  coco/bbox_mAP_l: 0.7360  data_time: 0.0257  time: 0.2294
2025/06/09 11:40:20 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:40:20 - mmengine - INFO - Epoch(train) [283][31/31]  base_lr: 3.9952e-05 lr: 3.9952e-05  eta: 0:03:25  time: 0.2502  data_time: 0.0314  memory: 3981  loss: 0.6835  loss_cls: 0.3295  loss_bbox: 0.3540
2025/06/09 11:40:26 - mmengine - INFO - Evaluating bbox...
2025/06/09 11:40:27 - mmengine - INFO - bbox_mAP_copypaste: 0.378 0.508 0.398 0.014 0.069 0.726
2025/06/09 11:40:27 - mmengine - INFO - Epoch(val) [283][20/20]    coco/bbox_mAP: 0.3780  coco/bbox_mAP_50: 0.5080  coco/bbox_mAP_75: 0.3980  coco/bbox_mAP_s: 0.0140  coco/bbox_mAP_m: 0.0690  coco/bbox_mAP_l: 0.7260  data_time: 0.0193  time: 0.2204
2025/06/09 11:40:36 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:40:36 - mmengine - INFO - Epoch(train) [284][31/31]  base_lr: 3.8263e-05 lr: 3.8263e-05  eta: 0:03:13  time: 0.2548  data_time: 0.0358  memory: 4004  loss: 0.6603  loss_cls: 0.3184  loss_bbox: 0.3419
2025/06/09 11:40:41 - mmengine - INFO - Evaluating bbox...
2025/06/09 11:40:43 - mmengine - INFO - bbox_mAP_copypaste: 0.374 0.505 0.399 0.014 0.065 0.720
2025/06/09 11:40:43 - mmengine - INFO - Epoch(val) [284][20/20]    coco/bbox_mAP: 0.3740  coco/bbox_mAP_50: 0.5050  coco/bbox_mAP_75: 0.3990  coco/bbox_mAP_s: 0.0140  coco/bbox_mAP_m: 0.0650  coco/bbox_mAP_l: 0.7200  data_time: 0.0181  time: 0.2232
2025/06/09 11:40:51 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:40:51 - mmengine - INFO - Epoch(train) [285][31/31]  base_lr: 3.6674e-05 lr: 3.6674e-05  eta: 0:03:01  time: 0.2571  data_time: 0.0406  memory: 3981  loss: 0.6381  loss_cls: 0.3021  loss_bbox: 0.3360
2025/06/09 11:40:59 - mmengine - INFO - Evaluating bbox...
2025/06/09 11:41:00 - mmengine - INFO - bbox_mAP_copypaste: 0.372 0.507 0.398 0.014 0.068 0.715
2025/06/09 11:41:00 - mmengine - INFO - Epoch(val) [285][20/20]    coco/bbox_mAP: 0.3720  coco/bbox_mAP_50: 0.5070  coco/bbox_mAP_75: 0.3980  coco/bbox_mAP_s: 0.0140  coco/bbox_mAP_m: 0.0680  coco/bbox_mAP_l: 0.7150  data_time: 0.0958  time: 0.3101
2025/06/09 11:41:09 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:41:09 - mmengine - INFO - Epoch(train) [286][31/31]  base_lr: 3.5183e-05 lr: 3.5183e-05  eta: 0:02:49  time: 0.2597  data_time: 0.0410  memory: 3981  loss: 0.6374  loss_cls: 0.3039  loss_bbox: 0.3335
2025/06/09 11:41:15 - mmengine - INFO - Evaluating bbox...
2025/06/09 11:41:16 - mmengine - INFO - bbox_mAP_copypaste: 0.371 0.507 0.398 0.014 0.068 0.712
2025/06/09 11:41:16 - mmengine - INFO - Epoch(val) [286][20/20]    coco/bbox_mAP: 0.3710  coco/bbox_mAP_50: 0.5070  coco/bbox_mAP_75: 0.3980  coco/bbox_mAP_s: 0.0140  coco/bbox_mAP_m: 0.0680  coco/bbox_mAP_l: 0.7120  data_time: 0.0189  time: 0.2137
2025/06/09 11:41:25 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:41:25 - mmengine - INFO - Epoch(train) [287][31/31]  base_lr: 3.3792e-05 lr: 3.3792e-05  eta: 0:02:36  time: 0.2592  data_time: 0.0429  memory: 3981  loss: 0.6329  loss_cls: 0.2999  loss_bbox: 0.3330
2025/06/09 11:41:31 - mmengine - INFO - Evaluating bbox...
2025/06/09 11:41:32 - mmengine - INFO - bbox_mAP_copypaste: 0.371 0.508 0.397 0.015 0.062 0.714
2025/06/09 11:41:32 - mmengine - INFO - Epoch(val) [287][20/20]    coco/bbox_mAP: 0.3710  coco/bbox_mAP_50: 0.5080  coco/bbox_mAP_75: 0.3970  coco/bbox_mAP_s: 0.0150  coco/bbox_mAP_m: 0.0620  coco/bbox_mAP_l: 0.7140  data_time: 0.0559  time: 0.2519
2025/06/09 11:41:41 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:41:41 - mmengine - INFO - Epoch(train) [288][31/31]  base_lr: 3.2501e-05 lr: 3.2501e-05  eta: 0:02:24  time: 0.2515  data_time: 0.0337  memory: 3959  loss: 0.6171  loss_cls: 0.2903  loss_bbox: 0.3269
2025/06/09 11:41:47 - mmengine - INFO - Evaluating bbox...
2025/06/09 11:41:48 - mmengine - INFO - bbox_mAP_copypaste: 0.371 0.508 0.404 0.014 0.059 0.715
2025/06/09 11:41:48 - mmengine - INFO - Epoch(val) [288][20/20]    coco/bbox_mAP: 0.3710  coco/bbox_mAP_50: 0.5080  coco/bbox_mAP_75: 0.4040  coco/bbox_mAP_s: 0.0140  coco/bbox_mAP_m: 0.0590  coco/bbox_mAP_l: 0.7150  data_time: 0.0185  time: 0.2132
2025/06/09 11:41:57 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:41:57 - mmengine - INFO - Epoch(train) [289][31/31]  base_lr: 3.1312e-05 lr: 3.1312e-05  eta: 0:02:12  time: 0.2527  data_time: 0.0371  memory: 3981  loss: 0.6032  loss_cls: 0.2845  loss_bbox: 0.3187
2025/06/09 11:42:02 - mmengine - INFO - Evaluating bbox...
2025/06/09 11:42:03 - mmengine - INFO - bbox_mAP_copypaste: 0.369 0.508 0.398 0.013 0.060 0.714
2025/06/09 11:42:03 - mmengine - INFO - Epoch(val) [289][20/20]    coco/bbox_mAP: 0.3690  coco/bbox_mAP_50: 0.5080  coco/bbox_mAP_75: 0.3980  coco/bbox_mAP_s: 0.0130  coco/bbox_mAP_m: 0.0600  coco/bbox_mAP_l: 0.7140  data_time: 0.0179  time: 0.2128
2025/06/09 11:42:12 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:42:12 - mmengine - INFO - Epoch(train) [290][31/31]  base_lr: 3.0223e-05 lr: 3.0223e-05  eta: 0:02:00  time: 0.2562  data_time: 0.0330  memory: 3981  loss: 0.6160  loss_cls: 0.2922  loss_bbox: 0.3239
2025/06/09 11:42:12 - mmengine - INFO - Saving checkpoint at 290 epochs
2025/06/09 11:42:18 - mmengine - INFO - Evaluating bbox...
2025/06/09 11:42:19 - mmengine - INFO - bbox_mAP_copypaste: 0.370 0.512 0.401 0.015 0.064 0.714
2025/06/09 11:42:19 - mmengine - INFO - Epoch(val) [290][20/20]    coco/bbox_mAP: 0.3700  coco/bbox_mAP_50: 0.5120  coco/bbox_mAP_75: 0.4010  coco/bbox_mAP_s: 0.0150  coco/bbox_mAP_m: 0.0640  coco/bbox_mAP_l: 0.7140  data_time: 0.0244  time: 0.2198
2025/06/09 11:42:24 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:42:28 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:42:28 - mmengine - INFO - Epoch(train) [291][31/31]  base_lr: 2.9237e-05 lr: 2.9237e-05  eta: 0:01:48  time: 0.2627  data_time: 0.0403  memory: 3981  loss: 0.6109  loss_cls: 0.2896  loss_bbox: 0.3213
2025/06/09 11:42:34 - mmengine - INFO - Evaluating bbox...
2025/06/09 11:42:35 - mmengine - INFO - bbox_mAP_copypaste: 0.371 0.511 0.408 0.015 0.066 0.716
2025/06/09 11:42:35 - mmengine - INFO - Epoch(val) [291][20/20]    coco/bbox_mAP: 0.3710  coco/bbox_mAP_50: 0.5110  coco/bbox_mAP_75: 0.4080  coco/bbox_mAP_s: 0.0150  coco/bbox_mAP_m: 0.0660  coco/bbox_mAP_l: 0.7160  data_time: 0.0174  time: 0.2126
2025/06/09 11:42:44 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:42:44 - mmengine - INFO - Epoch(train) [292][31/31]  base_lr: 2.8353e-05 lr: 2.8353e-05  eta: 0:01:36  time: 0.2572  data_time: 0.0376  memory: 3981  loss: 0.6034  loss_cls: 0.2866  loss_bbox: 0.3169
2025/06/09 11:42:49 - mmengine - INFO - Evaluating bbox...
2025/06/09 11:42:51 - mmengine - INFO - bbox_mAP_copypaste: 0.368 0.513 0.405 0.015 0.064 0.710
2025/06/09 11:42:51 - mmengine - INFO - Epoch(val) [292][20/20]    coco/bbox_mAP: 0.3680  coco/bbox_mAP_50: 0.5130  coco/bbox_mAP_75: 0.4050  coco/bbox_mAP_s: 0.0150  coco/bbox_mAP_m: 0.0640  coco/bbox_mAP_l: 0.7100  data_time: 0.0187  time: 0.2116
2025/06/09 11:42:59 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:42:59 - mmengine - INFO - Epoch(train) [293][31/31]  base_lr: 2.7571e-05 lr: 2.7571e-05  eta: 0:01:23  time: 0.2586  data_time: 0.0431  memory: 3981  loss: 0.6012  loss_cls: 0.2822  loss_bbox: 0.3190
2025/06/09 11:43:07 - mmengine - INFO - Evaluating bbox...
2025/06/09 11:43:08 - mmengine - INFO - bbox_mAP_copypaste: 0.369 0.513 0.405 0.013 0.065 0.711
2025/06/09 11:43:08 - mmengine - INFO - Epoch(val) [293][20/20]    coco/bbox_mAP: 0.3690  coco/bbox_mAP_50: 0.5130  coco/bbox_mAP_75: 0.4050  coco/bbox_mAP_s: 0.0130  coco/bbox_mAP_m: 0.0650  coco/bbox_mAP_l: 0.7110  data_time: 0.0978  time: 0.2899
2025/06/09 11:43:17 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:43:17 - mmengine - INFO - Epoch(train) [294][31/31]  base_lr: 2.6893e-05 lr: 2.6893e-05  eta: 0:01:11  time: 0.2556  data_time: 0.0357  memory: 4004  loss: 0.5962  loss_cls: 0.2795  loss_bbox: 0.3167
2025/06/09 11:43:22 - mmengine - INFO - Evaluating bbox...
2025/06/09 11:43:23 - mmengine - INFO - bbox_mAP_copypaste: 0.368 0.511 0.403 0.016 0.061 0.708
2025/06/09 11:43:23 - mmengine - INFO - Epoch(val) [294][20/20]    coco/bbox_mAP: 0.3680  coco/bbox_mAP_50: 0.5110  coco/bbox_mAP_75: 0.4030  coco/bbox_mAP_s: 0.0160  coco/bbox_mAP_m: 0.0610  coco/bbox_mAP_l: 0.7080  data_time: 0.0194  time: 0.2096
2025/06/09 11:43:32 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:43:32 - mmengine - INFO - Epoch(train) [295][31/31]  base_lr: 2.6318e-05 lr: 2.6318e-05  eta: 0:00:59  time: 0.2579  data_time: 0.0363  memory: 3981  loss: 0.6123  loss_cls: 0.2870  loss_bbox: 0.3254
2025/06/09 11:43:38 - mmengine - INFO - Evaluating bbox...
2025/06/09 11:43:40 - mmengine - INFO - bbox_mAP_copypaste: 0.369 0.513 0.402 0.015 0.061 0.711
2025/06/09 11:43:40 - mmengine - INFO - Epoch(val) [295][20/20]    coco/bbox_mAP: 0.3690  coco/bbox_mAP_50: 0.5130  coco/bbox_mAP_75: 0.4020  coco/bbox_mAP_s: 0.0150  coco/bbox_mAP_m: 0.0610  coco/bbox_mAP_l: 0.7110  data_time: 0.0618  time: 0.2481
2025/06/09 11:43:48 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:43:48 - mmengine - INFO - Epoch(train) [296][31/31]  base_lr: 2.5846e-05 lr: 2.5846e-05  eta: 0:00:47  time: 0.2553  data_time: 0.0378  memory: 3981  loss: 0.5983  loss_cls: 0.2818  loss_bbox: 0.3165
2025/06/09 11:43:54 - mmengine - INFO - Evaluating bbox...
2025/06/09 11:43:55 - mmengine - INFO - bbox_mAP_copypaste: 0.369 0.511 0.412 0.014 0.066 0.711
2025/06/09 11:43:55 - mmengine - INFO - Epoch(val) [296][20/20]    coco/bbox_mAP: 0.3690  coco/bbox_mAP_50: 0.5110  coco/bbox_mAP_75: 0.4120  coco/bbox_mAP_s: 0.0140  coco/bbox_mAP_m: 0.0660  coco/bbox_mAP_l: 0.7110  data_time: 0.0172  time: 0.2032
2025/06/09 11:44:04 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:44:04 - mmengine - INFO - Epoch(train) [297][31/31]  base_lr: 2.5479e-05 lr: 2.5479e-05  eta: 0:00:35  time: 0.2556  data_time: 0.0367  memory: 4004  loss: 0.5895  loss_cls: 0.2774  loss_bbox: 0.3121
2025/06/09 11:44:09 - mmengine - INFO - Evaluating bbox...
2025/06/09 11:44:10 - mmengine - INFO - bbox_mAP_copypaste: 0.366 0.510 0.407 0.014 0.066 0.706
2025/06/09 11:44:10 - mmengine - INFO - Epoch(val) [297][20/20]    coco/bbox_mAP: 0.3660  coco/bbox_mAP_50: 0.5100  coco/bbox_mAP_75: 0.4070  coco/bbox_mAP_s: 0.0140  coco/bbox_mAP_m: 0.0660  coco/bbox_mAP_l: 0.7060  data_time: 0.0168  time: 0.2036
2025/06/09 11:44:19 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:44:19 - mmengine - INFO - Epoch(train) [298][31/31]  base_lr: 2.5215e-05 lr: 2.5215e-05  eta: 0:00:23  time: 0.2594  data_time: 0.0413  memory: 3981  loss: 0.5841  loss_cls: 0.2758  loss_bbox: 0.3083
2025/06/09 11:44:25 - mmengine - INFO - Evaluating bbox...
2025/06/09 11:44:26 - mmengine - INFO - bbox_mAP_copypaste: 0.369 0.505 0.406 0.014 0.067 0.710
2025/06/09 11:44:26 - mmengine - INFO - Epoch(val) [298][20/20]    coco/bbox_mAP: 0.3690  coco/bbox_mAP_50: 0.5050  coco/bbox_mAP_75: 0.4060  coco/bbox_mAP_s: 0.0140  coco/bbox_mAP_m: 0.0670  coco/bbox_mAP_l: 0.7100  data_time: 0.0260  time: 0.2139
2025/06/09 11:44:35 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:44:35 - mmengine - INFO - Epoch(train) [299][31/31]  base_lr: 2.5056e-05 lr: 2.5056e-05  eta: 0:00:11  time: 0.2555  data_time: 0.0365  memory: 3981  loss: 0.5774  loss_cls: 0.2680  loss_bbox: 0.3093
2025/06/09 11:44:40 - mmengine - INFO - Evaluating bbox...
2025/06/09 11:44:41 - mmengine - INFO - bbox_mAP_copypaste: 0.367 0.506 0.404 0.014 0.066 0.706
2025/06/09 11:44:41 - mmengine - INFO - Epoch(val) [299][20/20]    coco/bbox_mAP: 0.3670  coco/bbox_mAP_50: 0.5060  coco/bbox_mAP_75: 0.4040  coco/bbox_mAP_s: 0.0140  coco/bbox_mAP_m: 0.0660  coco/bbox_mAP_l: 0.7060  data_time: 0.0174  time: 0.2064
2025/06/09 11:44:50 - mmengine - INFO - Exp name: rtmdet_nano_motherboard_final_20250609_103806
2025/06/09 11:44:50 - mmengine - INFO - Epoch(train) [300][31/31]  base_lr: 2.5000e-05 lr: 2.5000e-05  eta: 0:00:00  time: 0.2562  data_time: 0.0386  memory: 4004  loss: 0.5727  loss_cls: 0.2670  loss_bbox: 0.3057
2025/06/09 11:44:50 - mmengine - INFO - Saving checkpoint at 300 epochs
2025/06/09 11:44:57 - mmengine - INFO - Evaluating bbox...
2025/06/09 11:44:58 - mmengine - INFO - bbox_mAP_copypaste: 0.367 0.506 0.395 0.014 0.070 0.704
2025/06/09 11:44:58 - mmengine - INFO - Epoch(val) [300][20/20]    coco/bbox_mAP: 0.3670  coco/bbox_mAP_50: 0.5060  coco/bbox_mAP_75: 0.3950  coco/bbox_mAP_s: 0.0140  coco/bbox_mAP_m: 0.0700  coco/bbox_mAP_l: 0.7040  data_time: 0.0683  time: 0.2563
