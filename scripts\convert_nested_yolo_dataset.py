#!/usr/bin/env python3
"""
Convert Nested YOLO Dataset to COCO Format
Handles YOLO datasets with structure:
- images/train/
- images/val/
- labels/train/
- labels/val/
"""

import os
import sys
import argparse
from pathlib import Path

# Add scripts directory to path for imports
sys.path.append(str(Path(__file__).parent))

from yolo_to_coco_converter import YOLOtoCOCOConverter


def convert_nested_yolo_to_coco(yolo_path: Path, output_path: Path, dataset_name: str):
    """Convert nested YOLO dataset to COCO format"""
    
    print(f"Converting nested YOLO dataset: {yolo_path}")
    print(f"Output COCO dataset: {output_path}")
    print(f"Dataset name: {dataset_name}")
    
    # Create output directory
    output_path.mkdir(parents=True, exist_ok=True)
    
    # Check for train and val directories
    train_images_dir = yolo_path / "images" / "train"
    val_images_dir = yolo_path / "images" / "val"
    train_labels_dir = yolo_path / "labels" / "train"
    val_labels_dir = yolo_path / "labels" / "val"
    
    if not train_images_dir.exists():
        raise FileNotFoundError(f"Train images directory not found: {train_images_dir}")
    
    if not val_images_dir.exists():
        raise FileNotFoundError(f"Validation images directory not found: {val_images_dir}")
    
    # Convert train split
    print("\n=== Converting Train Split ===")
    train_converter = YOLOtoCOCOConverter(
        str(yolo_path), 
        str(output_path), 
        dataset_name
    )
    
    # Temporarily modify the converter to use nested structure
    train_converter.yolo_path = yolo_path
    train_output = convert_split(train_converter, train_images_dir, train_labels_dir, "train", output_path)
    
    # Convert validation split
    print("\n=== Converting Validation Split ===")
    val_converter = YOLOtoCOCOConverter(
        str(yolo_path), 
        str(output_path), 
        dataset_name
    )
    
    val_converter.yolo_path = yolo_path
    val_output = convert_split(val_converter, val_images_dir, val_labels_dir, "valid", output_path)
    
    # Copy classes.txt to output directory
    classes_file = yolo_path / "classes.txt"
    if classes_file.exists():
        import shutil
        shutil.copy2(classes_file, output_path / "classes.txt")
        print(f"\nClasses file copied to: {output_path / 'classes.txt'}")
    
    print(f"\n=== Conversion Complete ===")
    print(f"Train dataset: {train_output}")
    print(f"Valid dataset: {val_output}")
    
    return train_output, val_output


def convert_split(converter, images_dir: Path, labels_dir: Path, split_name: str, output_path: Path):
    """Convert a single split to COCO format"""
    
    # Load classes
    classes = converter.load_classes()
    converter.setup_categories(classes)
    
    # Get all image files
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
    image_files = []
    for ext in image_extensions:
        image_files.extend(images_dir.glob(f"*{ext}"))
        image_files.extend(images_dir.glob(f"*{ext.upper()}"))
    
    print(f"Found {len(image_files)} images in {split_name} split")
    
    # Create output directory for this split
    output_split_dir = output_path / split_name
    output_split_dir.mkdir(parents=True, exist_ok=True)
    
    # Process each image
    processed_count = 0
    for image_path in image_files:
        # Find corresponding label file
        label_path = labels_dir / f"{image_path.stem}.txt"
        
        # Copy image to output directory
        import shutil
        output_image_path = output_split_dir / image_path.name
        shutil.copy2(image_path, output_image_path)
        
        # Process annotations
        if converter.process_image_and_annotations(image_path, label_path):
            processed_count += 1
    
    # Save COCO annotations
    annotations_file = output_split_dir / "_annotations.coco.json"
    with open(annotations_file, 'w') as f:
        import json
        json.dump(converter.coco_format, f, indent=2)
    
    print(f"Conversion complete for {split_name}!")
    print(f"Processed {processed_count} images")
    print(f"Generated {len(converter.coco_format['annotations'])} annotations")
    print(f"Output saved to: {output_split_dir}")
    
    return output_split_dir


def main():
    parser = argparse.ArgumentParser(description="Convert nested YOLO dataset to COCO format")
    parser.add_argument("--input", "-i", required=True, help="Path to nested YOLO dataset directory")
    parser.add_argument("--output", "-o", required=True, help="Output directory for COCO dataset")
    parser.add_argument("--name", "-n", default="motherboard_led_detection", help="Dataset name")
    
    args = parser.parse_args()
    
    yolo_path = Path(args.input)
    output_path = Path(args.output)
    
    if not yolo_path.exists():
        raise FileNotFoundError(f"Input directory not found: {yolo_path}")
    
    # Convert the dataset
    convert_nested_yolo_to_coco(yolo_path, output_path, args.name)
    
    print(f"\nDataset structure:")
    print(f"{output_path}/")
    print(f"├── train/")
    print(f"│   ├── *.jpg (images)")
    print(f"│   └── _annotations.coco.json")
    print(f"└── valid/")
    print(f"    ├── *.jpg (images)")
    print(f"    └── _annotations.coco.json")


if __name__ == "__main__":
    main()
