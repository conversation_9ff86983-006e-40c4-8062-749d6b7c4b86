2025/06/09 12:34:58 - mmengine - INFO - 
------------------------------------------------------------
System environment:
    sys.platform: win32
    Python: 3.12.11 | packaged by Anaconda, Inc. | (main, Jun  5 2025, 12:58:53) [MSC v.1929 64 bit (AMD64)]
    CUDA available: True
    MUSA available: False
    numpy_random_seed: 1428344006
    GPU 0: NVIDIA GeForce RTX 5060 Ti
    CUDA_HOME: None
    MSVC: Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35209 for x64
    GCC: n/a
    PyTorch: 2.8.0.dev20250605+cu128
    PyTorch compiling details: PyTorch built with:
  - C++ Version: 201703
  - MSVC 193833144
  - Intel(R) oneAPI Math Kernel Library Version 2025.1-Product Build 20250306 for Intel(R) 64 architecture applications
  - Intel(R) MKL-DNN v3.7.1 (Git Hash 8d263e693366ef8db40acc569cc7d8edf644556d)
  - OpenMP 2019
  - LAPACK is enabled (usually provided by MKL)
  - CPU capability usage: AVX512
  - CUDA Runtime 12.8
  - NVCC architecture flags: -gencode;arch=compute_61,code=sm_61;-gencode;arch=compute_70,code=sm_70;-gencode;arch=compute_75,code=sm_75;-gencode;arch=compute_80,code=sm_80;-gencode;arch=compute_86,code=sm_86;-gencode;arch=compute_90,code=sm_90;-gencode;arch=compute_100,code=sm_100;-gencode;arch=compute_120,code=sm_120
  - CuDNN 90.7.1
  - Magma 2.5.4
  - Build settings: BLAS_INFO=mkl, BUILD_TYPE=Release, COMMIT_SHA=d53869579d491ac5c9b8a60419c015062f904a3e, CUDA_VERSION=12.8, CUDNN_VERSION=9.7.1, CXX_COMPILER=C:/actions-runner/_work/pytorch/pytorch/pytorch/.ci/pytorch/windows/tmp_bin/sccache-cl.exe, CXX_FLAGS=/DWIN32 /D_WINDOWS /GR /EHsc /Zc:__cplusplus /bigobj /FS /utf-8 -DUSE_PTHREADPOOL -DNDEBUG -DUSE_KINETO -DLIBKINETO_NOROCTRACER -DLIBKINETO_NOXPUPTI=ON -DUSE_FBGEMM -DUSE_XNNPACK -DSYMBOLICATE_MOBILE_DEBUG_HANDLE /wd4624 /wd4068 /wd4067 /wd4267 /wd4661 /wd4717 /wd4244 /wd4804 /wd4273, LAPACK_INFO=mkl, PERF_WITH_AVX=1, PERF_WITH_AVX2=1, TORCH_VERSION=2.8.0, USE_CUDA=ON, USE_CUDNN=ON, USE_CUSPARSELT=OFF, USE_GFLAGS=OFF, USE_GLOG=OFF, USE_GLOO=ON, USE_MKL=ON, USE_MKLDNN=ON, USE_MPI=OFF, USE_NCCL=OFF, USE_NNPACK=OFF, USE_OPENMP=ON, USE_ROCM=OFF, USE_ROCM_KERNEL_ASSERT=OFF, USE_XCCL=OFF, USE_XPU=OFF, 

    TorchVision: 0.23.0.dev20250606+cu128
    OpenCV: 4.11.0
    MMEngine: 0.10.4

Runtime environment:
    cudnn_benchmark: True
    mp_cfg: {'mp_start_method': 'fork', 'opencv_num_threads': 0}
    dist_cfg: {'backend': 'nccl'}
    seed: 1428344006
    deterministic: False
    Distributed launcher: none
    Distributed training: False
    GPU number: 1
------------------------------------------------------------

2025/06/09 12:34:58 - mmengine - INFO - Config:
auto_scale_lr = dict(base_batch_size=32, enable=True)
backend_args = None
base_lr = 0.0005
batch_shapes_cfg = dict(
    batch_size=32,
    extra_pad_ratio=0.5,
    img_size=192,
    size_divisor=32,
    type='sscma.datasets.BatchShapePolicy')
batch_size = 32
checkpoint = None
custom_hooks = [
    dict(
        ema_type='sscma.models.ExpMomentumEMA',
        momentum=0.0002,
        priority=49,
        type='mmengine.hooks.EMAHook',
        update_buffers=True),
    dict(
        switch_epoch=280,
        switch_pipeline=[
            dict(
                backend_args=None,
                imdecode_backend='cv2',
                type='sscma.datasets.transforms.LoadImageFromFile'),
            dict(
                imdecode_backend='cv2',
                type='sscma.datasets.transforms.LoadAnnotations',
                with_bbox=True),
            dict(
                keep_ratio=True,
                ratio_range=(
                    0.8,
                    1.2,
                ),
                resize_type='sscma.datasets.transforms.Resize',
                scale=(
                    384,
                    384,
                ),
                type='sscma.datasets.transforms.RandomResize'),
            dict(
                crop_size=(
                    192,
                    192,
                ),
                type='sscma.datasets.transforms.RandomCrop'),
            dict(type='sscma.datasets.transforms.HSVRandomAug'),
            dict(prob=0.5, type='sscma.datasets.transforms.RandomFlip'),
            dict(
                pad_val=dict(img=(
                    114,
                    114,
                    114,
                )),
                size=(
                    192,
                    192,
                ),
                type='sscma.datasets.transforms.Pad'),
            dict(type='sscma.datasets.transforms.PackDetInputs'),
        ],
        type='sscma.engine.PipelineSwitchHook'),
]
d_factor = 0.33
data_root = '../datasets/motherboard_led_detection_coco/'
dataset_type = 'sscma.datasets.CustomFomoCocoDataset'
default_hooks = dict(
    checkpoint=dict(
        interval=10,
        max_keep_ckpts=3,
        save_best='auto',
        type='mmengine.hooks.CheckpointHook'),
    logger=dict(interval=100, type='mmengine.hooks.LoggerHook'),
    param_scheduler=dict(type='mmengine.hooks.ParamSchedulerHook'),
    sampler_seed=dict(type='mmengine.hooks.DistSamplerSeedHook'),
    timer=dict(type='mmengine.hooks.IterTimerHook'),
    visualization=dict(
        draw=True,
        test_out_dir='works',
        type='sscma.engine.DetVisualizationHook'))
default_scope = None
deploy = dict(
    data_preprocessor=dict(
        batch_augments=None,
        bgr_to_rgb=False,
        mean=[
            0,
            0,
            0,
        ],
        std=[
            255,
            255,
            255,
        ],
        type='sscma.datasets.DetDataPreprocessor'),
    type='sscma.deploy.models.RTMDetInfer')
dump_config = True
env_cfg = dict(
    cudnn_benchmark=True,
    dist_cfg=dict(backend='nccl'),
    mp_cfg=dict(mp_start_method='fork', opencv_num_threads=0))
epochs = 300
imdecode_backend = 'cv2'
imgsz = (
    192,
    192,
)
interval = 10
launcher = 'none'
load_from = None
log_level = 'INFO'
log_processor = dict(
    by_epoch=True, type='mmengine.runner.LogProcessor', window_size=50)
mixup_max_cached_images = 10
model = dict(
    backbone=dict(
        act_cfg=dict(inplace=True, type='torch.nn.ReLU6'),
        arch='P5',
        channel_attention=False,
        deepen_factor=0.33,
        expand_ratio=0.5,
        init_cfg=dict(layer='Conv2d', type='Kaiming'),
        norm_cfg=dict(type='torch.nn.BatchNorm2d'),
        split_max_pool_kernel=False,
        type='sscma.models.CSPNeXt',
        widen_factor=0.25),
    bbox_head=dict(
        bbox_coder=dict(type='sscma.models.DistancePointBBoxCoder'),
        head_module=dict(
            act_cfg=dict(inplace=True, type='torch.nn.ReLU6'),
            feat_channels=256,
            featmap_strides=[
                8,
                16,
                32,
            ],
            in_channels=256,
            norm_cfg=dict(type='torch.nn.BatchNorm2d'),
            num_classes=2,
            pred_kernel_size=1,
            share_conv=False,
            stacked_convs=2,
            type='sscma.models.RTMDetSepBNHeadModule',
            widen_factor=0.25),
        loss_bbox=dict(loss_weight=2.0, type='sscma.models.GIoULoss'),
        loss_cls=dict(
            beta=2.0,
            loss_weight=1.0,
            type='sscma.models.QualityFocalLoss',
            use_sigmoid=True),
        prior_generator=dict(
            offset=0,
            strides=[
                8,
                16,
                32,
            ],
            type='sscma.models.MlvlPointGenerator'),
        test_cfg=dict(
            max_per_img=300,
            min_bbox_size=0,
            multi_label=True,
            nms=dict(iou_threshold=0.65, type='torchvision.ops.nms'),
            nms_pre=30000,
            score_thr=0.001),
        train_cfg=dict(
            allowed_border=-1,
            assigner=dict(
                iou_calculator=dict(type='sscma.models.BboxOverlaps2D'),
                num_classes=2,
                topk=13,
                type='sscma.models.BatchDynamicSoftLabelAssigner'),
            debug=False,
            pos_weight=-1),
        type='sscma.models.RTMDetHead'),
    data_preprocessor=dict(
        batch_augments=None,
        bgr_to_rgb=False,
        mean=[
            0,
            0,
            0,
        ],
        std=[
            255,
            255,
            255,
        ],
        type='sscma.datasets.DetDataPreprocessor'),
    neck=dict(
        act_cfg=dict(inplace=True, type='torch.nn.ReLU6'),
        deepen_factor=0.33,
        expand_ratio=0.5,
        in_channels=[
            256,
            512,
            1024,
        ],
        norm_cfg=dict(type='torch.nn.BatchNorm2d'),
        num_csp_blocks=3,
        out_channels=256,
        type='sscma.models.CSPNeXtPAFPN',
        widen_factor=0.25),
    test_cfg=dict(
        max_per_img=300,
        min_bbox_size=0,
        multi_label=True,
        nms=dict(iou_threshold=0.65, type='torchvision.ops.nms'),
        nms_pre=30000,
        score_thr=0.001),
    train_cfg=dict(
        allowed_border=-1,
        assigner=dict(
            iou_calculator=dict(type='sscma.models.BboxOverlaps2D'),
            num_classes=2,
            topk=13,
            type='sscma.models.BatchDynamicSoftLabelAssigner'),
        debug=False,
        pos_weight=-1),
    type='sscma.models.RTMDet')
mosaic_max_cached_images = 20
num_classes = 2
num_workers = 16
optim_wrapper = dict(
    optimizer=dict(
        lr=0.0004, type='torch.optim.adamw.AdamW', weight_decay=0.05),
    paramwise_cfg=dict(
        bias_decay_mult=0, bypass_duplicate=True, norm_decay_mult=0),
    type='mmengine.optim.AmpOptimWrapper')
param_scheduler = [
    dict(
        begin=0,
        by_epoch=False,
        end=2000,
        start_factor=1e-05,
        type='mmengine.optim.LinearLR'),
    dict(
        T_max=150,
        begin=150,
        by_epoch=True,
        convert_to_iter_based=True,
        end=300,
        eta_min=2.5e-05,
        type='mmengine.optim.CosineAnnealingLR'),
]
quantizer_config = dict(
    bbox_head=dict(
        bbox_coder=dict(type='sscma.models.DistancePointBBoxCoder'),
        head_module=dict(
            act_cfg=dict(inplace=True, type='torch.nn.ReLU6'),
            feat_channels=256,
            featmap_strides=[
                8,
                16,
                32,
            ],
            in_channels=256,
            norm_cfg=dict(type='torch.nn.BatchNorm2d'),
            num_classes=2,
            pred_kernel_size=1,
            share_conv=False,
            stacked_convs=2,
            type='sscma.models.RTMDetSepBNHeadModule',
            widen_factor=0.25),
        loss_bbox=dict(loss_weight=2.0, type='sscma.models.GIoULoss'),
        loss_cls=dict(
            beta=2.0,
            loss_weight=1.0,
            type='sscma.models.QualityFocalLoss',
            use_sigmoid=True),
        prior_generator=dict(
            offset=0,
            strides=[
                8,
                16,
                32,
            ],
            type='sscma.models.MlvlPointGenerator'),
        test_cfg=dict(
            max_per_img=300,
            min_bbox_size=0,
            multi_label=True,
            nms=dict(iou_threshold=0.65, type='torchvision.ops.nms'),
            nms_pre=30000,
            score_thr=0.001),
        train_cfg=dict(
            allowed_border=-1,
            assigner=dict(
                iou_calculator=dict(type='sscma.models.BboxOverlaps2D'),
                num_classes=2,
                topk=13,
                type='sscma.models.BatchDynamicSoftLabelAssigner'),
            debug=False,
            pos_weight=-1),
        type='sscma.models.RTMDetHead'),
    data_preprocessor=dict(
        batch_augments=None,
        bgr_to_rgb=False,
        mean=[
            0,
            0,
            0,
        ],
        std=[
            255,
            255,
            255,
        ],
        type='sscma.datasets.DetDataPreprocessor'),
    type='sscma.quantizer.RtmdetQuantModel')
random_resize_ratio_range = (
    0.5,
    2.0,
)
randomness = dict(deterministic=False, seed=None)
resume = False
stage2_num_epochs = 20
test_cfg = dict(type='mmengine.runner.loops.TestLoop')
test_dataloader = dict(
    batch_size=16,
    dataset=dict(
        ann_file='annotations/instances_val2017.json',
        batch_shapes_cfg=dict(
            batch_size=32,
            extra_pad_ratio=0.5,
            img_size=192,
            size_divisor=32,
            type='sscma.datasets.BatchShapePolicy'),
        data_prefix=dict(img='val2017/'),
        data_root='../datasets/motherboard_led_detection_coco/',
        pipeline=[
            dict(
                backend_args=None,
                imdecode_backend='cv2',
                type='sscma.datasets.transforms.LoadImageFromFile'),
            dict(
                imdecode_backend='cv2',
                type='sscma.datasets.transforms.LoadAnnotations',
                with_bbox=True),
            dict(
                keep_ratio=True,
                scale=(
                    192,
                    192,
                ),
                type='sscma.datasets.transforms.Resize'),
            dict(
                pad_val=dict(img=(
                    114,
                    114,
                    114,
                )),
                size=(
                    192,
                    192,
                ),
                type='sscma.datasets.transforms.Pad'),
            dict(
                meta_keys=(
                    'img_id',
                    'img_path',
                    'ori_shape',
                    'img_shape',
                    'scale_factor',
                ),
                type='sscma.datasets.transforms.PackDetInputs'),
        ],
        test_mode=True,
        type='sscma.datasets.CustomFomoCocoDataset'),
    drop_last=False,
    num_workers=8,
    persistent_workers=True,
    pin_memory=True,
    sampler=dict(
        shuffle=False, type='mmengine.dataset.sampler.DefaultSampler'))
test_evaluator = dict(
    ann_file=
    '../datasets/motherboard_led_detection_coco/annotations/instances_val2017.json',
    backend_args=None,
    format_only=False,
    metric='bbox',
    proposal_nums=(
        100,
        1,
        10,
    ),
    sort_categories=True,
    type='sscma.evaluation.CocoMetric')
test_pipeline = [
    dict(
        backend_args=None,
        imdecode_backend='cv2',
        type='sscma.datasets.transforms.LoadImageFromFile'),
    dict(
        imdecode_backend='cv2',
        type='sscma.datasets.transforms.LoadAnnotations',
        with_bbox=True),
    dict(
        keep_ratio=True,
        scale=(
            192,
            192,
        ),
        type='sscma.datasets.transforms.Resize'),
    dict(
        pad_val=dict(img=(
            114,
            114,
            114,
        )),
        size=(
            192,
            192,
        ),
        type='sscma.datasets.transforms.Pad'),
    dict(
        meta_keys=(
            'img_id',
            'img_path',
            'ori_shape',
            'img_shape',
            'scale_factor',
        ),
        type='sscma.datasets.transforms.PackDetInputs'),
]
train_ann_file = 'annotations/instances_train2017.json'
train_cfg = dict(
    dynamic_intervals=[
        (
            280,
            1,
        ),
    ],
    max_epochs=300,
    type='mmengine.runner.loops.EpochBasedTrainLoop',
    val_interval=10)
train_dataloader = dict(
    batch_sampler=None,
    batch_size=32,
    collate_fn='sscma.datasets.coco_collate',
    dataset=dict(
        ann_file='annotations/instances_train2017.json',
        data_prefix=dict(img='train2017/'),
        data_root='../datasets/motherboard_led_detection_coco/',
        filter_cfg=dict(filter_empty_gt=True, min_size=32),
        pipeline=[
            dict(
                backend_args=None,
                imdecode_backend='cv2',
                type='sscma.datasets.transforms.LoadImageFromFile'),
            dict(
                imdecode_backend='cv2',
                type='sscma.datasets.transforms.LoadAnnotations',
                with_bbox=True),
            dict(
                img_scale=(
                    192,
                    192,
                ),
                max_cached_images=20,
                pad_val=114.0,
                random_pop=False,
                type='sscma.datasets.transforms.Mosaic',
                use_cached=True),
            dict(
                keep_ratio=True,
                ratio_range=(
                    0.8,
                    1.2,
                ),
                resize_type='sscma.datasets.transforms.Resize',
                scale=(
                    384,
                    384,
                ),
                type='sscma.datasets.transforms.RandomResize'),
            dict(
                crop_size=(
                    192,
                    192,
                ),
                type='sscma.datasets.transforms.RandomCrop'),
            dict(type='sscma.datasets.transforms.HSVRandomAug'),
            dict(prob=0.5, type='sscma.datasets.transforms.RandomFlip'),
            dict(
                pad_val=dict(img=(
                    114,
                    114,
                    114,
                )),
                size=(
                    192,
                    192,
                ),
                type='sscma.datasets.transforms.Pad'),
            dict(
                max_cached_images=10,
                prob=0.3,
                random_pop=False,
                type='sscma.datasets.transforms.MixUp',
                use_cached=True),
            dict(type='sscma.datasets.transforms.PackDetInputs'),
        ],
        type='sscma.datasets.CustomFomoCocoDataset'),
    num_workers=16,
    persistent_workers=True,
    pin_memory=True,
    sampler=dict(shuffle=True, type='mmengine.dataset.sampler.DefaultSampler'))
train_img_prefix = 'train2017/'
train_pipeline = [
    dict(
        backend_args=None,
        imdecode_backend='cv2',
        type='sscma.datasets.transforms.LoadImageFromFile'),
    dict(
        imdecode_backend='cv2',
        type='sscma.datasets.transforms.LoadAnnotations',
        with_bbox=True),
    dict(
        img_scale=(
            192,
            192,
        ),
        max_cached_images=20,
        pad_val=114.0,
        random_pop=False,
        type='sscma.datasets.transforms.Mosaic',
        use_cached=True),
    dict(
        keep_ratio=True,
        ratio_range=(
            0.8,
            1.2,
        ),
        resize_type='sscma.datasets.transforms.Resize',
        scale=(
            384,
            384,
        ),
        type='sscma.datasets.transforms.RandomResize'),
    dict(crop_size=(
        192,
        192,
    ), type='sscma.datasets.transforms.RandomCrop'),
    dict(type='sscma.datasets.transforms.HSVRandomAug'),
    dict(prob=0.5, type='sscma.datasets.transforms.RandomFlip'),
    dict(
        pad_val=dict(img=(
            114,
            114,
            114,
        )),
        size=(
            192,
            192,
        ),
        type='sscma.datasets.transforms.Pad'),
    dict(
        max_cached_images=10,
        prob=0.3,
        random_pop=False,
        type='sscma.datasets.transforms.MixUp',
        use_cached=True),
    dict(type='sscma.datasets.transforms.PackDetInputs'),
]
train_pipeline_stage2 = [
    dict(
        backend_args=None,
        imdecode_backend='cv2',
        type='sscma.datasets.transforms.LoadImageFromFile'),
    dict(
        imdecode_backend='cv2',
        type='sscma.datasets.transforms.LoadAnnotations',
        with_bbox=True),
    dict(
        keep_ratio=True,
        ratio_range=(
            0.8,
            1.2,
        ),
        resize_type='sscma.datasets.transforms.Resize',
        scale=(
            384,
            384,
        ),
        type='sscma.datasets.transforms.RandomResize'),
    dict(crop_size=(
        192,
        192,
    ), type='sscma.datasets.transforms.RandomCrop'),
    dict(type='sscma.datasets.transforms.HSVRandomAug'),
    dict(prob=0.5, type='sscma.datasets.transforms.RandomFlip'),
    dict(
        pad_val=dict(img=(
            114,
            114,
            114,
        )),
        size=(
            192,
            192,
        ),
        type='sscma.datasets.transforms.Pad'),
    dict(type='sscma.datasets.transforms.PackDetInputs'),
]
val_ann_file = 'annotations/instances_val2017.json'
val_cfg = dict(type='mmengine.runner.loops.ValLoop')
val_dataloader = dict(
    batch_size=16,
    dataset=dict(
        ann_file='annotations/instances_val2017.json',
        batch_shapes_cfg=dict(
            batch_size=32,
            extra_pad_ratio=0.5,
            img_size=192,
            size_divisor=32,
            type='sscma.datasets.BatchShapePolicy'),
        data_prefix=dict(img='val2017/'),
        data_root='../datasets/motherboard_led_detection_coco/',
        pipeline=[
            dict(
                backend_args=None,
                imdecode_backend='cv2',
                type='sscma.datasets.transforms.LoadImageFromFile'),
            dict(
                imdecode_backend='cv2',
                type='sscma.datasets.transforms.LoadAnnotations',
                with_bbox=True),
            dict(
                keep_ratio=True,
                scale=(
                    192,
                    192,
                ),
                type='sscma.datasets.transforms.Resize'),
            dict(
                pad_val=dict(img=(
                    114,
                    114,
                    114,
                )),
                size=(
                    192,
                    192,
                ),
                type='sscma.datasets.transforms.Pad'),
            dict(
                meta_keys=(
                    'img_id',
                    'img_path',
                    'ori_shape',
                    'img_shape',
                    'scale_factor',
                ),
                type='sscma.datasets.transforms.PackDetInputs'),
        ],
        test_mode=True,
        type='sscma.datasets.CustomFomoCocoDataset'),
    drop_last=False,
    num_workers=8,
    persistent_workers=True,
    pin_memory=True,
    sampler=dict(
        shuffle=False, type='mmengine.dataset.sampler.DefaultSampler'))
val_evaluator = dict(
    ann_file=
    '../datasets/motherboard_led_detection_coco/annotations/instances_val2017.json',
    backend_args=None,
    format_only=False,
    metric='bbox',
    proposal_nums=(
        100,
        1,
        10,
    ),
    sort_categories=True,
    type='sscma.evaluation.CocoMetric')
val_img_prefix = 'val2017/'
vis_backends = [
    dict(type='mmengine.visualization.LocalVisBackend'),
    dict(type='mmengine.visualization.TensorboardVisBackend'),
]
visualizer = dict(
    name='visualizer',
    type='sscma.visualization.DetLocalVisualizer',
    vis_backends=[
        dict(type='mmengine.visualization.LocalVisBackend'),
        dict(type='mmengine.visualization.TensorboardVisBackend'),
    ])
w_factor = 0.25
work_dir = 'work_dirs\\rtmdet_nano_config_motherboard_led_192x192'

2025/06/09 12:35:01 - mmengine - INFO - Distributed training is not used, all SyncBatchNorm (SyncBN) layers in the model will be automatically reverted to BatchNormXd layers if they are used.
2025/06/09 12:35:01 - mmengine - INFO - Hooks will be executed in the following order:
before_run:
(VERY_HIGH   ) RuntimeInfoHook                    
(49          ) EMAHook                            
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
after_load_checkpoint:
(49          ) EMAHook                            
 -------------------- 
before_train:
(VERY_HIGH   ) RuntimeInfoHook                    
(49          ) EMAHook                            
(NORMAL      ) IterTimerHook                      
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
before_train_epoch:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(NORMAL      ) DistSamplerSeedHook                
(NORMAL      ) PipelineSwitchHook                 
 -------------------- 
before_train_iter:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
 -------------------- 
after_train_iter:
(VERY_HIGH   ) RuntimeInfoHook                    
(49          ) EMAHook                            
(NORMAL      ) IterTimerHook                      
(BELOW_NORMAL) LoggerHook                         
(LOW         ) ParamSchedulerHook                 
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
after_train_epoch:
(NORMAL      ) IterTimerHook                      
(LOW         ) ParamSchedulerHook                 
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
before_val:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
before_val_epoch:
(49          ) EMAHook                            
(NORMAL      ) IterTimerHook                      
 -------------------- 
before_val_iter:
(NORMAL      ) IterTimerHook                      
 -------------------- 
after_val_iter:
(NORMAL      ) IterTimerHook                      
(NORMAL      ) DetVisualizationHook               
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
after_val_epoch:
(VERY_HIGH   ) RuntimeInfoHook                    
(49          ) EMAHook                            
(NORMAL      ) IterTimerHook                      
(BELOW_NORMAL) LoggerHook                         
(LOW         ) ParamSchedulerHook                 
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
after_val:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
before_save_checkpoint:
(49          ) EMAHook                            
 -------------------- 
after_train:
(VERY_HIGH   ) RuntimeInfoHook                    
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
before_test:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
before_test_epoch:
(49          ) EMAHook                            
(NORMAL      ) IterTimerHook                      
 -------------------- 
before_test_iter:
(NORMAL      ) IterTimerHook                      
 -------------------- 
after_test_iter:
(NORMAL      ) IterTimerHook                      
(NORMAL      ) DetVisualizationHook               
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
after_test_epoch:
(VERY_HIGH   ) RuntimeInfoHook                    
(49          ) EMAHook                            
(NORMAL      ) IterTimerHook                      
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
after_test:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
after_run:
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
2025/06/09 12:35:01 - mmengine - INFO - LR is set based on batch size of 32 and the current batch size is 32. Scaling the original LR by 1.0.
Name of parameter - Initialization information

backbone.stem.0.conv.weight - torch.Size([8, 3, 3, 3]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

backbone.stem.0.bn.weight - torch.Size([8]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stem.0.bn.bias - torch.Size([8]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stem.1.conv.weight - torch.Size([8, 8, 3, 3]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

backbone.stem.1.bn.weight - torch.Size([8]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stem.1.bn.bias - torch.Size([8]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stem.2.conv.weight - torch.Size([16, 8, 3, 3]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

backbone.stem.2.bn.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stem.2.bn.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage1.0.conv.weight - torch.Size([32, 16, 3, 3]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

backbone.stage1.0.bn.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage1.0.bn.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage1.1.main_conv.conv.weight - torch.Size([16, 32, 1, 1]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

backbone.stage1.1.main_conv.bn.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage1.1.main_conv.bn.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage1.1.short_conv.conv.weight - torch.Size([16, 32, 1, 1]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

backbone.stage1.1.short_conv.bn.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage1.1.short_conv.bn.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage1.1.final_conv.conv.weight - torch.Size([32, 32, 1, 1]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

backbone.stage1.1.final_conv.bn.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage1.1.final_conv.bn.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage1.1.blocks.0.conv1.conv.weight - torch.Size([16, 16, 3, 3]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

backbone.stage1.1.blocks.0.conv1.bn.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage1.1.blocks.0.conv1.bn.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage1.1.blocks.0.conv2.depthwise_conv.conv.weight - torch.Size([16, 1, 5, 5]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

backbone.stage1.1.blocks.0.conv2.depthwise_conv.bn.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage1.1.blocks.0.conv2.depthwise_conv.bn.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage1.1.blocks.0.conv2.pointwise_conv.conv.weight - torch.Size([16, 16, 1, 1]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

backbone.stage1.1.blocks.0.conv2.pointwise_conv.bn.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage1.1.blocks.0.conv2.pointwise_conv.bn.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage2.0.conv.weight - torch.Size([64, 32, 3, 3]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

backbone.stage2.0.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage2.0.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage2.1.main_conv.conv.weight - torch.Size([32, 64, 1, 1]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

backbone.stage2.1.main_conv.bn.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage2.1.main_conv.bn.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage2.1.short_conv.conv.weight - torch.Size([32, 64, 1, 1]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

backbone.stage2.1.short_conv.bn.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage2.1.short_conv.bn.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage2.1.final_conv.conv.weight - torch.Size([64, 64, 1, 1]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

backbone.stage2.1.final_conv.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage2.1.final_conv.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage2.1.blocks.0.conv1.conv.weight - torch.Size([32, 32, 3, 3]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

backbone.stage2.1.blocks.0.conv1.bn.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage2.1.blocks.0.conv1.bn.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage2.1.blocks.0.conv2.depthwise_conv.conv.weight - torch.Size([32, 1, 5, 5]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

backbone.stage2.1.blocks.0.conv2.depthwise_conv.bn.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage2.1.blocks.0.conv2.depthwise_conv.bn.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage2.1.blocks.0.conv2.pointwise_conv.conv.weight - torch.Size([32, 32, 1, 1]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

backbone.stage2.1.blocks.0.conv2.pointwise_conv.bn.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage2.1.blocks.0.conv2.pointwise_conv.bn.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage2.1.blocks.1.conv1.conv.weight - torch.Size([32, 32, 3, 3]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

backbone.stage2.1.blocks.1.conv1.bn.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage2.1.blocks.1.conv1.bn.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage2.1.blocks.1.conv2.depthwise_conv.conv.weight - torch.Size([32, 1, 5, 5]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

backbone.stage2.1.blocks.1.conv2.depthwise_conv.bn.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage2.1.blocks.1.conv2.depthwise_conv.bn.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage2.1.blocks.1.conv2.pointwise_conv.conv.weight - torch.Size([32, 32, 1, 1]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

backbone.stage2.1.blocks.1.conv2.pointwise_conv.bn.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage2.1.blocks.1.conv2.pointwise_conv.bn.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage3.0.conv.weight - torch.Size([128, 64, 3, 3]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

backbone.stage3.0.bn.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage3.0.bn.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage3.1.main_conv.conv.weight - torch.Size([64, 128, 1, 1]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

backbone.stage3.1.main_conv.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage3.1.main_conv.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage3.1.short_conv.conv.weight - torch.Size([64, 128, 1, 1]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

backbone.stage3.1.short_conv.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage3.1.short_conv.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage3.1.final_conv.conv.weight - torch.Size([128, 128, 1, 1]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

backbone.stage3.1.final_conv.bn.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage3.1.final_conv.bn.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage3.1.blocks.0.conv1.conv.weight - torch.Size([64, 64, 3, 3]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

backbone.stage3.1.blocks.0.conv1.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage3.1.blocks.0.conv1.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage3.1.blocks.0.conv2.depthwise_conv.conv.weight - torch.Size([64, 1, 5, 5]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

backbone.stage3.1.blocks.0.conv2.depthwise_conv.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage3.1.blocks.0.conv2.depthwise_conv.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage3.1.blocks.0.conv2.pointwise_conv.conv.weight - torch.Size([64, 64, 1, 1]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

backbone.stage3.1.blocks.0.conv2.pointwise_conv.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage3.1.blocks.0.conv2.pointwise_conv.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage3.1.blocks.1.conv1.conv.weight - torch.Size([64, 64, 3, 3]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

backbone.stage3.1.blocks.1.conv1.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage3.1.blocks.1.conv1.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage3.1.blocks.1.conv2.depthwise_conv.conv.weight - torch.Size([64, 1, 5, 5]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

backbone.stage3.1.blocks.1.conv2.depthwise_conv.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage3.1.blocks.1.conv2.depthwise_conv.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage3.1.blocks.1.conv2.pointwise_conv.conv.weight - torch.Size([64, 64, 1, 1]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

backbone.stage3.1.blocks.1.conv2.pointwise_conv.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage3.1.blocks.1.conv2.pointwise_conv.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage4.0.conv.weight - torch.Size([256, 128, 3, 3]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

backbone.stage4.0.bn.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage4.0.bn.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage4.1.conv1.conv.weight - torch.Size([128, 256, 1, 1]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

backbone.stage4.1.conv1.bn.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage4.1.conv1.bn.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage4.1.conv2.conv.weight - torch.Size([256, 512, 1, 1]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

backbone.stage4.1.conv2.bn.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage4.1.conv2.bn.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage4.2.main_conv.conv.weight - torch.Size([128, 256, 1, 1]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

backbone.stage4.2.main_conv.bn.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage4.2.main_conv.bn.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage4.2.short_conv.conv.weight - torch.Size([128, 256, 1, 1]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

backbone.stage4.2.short_conv.bn.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage4.2.short_conv.bn.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage4.2.final_conv.conv.weight - torch.Size([256, 256, 1, 1]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

backbone.stage4.2.final_conv.bn.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage4.2.final_conv.bn.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage4.2.blocks.0.conv1.conv.weight - torch.Size([128, 128, 3, 3]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

backbone.stage4.2.blocks.0.conv1.bn.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage4.2.blocks.0.conv1.bn.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage4.2.blocks.0.conv2.depthwise_conv.conv.weight - torch.Size([128, 1, 5, 5]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

backbone.stage4.2.blocks.0.conv2.depthwise_conv.bn.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage4.2.blocks.0.conv2.depthwise_conv.bn.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage4.2.blocks.0.conv2.pointwise_conv.conv.weight - torch.Size([128, 128, 1, 1]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

backbone.stage4.2.blocks.0.conv2.pointwise_conv.bn.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

backbone.stage4.2.blocks.0.conv2.pointwise_conv.bn.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.reduce_layers.2.conv.weight - torch.Size([128, 256, 1, 1]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.reduce_layers.2.bn.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.reduce_layers.2.bn.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.top_down_layers.0.0.main_conv.conv.weight - torch.Size([64, 256, 1, 1]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.top_down_layers.0.0.main_conv.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.top_down_layers.0.0.main_conv.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.top_down_layers.0.0.short_conv.conv.weight - torch.Size([64, 256, 1, 1]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.top_down_layers.0.0.short_conv.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.top_down_layers.0.0.short_conv.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.top_down_layers.0.0.final_conv.conv.weight - torch.Size([128, 128, 1, 1]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.top_down_layers.0.0.final_conv.bn.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.top_down_layers.0.0.final_conv.bn.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.top_down_layers.0.0.blocks.0.conv1.conv.weight - torch.Size([64, 64, 3, 3]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.top_down_layers.0.0.blocks.0.conv1.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.top_down_layers.0.0.blocks.0.conv1.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.top_down_layers.0.0.blocks.0.conv2.depthwise_conv.conv.weight - torch.Size([64, 1, 5, 5]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.top_down_layers.0.0.blocks.0.conv2.depthwise_conv.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.top_down_layers.0.0.blocks.0.conv2.depthwise_conv.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.top_down_layers.0.0.blocks.0.conv2.pointwise_conv.conv.weight - torch.Size([64, 64, 1, 1]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.top_down_layers.0.0.blocks.0.conv2.pointwise_conv.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.top_down_layers.0.0.blocks.0.conv2.pointwise_conv.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.top_down_layers.0.1.conv.weight - torch.Size([64, 128, 1, 1]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.top_down_layers.0.1.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.top_down_layers.0.1.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.top_down_layers.1.main_conv.conv.weight - torch.Size([32, 128, 1, 1]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.top_down_layers.1.main_conv.bn.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.top_down_layers.1.main_conv.bn.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.top_down_layers.1.short_conv.conv.weight - torch.Size([32, 128, 1, 1]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.top_down_layers.1.short_conv.bn.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.top_down_layers.1.short_conv.bn.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.top_down_layers.1.final_conv.conv.weight - torch.Size([64, 64, 1, 1]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.top_down_layers.1.final_conv.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.top_down_layers.1.final_conv.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.top_down_layers.1.blocks.0.conv1.conv.weight - torch.Size([32, 32, 3, 3]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.top_down_layers.1.blocks.0.conv1.bn.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.top_down_layers.1.blocks.0.conv1.bn.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.top_down_layers.1.blocks.0.conv2.depthwise_conv.conv.weight - torch.Size([32, 1, 5, 5]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.top_down_layers.1.blocks.0.conv2.depthwise_conv.bn.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.top_down_layers.1.blocks.0.conv2.depthwise_conv.bn.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.top_down_layers.1.blocks.0.conv2.pointwise_conv.conv.weight - torch.Size([32, 32, 1, 1]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.top_down_layers.1.blocks.0.conv2.pointwise_conv.bn.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.top_down_layers.1.blocks.0.conv2.pointwise_conv.bn.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.downsample_layers.0.conv.weight - torch.Size([64, 64, 3, 3]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.downsample_layers.0.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.downsample_layers.0.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.downsample_layers.1.conv.weight - torch.Size([128, 128, 3, 3]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.downsample_layers.1.bn.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.downsample_layers.1.bn.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.bottom_up_layers.0.main_conv.conv.weight - torch.Size([64, 128, 1, 1]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.bottom_up_layers.0.main_conv.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.bottom_up_layers.0.main_conv.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.bottom_up_layers.0.short_conv.conv.weight - torch.Size([64, 128, 1, 1]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.bottom_up_layers.0.short_conv.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.bottom_up_layers.0.short_conv.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.bottom_up_layers.0.final_conv.conv.weight - torch.Size([128, 128, 1, 1]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.bottom_up_layers.0.final_conv.bn.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.bottom_up_layers.0.final_conv.bn.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.bottom_up_layers.0.blocks.0.conv1.conv.weight - torch.Size([64, 64, 3, 3]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.bottom_up_layers.0.blocks.0.conv1.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.bottom_up_layers.0.blocks.0.conv1.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.bottom_up_layers.0.blocks.0.conv2.depthwise_conv.conv.weight - torch.Size([64, 1, 5, 5]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.bottom_up_layers.0.blocks.0.conv2.depthwise_conv.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.bottom_up_layers.0.blocks.0.conv2.depthwise_conv.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.bottom_up_layers.0.blocks.0.conv2.pointwise_conv.conv.weight - torch.Size([64, 64, 1, 1]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.bottom_up_layers.0.blocks.0.conv2.pointwise_conv.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.bottom_up_layers.0.blocks.0.conv2.pointwise_conv.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.bottom_up_layers.1.main_conv.conv.weight - torch.Size([128, 256, 1, 1]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.bottom_up_layers.1.main_conv.bn.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.bottom_up_layers.1.main_conv.bn.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.bottom_up_layers.1.short_conv.conv.weight - torch.Size([128, 256, 1, 1]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.bottom_up_layers.1.short_conv.bn.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.bottom_up_layers.1.short_conv.bn.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.bottom_up_layers.1.final_conv.conv.weight - torch.Size([256, 256, 1, 1]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.bottom_up_layers.1.final_conv.bn.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.bottom_up_layers.1.final_conv.bn.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.bottom_up_layers.1.blocks.0.conv1.conv.weight - torch.Size([128, 128, 3, 3]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.bottom_up_layers.1.blocks.0.conv1.bn.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.bottom_up_layers.1.blocks.0.conv1.bn.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.bottom_up_layers.1.blocks.0.conv2.depthwise_conv.conv.weight - torch.Size([128, 1, 5, 5]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.bottom_up_layers.1.blocks.0.conv2.depthwise_conv.bn.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.bottom_up_layers.1.blocks.0.conv2.depthwise_conv.bn.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.bottom_up_layers.1.blocks.0.conv2.pointwise_conv.conv.weight - torch.Size([128, 128, 1, 1]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.bottom_up_layers.1.blocks.0.conv2.pointwise_conv.bn.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.bottom_up_layers.1.blocks.0.conv2.pointwise_conv.bn.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.out_layers.0.conv.weight - torch.Size([64, 64, 3, 3]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.out_layers.0.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.out_layers.0.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.out_layers.1.conv.weight - torch.Size([64, 128, 3, 3]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.out_layers.1.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.out_layers.1.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.out_layers.2.conv.weight - torch.Size([64, 256, 3, 3]): 
KaimingInit: a=2.23606797749979, mode=fan_in, nonlinearity=leaky_relu, distribution =uniform, bias=0 

neck.out_layers.2.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

neck.out_layers.2.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

bbox_head.head_module.cls_convs.0.0.conv.weight - torch.Size([64, 64, 3, 3]): 
Initialized by user-defined `init_weights` in RTMDetSepBNHeadModule  

bbox_head.head_module.cls_convs.0.0.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

bbox_head.head_module.cls_convs.0.0.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

bbox_head.head_module.cls_convs.0.1.conv.weight - torch.Size([64, 64, 3, 3]): 
Initialized by user-defined `init_weights` in RTMDetSepBNHeadModule  

bbox_head.head_module.cls_convs.0.1.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

bbox_head.head_module.cls_convs.0.1.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

bbox_head.head_module.cls_convs.1.0.conv.weight - torch.Size([64, 64, 3, 3]): 
Initialized by user-defined `init_weights` in RTMDetSepBNHeadModule  

bbox_head.head_module.cls_convs.1.0.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

bbox_head.head_module.cls_convs.1.0.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

bbox_head.head_module.cls_convs.1.1.conv.weight - torch.Size([64, 64, 3, 3]): 
Initialized by user-defined `init_weights` in RTMDetSepBNHeadModule  

bbox_head.head_module.cls_convs.1.1.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

bbox_head.head_module.cls_convs.1.1.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

bbox_head.head_module.cls_convs.2.0.conv.weight - torch.Size([64, 64, 3, 3]): 
Initialized by user-defined `init_weights` in RTMDetSepBNHeadModule  

bbox_head.head_module.cls_convs.2.0.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

bbox_head.head_module.cls_convs.2.0.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

bbox_head.head_module.cls_convs.2.1.conv.weight - torch.Size([64, 64, 3, 3]): 
Initialized by user-defined `init_weights` in RTMDetSepBNHeadModule  

bbox_head.head_module.cls_convs.2.1.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

bbox_head.head_module.cls_convs.2.1.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

bbox_head.head_module.reg_convs.0.0.conv.weight - torch.Size([64, 64, 3, 3]): 
Initialized by user-defined `init_weights` in RTMDetSepBNHeadModule  

bbox_head.head_module.reg_convs.0.0.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

bbox_head.head_module.reg_convs.0.0.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

bbox_head.head_module.reg_convs.0.1.conv.weight - torch.Size([64, 64, 3, 3]): 
Initialized by user-defined `init_weights` in RTMDetSepBNHeadModule  

bbox_head.head_module.reg_convs.0.1.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

bbox_head.head_module.reg_convs.0.1.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

bbox_head.head_module.reg_convs.1.0.conv.weight - torch.Size([64, 64, 3, 3]): 
Initialized by user-defined `init_weights` in RTMDetSepBNHeadModule  

bbox_head.head_module.reg_convs.1.0.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

bbox_head.head_module.reg_convs.1.0.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

bbox_head.head_module.reg_convs.1.1.conv.weight - torch.Size([64, 64, 3, 3]): 
Initialized by user-defined `init_weights` in RTMDetSepBNHeadModule  

bbox_head.head_module.reg_convs.1.1.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

bbox_head.head_module.reg_convs.1.1.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

bbox_head.head_module.reg_convs.2.0.conv.weight - torch.Size([64, 64, 3, 3]): 
Initialized by user-defined `init_weights` in RTMDetSepBNHeadModule  

bbox_head.head_module.reg_convs.2.0.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

bbox_head.head_module.reg_convs.2.0.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

bbox_head.head_module.reg_convs.2.1.conv.weight - torch.Size([64, 64, 3, 3]): 
Initialized by user-defined `init_weights` in RTMDetSepBNHeadModule  

bbox_head.head_module.reg_convs.2.1.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

bbox_head.head_module.reg_convs.2.1.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of RTMDet  

bbox_head.head_module.rtm_cls.0.weight - torch.Size([2, 64, 1, 1]): 
Initialized by user-defined `init_weights` in RTMDetSepBNHeadModule  

bbox_head.head_module.rtm_cls.0.bias - torch.Size([2]): 
Initialized by user-defined `init_weights` in RTMDetSepBNHeadModule  

bbox_head.head_module.rtm_cls.1.weight - torch.Size([2, 64, 1, 1]): 
Initialized by user-defined `init_weights` in RTMDetSepBNHeadModule  

bbox_head.head_module.rtm_cls.1.bias - torch.Size([2]): 
Initialized by user-defined `init_weights` in RTMDetSepBNHeadModule  

bbox_head.head_module.rtm_cls.2.weight - torch.Size([2, 64, 1, 1]): 
Initialized by user-defined `init_weights` in RTMDetSepBNHeadModule  

bbox_head.head_module.rtm_cls.2.bias - torch.Size([2]): 
Initialized by user-defined `init_weights` in RTMDetSepBNHeadModule  

bbox_head.head_module.rtm_reg.0.weight - torch.Size([4, 64, 1, 1]): 
Initialized by user-defined `init_weights` in RTMDetSepBNHeadModule  

bbox_head.head_module.rtm_reg.0.bias - torch.Size([4]): 
Initialized by user-defined `init_weights` in RTMDetSepBNHeadModule  

bbox_head.head_module.rtm_reg.1.weight - torch.Size([4, 64, 1, 1]): 
Initialized by user-defined `init_weights` in RTMDetSepBNHeadModule  

bbox_head.head_module.rtm_reg.1.bias - torch.Size([4]): 
Initialized by user-defined `init_weights` in RTMDetSepBNHeadModule  

bbox_head.head_module.rtm_reg.2.weight - torch.Size([4, 64, 1, 1]): 
Initialized by user-defined `init_weights` in RTMDetSepBNHeadModule  

bbox_head.head_module.rtm_reg.2.bias - torch.Size([4]): 
Initialized by user-defined `init_weights` in RTMDetSepBNHeadModule  
2025/06/09 12:35:02 - mmengine - WARNING - "FileClient" will be deprecated in future. Please use io functions in https://mmengine.readthedocs.io/en/latest/api/fileio.html#file-io
2025/06/09 12:35:02 - mmengine - WARNING - "HardDiskBackend" is the alias of "LocalBackend" and the former will be deprecated in future.
2025/06/09 12:35:02 - mmengine - INFO - Checkpoints will be saved to D:\OBJECT_DETECTION\sscma\ModelAssistant\work_dirs\rtmdet_nano_config_motherboard_led_192x192.
2025/06/09 12:36:19 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:36:19 - mmengine - INFO - Epoch(train)   [1][31/31]  base_lr: 6.0069e-06 lr: 6.0069e-06  eta: 6:24:06  time: 2.4864  data_time: 2.3064  memory: 406  loss: 0.3829  loss_cls: 0.0566  loss_bbox: 0.3263
2025/06/09 12:36:24 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:36:24 - mmengine - INFO - Epoch(train)   [2][31/31]  base_lr: 1.2210e-05 lr: 1.2210e-05  eta: 3:24:21  time: 0.1511  data_time: 0.0297  memory: 415  loss: 0.5379  loss_cls: 0.0646  loss_bbox: 0.4733
2025/06/09 12:36:29 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:36:29 - mmengine - INFO - Epoch(train)   [3][31/31]  base_lr: 1.8413e-05 lr: 1.8413e-05  eta: 2:23:39  time: 0.1356  data_time: 0.0153  memory: 414  loss: 0.6150  loss_cls: 0.0711  loss_bbox: 0.5439
2025/06/09 12:36:34 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:36:34 - mmengine - INFO - Epoch(train)   [4][31/31]  base_lr: 2.4616e-05 lr: 2.4616e-05  eta: 1:53:40  time: 0.1416  data_time: 0.0168  memory: 419  loss: 0.6966  loss_cls: 0.0746  loss_bbox: 0.6220
2025/06/09 12:36:39 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:36:39 - mmengine - INFO - Epoch(train)   [5][31/31]  base_lr: 3.0819e-05 lr: 3.0819e-05  eta: 1:35:55  time: 0.1525  data_time: 0.0252  memory: 414  loss: 1.0062  loss_cls: 0.1274  loss_bbox: 0.8787
2025/06/09 12:36:45 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:36:45 - mmengine - INFO - Epoch(train)   [6][31/31]  base_lr: 3.7022e-05 lr: 3.7022e-05  eta: 1:23:58  time: 0.1508  data_time: 0.0195  memory: 414  loss: 1.1576  loss_cls: 0.1459  loss_bbox: 1.0117
2025/06/09 12:36:50 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:36:50 - mmengine - INFO - Epoch(train)   [7][31/31]  base_lr: 4.3225e-05 lr: 4.3225e-05  eta: 1:15:23  time: 0.1509  data_time: 0.0241  memory: 411  loss: 1.3496  loss_cls: 0.1763  loss_bbox: 1.1733
2025/06/09 12:36:55 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:36:55 - mmengine - INFO - Epoch(train)   [8][31/31]  base_lr: 4.9428e-05 lr: 4.9428e-05  eta: 1:08:50  time: 0.1457  data_time: 0.0158  memory: 417  loss: 1.5931  loss_cls: 0.1990  loss_bbox: 1.3941
2025/06/09 12:37:00 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:37:00 - mmengine - INFO - Epoch(train)   [9][31/31]  base_lr: 5.5631e-05 lr: 5.5631e-05  eta: 1:03:46  time: 0.1463  data_time: 0.0190  memory: 427  loss: 1.9015  loss_cls: 0.2350  loss_bbox: 1.6665
2025/06/09 12:37:05 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:37:05 - mmengine - INFO - Epoch(train)  [10][31/31]  base_lr: 6.1834e-05 lr: 6.1834e-05  eta: 0:59:42  time: 0.1497  data_time: 0.0166  memory: 420  loss: 2.0378  loss_cls: 0.2118  loss_bbox: 1.8259
2025/06/09 12:37:05 - mmengine - INFO - Saving checkpoint at 10 epochs
2025/06/09 12:37:42 - mmengine - INFO - Evaluating bbox...
2025/06/09 12:37:43 - mmengine - INFO - bbox_mAP_copypaste: 0.000 0.000 0.000 0.000 0.000 0.000
2025/06/09 12:37:43 - mmengine - INFO - Epoch(val) [10][20/20]    coco/bbox_mAP: 0.0000  coco/bbox_mAP_50: 0.0000  coco/bbox_mAP_75: 0.0000  coco/bbox_mAP_s: 0.0000  coco/bbox_mAP_m: 0.0000  coco/bbox_mAP_l: 0.0000  data_time: 1.7099  time: 1.7681
2025/06/09 12:37:44 - mmengine - INFO - The best checkpoint with 0.0000 coco/bbox_mAP at 10 epoch is saved to best_coco_bbox_mAP_epoch_10.pth.
2025/06/09 12:37:49 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:37:49 - mmengine - INFO - Epoch(train)  [11][31/31]  base_lr: 6.8037e-05 lr: 6.8037e-05  eta: 0:56:19  time: 0.1455  data_time: 0.0159  memory: 413  loss: 2.0205  loss_cls: 0.2296  loss_bbox: 1.7910
2025/06/09 12:37:54 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:37:54 - mmengine - INFO - Epoch(train)  [12][31/31]  base_lr: 7.4240e-05 lr: 7.4240e-05  eta: 0:53:24  time: 0.1402  data_time: 0.0230  memory: 415  loss: 2.0178  loss_cls: 0.2546  loss_bbox: 1.7632
2025/06/09 12:37:59 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:37:59 - mmengine - INFO - Epoch(train)  [13][31/31]  base_lr: 8.0443e-05 lr: 8.0443e-05  eta: 0:50:58  time: 0.1402  data_time: 0.0171  memory: 447  loss: 2.0112  loss_cls: 0.2246  loss_bbox: 1.7866
2025/06/09 12:38:04 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:38:04 - mmengine - INFO - Epoch(train)  [14][31/31]  base_lr: 8.6646e-05 lr: 8.6646e-05  eta: 0:48:55  time: 0.1468  data_time: 0.0205  memory: 422  loss: 2.0065  loss_cls: 0.1799  loss_bbox: 1.8266
2025/06/09 12:38:09 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:38:09 - mmengine - INFO - Epoch(train)  [15][31/31]  base_lr: 9.2849e-05 lr: 9.2849e-05  eta: 0:47:01  time: 0.1385  data_time: 0.0205  memory: 415  loss: 2.0043  loss_cls: 0.1722  loss_bbox: 1.8321
2025/06/09 12:38:14 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:38:14 - mmengine - INFO - Epoch(train)  [16][31/31]  base_lr: 9.9053e-05 lr: 9.9053e-05  eta: 0:45:26  time: 0.1420  data_time: 0.0211  memory: 417  loss: 2.0213  loss_cls: 0.2076  loss_bbox: 1.8137
2025/06/09 12:38:19 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:38:19 - mmengine - INFO - Epoch(train)  [17][31/31]  base_lr: 1.0526e-04 lr: 1.0526e-04  eta: 0:43:54  time: 0.1369  data_time: 0.0146  memory: 411  loss: 2.0128  loss_cls: 0.2308  loss_bbox: 1.7819
2025/06/09 12:38:24 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:38:24 - mmengine - INFO - Epoch(train)  [18][31/31]  base_lr: 1.1146e-04 lr: 1.1146e-04  eta: 0:42:39  time: 0.1412  data_time: 0.0189  memory: 418  loss: 2.0241  loss_cls: 0.2565  loss_bbox: 1.7676
2025/06/09 12:38:29 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:38:29 - mmengine - INFO - Epoch(train)  [19][31/31]  base_lr: 1.1766e-04 lr: 1.1766e-04  eta: 0:41:28  time: 0.1409  data_time: 0.0132  memory: 415  loss: 2.0246  loss_cls: 0.2505  loss_bbox: 1.7741
2025/06/09 12:38:34 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:38:34 - mmengine - INFO - Epoch(train)  [20][31/31]  base_lr: 1.2386e-04 lr: 1.2386e-04  eta: 0:40:26  time: 0.1430  data_time: 0.0255  memory: 417  loss: 2.0131  loss_cls: 0.2426  loss_bbox: 1.7705
2025/06/09 12:38:34 - mmengine - INFO - Saving checkpoint at 20 epochs
2025/06/09 12:38:36 - mmengine - INFO - Evaluating bbox...
2025/06/09 12:38:37 - mmengine - INFO - bbox_mAP_copypaste: 0.000 0.000 0.000 0.000 0.000 0.000
2025/06/09 12:38:37 - mmengine - INFO - Epoch(val) [20][20/20]    coco/bbox_mAP: 0.0000  coco/bbox_mAP_50: 0.0000  coco/bbox_mAP_75: 0.0000  coco/bbox_mAP_s: 0.0000  coco/bbox_mAP_m: 0.0000  coco/bbox_mAP_l: 0.0000  data_time: 0.0140  time: 0.0608
2025/06/09 12:38:42 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:38:42 - mmengine - INFO - Epoch(train)  [21][31/31]  base_lr: 1.3007e-04 lr: 1.3007e-04  eta: 0:39:27  time: 0.1398  data_time: 0.0188  memory: 423  loss: 1.9874  loss_cls: 0.2977  loss_bbox: 1.6896
2025/06/09 12:38:47 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:38:47 - mmengine - INFO - Epoch(train)  [22][31/31]  base_lr: 1.3627e-04 lr: 1.3627e-04  eta: 0:38:35  time: 0.1405  data_time: 0.0194  memory: 417  loss: 2.0033  loss_cls: 0.4497  loss_bbox: 1.5536
2025/06/09 12:38:52 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:38:52 - mmengine - INFO - Epoch(train)  [23][31/31]  base_lr: 1.4247e-04 lr: 1.4247e-04  eta: 0:37:46  time: 0.1422  data_time: 0.0160  memory: 420  loss: 2.0785  loss_cls: 0.7500  loss_bbox: 1.3285
2025/06/09 12:38:57 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:38:57 - mmengine - INFO - Epoch(train)  [24][31/31]  base_lr: 1.4868e-04 lr: 1.4868e-04  eta: 0:36:58  time: 0.1365  data_time: 0.0146  memory: 417  loss: 2.1586  loss_cls: 1.0489  loss_bbox: 1.1097
2025/06/09 12:39:02 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:39:02 - mmengine - INFO - Epoch(train)  [25][31/31]  base_lr: 1.5488e-04 lr: 1.5488e-04  eta: 0:36:17  time: 0.1411  data_time: 0.0298  memory: 411  loss: 2.1246  loss_cls: 1.0753  loss_bbox: 1.0493
2025/06/09 12:39:06 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:39:06 - mmengine - INFO - Epoch(train)  [26][31/31]  base_lr: 1.6108e-04 lr: 1.6108e-04  eta: 0:35:37  time: 0.1388  data_time: 0.0162  memory: 415  loss: 2.0273  loss_cls: 1.0115  loss_bbox: 1.0159
2025/06/09 12:39:12 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:39:12 - mmengine - INFO - Epoch(train)  [27][31/31]  base_lr: 1.6729e-04 lr: 1.6729e-04  eta: 0:35:02  time: 0.1425  data_time: 0.0209  memory: 418  loss: 1.8932  loss_cls: 0.8850  loss_bbox: 1.0082
2025/06/09 12:39:17 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:39:17 - mmengine - INFO - Epoch(train)  [28][31/31]  base_lr: 1.7349e-04 lr: 1.7349e-04  eta: 0:34:27  time: 0.1430  data_time: 0.0147  memory: 425  loss: 1.7573  loss_cls: 0.7741  loss_bbox: 0.9832
2025/06/09 12:39:22 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:39:22 - mmengine - INFO - Epoch(train)  [29][31/31]  base_lr: 1.7969e-04 lr: 1.7969e-04  eta: 0:33:57  time: 0.1458  data_time: 0.0166  memory: 420  loss: 1.6413  loss_cls: 0.6641  loss_bbox: 0.9771
2025/06/09 12:39:27 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:39:27 - mmengine - INFO - Epoch(train)  [30][31/31]  base_lr: 1.8590e-04 lr: 1.8590e-04  eta: 0:33:27  time: 0.1422  data_time: 0.0194  memory: 425  loss: 1.5309  loss_cls: 0.5696  loss_bbox: 0.9612
2025/06/09 12:39:27 - mmengine - INFO - Saving checkpoint at 30 epochs
2025/06/09 12:39:29 - mmengine - INFO - Evaluating bbox...
2025/06/09 12:39:30 - mmengine - INFO - bbox_mAP_copypaste: 0.001 0.006 0.000 0.000 0.000 0.003
2025/06/09 12:39:30 - mmengine - INFO - Epoch(val) [30][20/20]    coco/bbox_mAP: 0.0010  coco/bbox_mAP_50: 0.0060  coco/bbox_mAP_75: 0.0000  coco/bbox_mAP_s: 0.0000  coco/bbox_mAP_m: 0.0000  coco/bbox_mAP_l: 0.0030  data_time: 0.0142  time: 0.0553
2025/06/09 12:39:30 - mmengine - INFO - The previous best checkpoint D:\OBJECT_DETECTION\sscma\ModelAssistant\work_dirs\rtmdet_nano_config_motherboard_led_192x192\best_coco_bbox_mAP_epoch_10.pth is removed
2025/06/09 12:39:30 - mmengine - INFO - The best checkpoint with 0.0010 coco/bbox_mAP at 30 epoch is saved to best_coco_bbox_mAP_epoch_30.pth.
2025/06/09 12:39:36 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:39:36 - mmengine - INFO - Epoch(train)  [31][31/31]  base_lr: 1.9210e-04 lr: 1.9210e-04  eta: 0:33:00  time: 0.1467  data_time: 0.0277  memory: 431  loss: 1.4112  loss_cls: 0.4444  loss_bbox: 0.9668
2025/06/09 12:39:41 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:39:41 - mmengine - INFO - Epoch(train)  [32][31/31]  base_lr: 1.9830e-04 lr: 1.9830e-04  eta: 0:32:34  time: 0.1473  data_time: 0.0257  memory: 426  loss: 1.3378  loss_cls: 0.3749  loss_bbox: 0.9629
2025/06/09 12:39:44 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:39:46 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:39:46 - mmengine - INFO - Epoch(train)  [33][31/31]  base_lr: 2.0450e-04 lr: 2.0450e-04  eta: 0:32:11  time: 0.1499  data_time: 0.0318  memory: 436  loss: 1.2935  loss_cls: 0.3417  loss_bbox: 0.9518
2025/06/09 12:39:51 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:39:51 - mmengine - INFO - Epoch(train)  [34][31/31]  base_lr: 2.1071e-04 lr: 2.1071e-04  eta: 0:31:46  time: 0.1425  data_time: 0.0136  memory: 414  loss: 1.2691  loss_cls: 0.3209  loss_bbox: 0.9483
2025/06/09 12:39:56 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:39:56 - mmengine - INFO - Epoch(train)  [35][31/31]  base_lr: 2.1691e-04 lr: 2.1691e-04  eta: 0:31:21  time: 0.1401  data_time: 0.0165  memory: 409  loss: 1.2460  loss_cls: 0.3093  loss_bbox: 0.9367
2025/06/09 12:40:01 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:40:01 - mmengine - INFO - Epoch(train)  [36][31/31]  base_lr: 2.2311e-04 lr: 2.2311e-04  eta: 0:31:00  time: 0.1467  data_time: 0.0134  memory: 414  loss: 1.2538  loss_cls: 0.3003  loss_bbox: 0.9536
2025/06/09 12:40:07 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:40:07 - mmengine - INFO - Epoch(train)  [37][31/31]  base_lr: 2.2932e-04 lr: 2.2932e-04  eta: 0:30:39  time: 0.1453  data_time: 0.0251  memory: 413  loss: 1.2669  loss_cls: 0.3046  loss_bbox: 0.9623
2025/06/09 12:40:11 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:40:11 - mmengine - INFO - Epoch(train)  [38][31/31]  base_lr: 2.3552e-04 lr: 2.3552e-04  eta: 0:30:18  time: 0.1397  data_time: 0.0184  memory: 418  loss: 1.2460  loss_cls: 0.3067  loss_bbox: 0.9394
2025/06/09 12:40:16 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:40:16 - mmengine - INFO - Epoch(train)  [39][31/31]  base_lr: 2.4172e-04 lr: 2.4172e-04  eta: 0:29:58  time: 0.1424  data_time: 0.0168  memory: 417  loss: 1.2510  loss_cls: 0.3025  loss_bbox: 0.9485
2025/06/09 12:40:21 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:40:21 - mmengine - INFO - Epoch(train)  [40][31/31]  base_lr: 2.4793e-04 lr: 2.4793e-04  eta: 0:29:38  time: 0.1415  data_time: 0.0207  memory: 422  loss: 1.2571  loss_cls: 0.3064  loss_bbox: 0.9506
2025/06/09 12:40:21 - mmengine - INFO - Saving checkpoint at 40 epochs
2025/06/09 12:40:24 - mmengine - INFO - Evaluating bbox...
2025/06/09 12:40:25 - mmengine - INFO - bbox_mAP_copypaste: 0.001 0.006 0.000 0.000 0.000 0.002
2025/06/09 12:40:25 - mmengine - INFO - Epoch(val) [40][20/20]    coco/bbox_mAP: 0.0010  coco/bbox_mAP_50: 0.0060  coco/bbox_mAP_75: 0.0000  coco/bbox_mAP_s: 0.0000  coco/bbox_mAP_m: 0.0000  coco/bbox_mAP_l: 0.0020  data_time: 0.0205  time: 0.0636
2025/06/09 12:40:30 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:40:30 - mmengine - INFO - Epoch(train)  [41][31/31]  base_lr: 2.5413e-04 lr: 2.5413e-04  eta: 0:29:21  time: 0.1447  data_time: 0.0228  memory: 409  loss: 1.2139  loss_cls: 0.2939  loss_bbox: 0.9199
2025/06/09 12:40:35 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:40:35 - mmengine - INFO - Epoch(train)  [42][31/31]  base_lr: 2.6033e-04 lr: 2.6033e-04  eta: 0:29:02  time: 0.1402  data_time: 0.0137  memory: 422  loss: 1.2141  loss_cls: 0.2930  loss_bbox: 0.9210
2025/06/09 12:40:40 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:40:40 - mmengine - INFO - Epoch(train)  [43][31/31]  base_lr: 2.6653e-04 lr: 2.6653e-04  eta: 0:28:44  time: 0.1382  data_time: 0.0127  memory: 417  loss: 1.2253  loss_cls: 0.2872  loss_bbox: 0.9381
2025/06/09 12:40:45 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:40:45 - mmengine - INFO - Epoch(train)  [44][31/31]  base_lr: 2.7274e-04 lr: 2.7274e-04  eta: 0:28:27  time: 0.1399  data_time: 0.0252  memory: 409  loss: 1.2387  loss_cls: 0.2840  loss_bbox: 0.9548
2025/06/09 12:40:50 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:40:50 - mmengine - INFO - Epoch(train)  [45][31/31]  base_lr: 2.7894e-04 lr: 2.7894e-04  eta: 0:28:11  time: 0.1408  data_time: 0.0159  memory: 417  loss: 1.2429  loss_cls: 0.2874  loss_bbox: 0.9555
2025/06/09 12:40:55 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:40:55 - mmengine - INFO - Epoch(train)  [46][31/31]  base_lr: 2.8514e-04 lr: 2.8514e-04  eta: 0:27:55  time: 0.1415  data_time: 0.0166  memory: 411  loss: 1.2438  loss_cls: 0.2890  loss_bbox: 0.9548
2025/06/09 12:41:00 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:41:00 - mmengine - INFO - Epoch(train)  [47][31/31]  base_lr: 2.9135e-04 lr: 2.9135e-04  eta: 0:27:39  time: 0.1400  data_time: 0.0194  memory: 409  loss: 1.2466  loss_cls: 0.2960  loss_bbox: 0.9505
2025/06/09 12:41:05 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:41:05 - mmengine - INFO - Epoch(train)  [48][31/31]  base_lr: 2.9755e-04 lr: 2.9755e-04  eta: 0:27:24  time: 0.1393  data_time: 0.0128  memory: 417  loss: 1.2463  loss_cls: 0.2854  loss_bbox: 0.9608
2025/06/09 12:41:10 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:41:10 - mmengine - INFO - Epoch(train)  [49][31/31]  base_lr: 3.0375e-04 lr: 3.0375e-04  eta: 0:27:09  time: 0.1415  data_time: 0.0237  memory: 413  loss: 1.2490  loss_cls: 0.2696  loss_bbox: 0.9793
2025/06/09 12:41:15 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:41:15 - mmengine - INFO - Epoch(train)  [50][31/31]  base_lr: 3.0996e-04 lr: 3.0996e-04  eta: 0:26:58  time: 0.1538  data_time: 0.0267  memory: 417  loss: 1.2332  loss_cls: 0.2725  loss_bbox: 0.9607
2025/06/09 12:41:15 - mmengine - INFO - Saving checkpoint at 50 epochs
2025/06/09 12:41:19 - mmengine - INFO - Evaluating bbox...
2025/06/09 12:41:20 - mmengine - INFO - bbox_mAP_copypaste: 0.002 0.011 0.000 0.000 0.000 0.004
2025/06/09 12:41:20 - mmengine - INFO - Epoch(val) [50][20/20]    coco/bbox_mAP: 0.0020  coco/bbox_mAP_50: 0.0110  coco/bbox_mAP_75: 0.0000  coco/bbox_mAP_s: 0.0000  coco/bbox_mAP_m: 0.0000  coco/bbox_mAP_l: 0.0040  data_time: 0.0606  time: 0.1038
2025/06/09 12:41:20 - mmengine - INFO - The previous best checkpoint D:\OBJECT_DETECTION\sscma\ModelAssistant\work_dirs\rtmdet_nano_config_motherboard_led_192x192\best_coco_bbox_mAP_epoch_30.pth is removed
2025/06/09 12:41:21 - mmengine - INFO - The best checkpoint with 0.0020 coco/bbox_mAP at 50 epoch is saved to best_coco_bbox_mAP_epoch_50.pth.
2025/06/09 12:41:26 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:41:26 - mmengine - INFO - Epoch(train)  [51][31/31]  base_lr: 3.1616e-04 lr: 3.1616e-04  eta: 0:26:44  time: 0.1502  data_time: 0.0236  memory: 409  loss: 1.2240  loss_cls: 0.2735  loss_bbox: 0.9504
2025/06/09 12:41:31 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:41:31 - mmengine - INFO - Epoch(train)  [52][31/31]  base_lr: 3.2236e-04 lr: 3.2236e-04  eta: 0:26:31  time: 0.1413  data_time: 0.0232  memory: 419  loss: 1.2377  loss_cls: 0.2760  loss_bbox: 0.9617
2025/06/09 12:41:36 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:41:36 - mmengine - INFO - Epoch(train)  [53][31/31]  base_lr: 3.2856e-04 lr: 3.2856e-04  eta: 0:26:19  time: 0.1441  data_time: 0.0259  memory: 411  loss: 1.2615  loss_cls: 0.2707  loss_bbox: 0.9908
2025/06/09 12:41:41 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:41:41 - mmengine - INFO - Epoch(train)  [54][31/31]  base_lr: 3.3477e-04 lr: 3.3477e-04  eta: 0:26:06  time: 0.1437  data_time: 0.0176  memory: 415  loss: 1.2893  loss_cls: 0.2842  loss_bbox: 1.0051
2025/06/09 12:41:46 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:41:46 - mmengine - INFO - Epoch(train)  [55][31/31]  base_lr: 3.4097e-04 lr: 3.4097e-04  eta: 0:25:54  time: 0.1398  data_time: 0.0215  memory: 420  loss: 1.3584  loss_cls: 0.3223  loss_bbox: 1.0361
2025/06/09 12:41:51 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:41:51 - mmengine - INFO - Epoch(train)  [56][31/31]  base_lr: 3.4717e-04 lr: 3.4717e-04  eta: 0:25:43  time: 0.1465  data_time: 0.0188  memory: 420  loss: 1.3072  loss_cls: 0.3038  loss_bbox: 1.0033
2025/06/09 12:41:56 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:41:56 - mmengine - INFO - Epoch(train)  [57][31/31]  base_lr: 3.5338e-04 lr: 3.5338e-04  eta: 0:25:30  time: 0.1450  data_time: 0.0224  memory: 423  loss: 1.2629  loss_cls: 0.3062  loss_bbox: 0.9567
2025/06/09 12:42:02 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:42:02 - mmengine - INFO - Epoch(train)  [58][31/31]  base_lr: 3.5958e-04 lr: 3.5958e-04  eta: 0:25:19  time: 0.1422  data_time: 0.0266  memory: 408  loss: 1.2158  loss_cls: 0.3133  loss_bbox: 0.9025
2025/06/09 12:42:07 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:42:07 - mmengine - INFO - Epoch(train)  [59][31/31]  base_lr: 3.6578e-04 lr: 3.6578e-04  eta: 0:25:09  time: 0.1495  data_time: 0.0232  memory: 415  loss: 1.1855  loss_cls: 0.3243  loss_bbox: 0.8612
2025/06/09 12:42:12 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:42:12 - mmengine - INFO - Epoch(train)  [60][31/31]  base_lr: 3.7199e-04 lr: 3.7199e-04  eta: 0:24:59  time: 0.1507  data_time: 0.0296  memory: 414  loss: 1.1637  loss_cls: 0.3335  loss_bbox: 0.8302
2025/06/09 12:42:12 - mmengine - INFO - Saving checkpoint at 60 epochs
2025/06/09 12:42:15 - mmengine - INFO - Evaluating bbox...
2025/06/09 12:42:16 - mmengine - INFO - bbox_mAP_copypaste: 0.033 0.101 0.012 0.000 0.000 0.066
2025/06/09 12:42:16 - mmengine - INFO - Epoch(val) [60][20/20]    coco/bbox_mAP: 0.0330  coco/bbox_mAP_50: 0.1010  coco/bbox_mAP_75: 0.0120  coco/bbox_mAP_s: 0.0000  coco/bbox_mAP_m: 0.0000  coco/bbox_mAP_l: 0.0660  data_time: 0.0169  time: 0.0591
2025/06/09 12:42:16 - mmengine - INFO - The previous best checkpoint D:\OBJECT_DETECTION\sscma\ModelAssistant\work_dirs\rtmdet_nano_config_motherboard_led_192x192\best_coco_bbox_mAP_epoch_50.pth is removed
2025/06/09 12:42:17 - mmengine - INFO - The best checkpoint with 0.0330 coco/bbox_mAP at 60 epoch is saved to best_coco_bbox_mAP_epoch_60.pth.
2025/06/09 12:42:22 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:42:22 - mmengine - INFO - Epoch(train)  [61][31/31]  base_lr: 3.7819e-04 lr: 3.7819e-04  eta: 0:24:48  time: 0.1426  data_time: 0.0211  memory: 406  loss: 1.1399  loss_cls: 0.3390  loss_bbox: 0.8010
2025/06/09 12:42:27 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:42:27 - mmengine - INFO - Epoch(train)  [62][31/31]  base_lr: 3.8439e-04 lr: 3.8439e-04  eta: 0:24:38  time: 0.1421  data_time: 0.0278  memory: 414  loss: 1.1295  loss_cls: 0.3464  loss_bbox: 0.7831
2025/06/09 12:42:32 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:42:32 - mmengine - INFO - Epoch(train)  [63][31/31]  base_lr: 3.9060e-04 lr: 3.9060e-04  eta: 0:24:28  time: 0.1456  data_time: 0.0173  memory: 419  loss: 1.1167  loss_cls: 0.3462  loss_bbox: 0.7705
2025/06/09 12:42:37 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:42:37 - mmengine - INFO - Epoch(train)  [64][31/31]  base_lr: 3.9680e-04 lr: 3.9680e-04  eta: 0:24:17  time: 0.1390  data_time: 0.0169  memory: 413  loss: 1.1089  loss_cls: 0.3475  loss_bbox: 0.7614
2025/06/09 12:42:40 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:42:42 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:42:42 - mmengine - INFO - Epoch(train)  [65][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:24:06  time: 0.1389  data_time: 0.0189  memory: 414  loss: 1.1038  loss_cls: 0.3538  loss_bbox: 0.7500
2025/06/09 12:42:47 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:42:47 - mmengine - INFO - Epoch(train)  [66][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:23:56  time: 0.1405  data_time: 0.0212  memory: 417  loss: 1.0961  loss_cls: 0.3528  loss_bbox: 0.7433
2025/06/09 12:42:52 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:42:52 - mmengine - INFO - Epoch(train)  [67][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:23:45  time: 0.1386  data_time: 0.0160  memory: 409  loss: 1.0952  loss_cls: 0.3536  loss_bbox: 0.7417
2025/06/09 12:42:57 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:42:57 - mmengine - INFO - Epoch(train)  [68][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:23:35  time: 0.1408  data_time: 0.0186  memory: 417  loss: 1.0831  loss_cls: 0.3479  loss_bbox: 0.7352
2025/06/09 12:43:02 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:43:02 - mmengine - INFO - Epoch(train)  [69][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:23:25  time: 0.1422  data_time: 0.0277  memory: 417  loss: 1.0781  loss_cls: 0.3472  loss_bbox: 0.7308
2025/06/09 12:43:07 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:43:07 - mmengine - INFO - Epoch(train)  [70][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:23:16  time: 0.1414  data_time: 0.0251  memory: 413  loss: 1.0778  loss_cls: 0.3489  loss_bbox: 0.7289
2025/06/09 12:43:07 - mmengine - INFO - Saving checkpoint at 70 epochs
2025/06/09 12:43:10 - mmengine - INFO - Evaluating bbox...
2025/06/09 12:43:11 - mmengine - INFO - bbox_mAP_copypaste: 0.048 0.134 0.017 0.000 0.000 0.097
2025/06/09 12:43:11 - mmengine - INFO - Epoch(val) [70][20/20]    coco/bbox_mAP: 0.0480  coco/bbox_mAP_50: 0.1340  coco/bbox_mAP_75: 0.0170  coco/bbox_mAP_s: 0.0000  coco/bbox_mAP_m: 0.0000  coco/bbox_mAP_l: 0.0970  data_time: 0.0145  time: 0.0672
2025/06/09 12:43:11 - mmengine - INFO - The previous best checkpoint D:\OBJECT_DETECTION\sscma\ModelAssistant\work_dirs\rtmdet_nano_config_motherboard_led_192x192\best_coco_bbox_mAP_epoch_60.pth is removed
2025/06/09 12:43:11 - mmengine - INFO - The best checkpoint with 0.0480 coco/bbox_mAP at 70 epoch is saved to best_coco_bbox_mAP_epoch_70.pth.
2025/06/09 12:43:17 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:43:17 - mmengine - INFO - Epoch(train)  [71][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:23:06  time: 0.1386  data_time: 0.0213  memory: 415  loss: 1.0646  loss_cls: 0.3478  loss_bbox: 0.7168
2025/06/09 12:43:21 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:43:21 - mmengine - INFO - Epoch(train)  [72][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:22:56  time: 0.1362  data_time: 0.0169  memory: 419  loss: 1.0609  loss_cls: 0.3483  loss_bbox: 0.7126
2025/06/09 12:43:26 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:43:26 - mmengine - INFO - Epoch(train)  [73][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:22:46  time: 0.1401  data_time: 0.0237  memory: 408  loss: 1.0593  loss_cls: 0.3496  loss_bbox: 0.7097
2025/06/09 12:43:31 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:43:31 - mmengine - INFO - Epoch(train)  [74][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:22:37  time: 0.1397  data_time: 0.0185  memory: 417  loss: 1.0538  loss_cls: 0.3467  loss_bbox: 0.7071
2025/06/09 12:43:36 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:43:36 - mmengine - INFO - Epoch(train)  [75][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:22:28  time: 0.1393  data_time: 0.0222  memory: 414  loss: 1.0422  loss_cls: 0.3470  loss_bbox: 0.6952
2025/06/09 12:43:41 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:43:41 - mmengine - INFO - Epoch(train)  [76][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:22:19  time: 0.1393  data_time: 0.0251  memory: 417  loss: 1.0382  loss_cls: 0.3419  loss_bbox: 0.6963
2025/06/09 12:43:46 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:43:46 - mmengine - INFO - Epoch(train)  [77][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:22:10  time: 0.1394  data_time: 0.0187  memory: 415  loss: 1.0387  loss_cls: 0.3490  loss_bbox: 0.6897
2025/06/09 12:43:51 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:43:51 - mmengine - INFO - Epoch(train)  [78][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:22:01  time: 0.1420  data_time: 0.0209  memory: 417  loss: 1.0348  loss_cls: 0.3618  loss_bbox: 0.6730
2025/06/09 12:43:56 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:43:56 - mmengine - INFO - Epoch(train)  [79][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:21:52  time: 0.1365  data_time: 0.0166  memory: 413  loss: 1.0291  loss_cls: 0.3655  loss_bbox: 0.6636
2025/06/09 12:44:01 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:44:01 - mmengine - INFO - Epoch(train)  [80][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:21:43  time: 0.1361  data_time: 0.0140  memory: 414  loss: 1.0310  loss_cls: 0.3640  loss_bbox: 0.6670
2025/06/09 12:44:01 - mmengine - INFO - Saving checkpoint at 80 epochs
2025/06/09 12:44:03 - mmengine - INFO - Evaluating bbox...
2025/06/09 12:44:05 - mmengine - INFO - bbox_mAP_copypaste: 0.065 0.174 0.025 0.000 0.000 0.130
2025/06/09 12:44:05 - mmengine - INFO - Epoch(val) [80][20/20]    coco/bbox_mAP: 0.0650  coco/bbox_mAP_50: 0.1740  coco/bbox_mAP_75: 0.0250  coco/bbox_mAP_s: 0.0000  coco/bbox_mAP_m: 0.0000  coco/bbox_mAP_l: 0.1300  data_time: 0.0141  time: 0.0578
2025/06/09 12:44:05 - mmengine - INFO - The previous best checkpoint D:\OBJECT_DETECTION\sscma\ModelAssistant\work_dirs\rtmdet_nano_config_motherboard_led_192x192\best_coco_bbox_mAP_epoch_70.pth is removed
2025/06/09 12:44:05 - mmengine - INFO - The best checkpoint with 0.0650 coco/bbox_mAP at 80 epoch is saved to best_coco_bbox_mAP_epoch_80.pth.
2025/06/09 12:44:10 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:44:10 - mmengine - INFO - Epoch(train)  [81][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:21:34  time: 0.1400  data_time: 0.0246  memory: 417  loss: 1.0158  loss_cls: 0.3589  loss_bbox: 0.6570
2025/06/09 12:44:15 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:44:15 - mmengine - INFO - Epoch(train)  [82][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:21:26  time: 0.1408  data_time: 0.0214  memory: 422  loss: 1.0110  loss_cls: 0.3606  loss_bbox: 0.6504
2025/06/09 12:44:20 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:44:20 - mmengine - INFO - Epoch(train)  [83][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:21:17  time: 0.1380  data_time: 0.0183  memory: 414  loss: 1.0077  loss_cls: 0.3610  loss_bbox: 0.6467
2025/06/09 12:44:25 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:44:25 - mmengine - INFO - Epoch(train)  [84][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:21:09  time: 0.1435  data_time: 0.0313  memory: 422  loss: 0.9941  loss_cls: 0.3543  loss_bbox: 0.6399
2025/06/09 12:44:30 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:44:30 - mmengine - INFO - Epoch(train)  [85][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:21:01  time: 0.1408  data_time: 0.0221  memory: 413  loss: 0.9973  loss_cls: 0.3538  loss_bbox: 0.6435
2025/06/09 12:44:35 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:44:35 - mmengine - INFO - Epoch(train)  [86][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:20:53  time: 0.1397  data_time: 0.0230  memory: 411  loss: 0.9963  loss_cls: 0.3561  loss_bbox: 0.6403
2025/06/09 12:44:40 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:44:40 - mmengine - INFO - Epoch(train)  [87][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:20:45  time: 0.1399  data_time: 0.0225  memory: 419  loss: 0.9843  loss_cls: 0.3527  loss_bbox: 0.6315
2025/06/09 12:44:45 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:44:45 - mmengine - INFO - Epoch(train)  [88][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:20:37  time: 0.1420  data_time: 0.0285  memory: 422  loss: 0.9811  loss_cls: 0.3510  loss_bbox: 0.6300
2025/06/09 12:44:50 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:44:50 - mmengine - INFO - Epoch(train)  [89][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:20:29  time: 0.1362  data_time: 0.0176  memory: 414  loss: 0.9739  loss_cls: 0.3494  loss_bbox: 0.6246
2025/06/09 12:44:55 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:44:55 - mmengine - INFO - Epoch(train)  [90][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:20:21  time: 0.1411  data_time: 0.0274  memory: 418  loss: 0.9728  loss_cls: 0.3494  loss_bbox: 0.6234
2025/06/09 12:44:55 - mmengine - INFO - Saving checkpoint at 90 epochs
2025/06/09 12:44:58 - mmengine - INFO - Evaluating bbox...
2025/06/09 12:44:59 - mmengine - INFO - bbox_mAP_copypaste: 0.095 0.247 0.048 0.000 0.000 0.191
2025/06/09 12:44:59 - mmengine - INFO - Epoch(val) [90][20/20]    coco/bbox_mAP: 0.0950  coco/bbox_mAP_50: 0.2470  coco/bbox_mAP_75: 0.0480  coco/bbox_mAP_s: 0.0000  coco/bbox_mAP_m: 0.0000  coco/bbox_mAP_l: 0.1910  data_time: 0.0210  time: 0.0641
2025/06/09 12:44:59 - mmengine - INFO - The previous best checkpoint D:\OBJECT_DETECTION\sscma\ModelAssistant\work_dirs\rtmdet_nano_config_motherboard_led_192x192\best_coco_bbox_mAP_epoch_80.pth is removed
2025/06/09 12:45:00 - mmengine - INFO - The best checkpoint with 0.0950 coco/bbox_mAP at 90 epoch is saved to best_coco_bbox_mAP_epoch_90.pth.
2025/06/09 12:45:06 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:45:06 - mmengine - INFO - Epoch(train)  [91][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:20:14  time: 0.1481  data_time: 0.0259  memory: 414  loss: 0.9699  loss_cls: 0.3442  loss_bbox: 0.6258
2025/06/09 12:45:11 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:45:11 - mmengine - INFO - Epoch(train)  [92][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:20:07  time: 0.1483  data_time: 0.0141  memory: 417  loss: 0.9705  loss_cls: 0.3417  loss_bbox: 0.6288
2025/06/09 12:45:15 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:45:15 - mmengine - INFO - Epoch(train)  [93][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:19:59  time: 0.1391  data_time: 0.0145  memory: 411  loss: 0.9637  loss_cls: 0.3402  loss_bbox: 0.6235
2025/06/09 12:45:20 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:45:20 - mmengine - INFO - Epoch(train)  [94][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:19:51  time: 0.1406  data_time: 0.0194  memory: 422  loss: 0.9630  loss_cls: 0.3431  loss_bbox: 0.6199
2025/06/09 12:45:25 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:45:25 - mmengine - INFO - Epoch(train)  [95][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:19:44  time: 0.1406  data_time: 0.0228  memory: 408  loss: 0.9690  loss_cls: 0.3435  loss_bbox: 0.6255
2025/06/09 12:45:30 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:45:30 - mmengine - INFO - Epoch(train)  [96][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:19:36  time: 0.1396  data_time: 0.0212  memory: 409  loss: 0.9596  loss_cls: 0.3416  loss_bbox: 0.6181
2025/06/09 12:45:34 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:45:35 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:45:35 - mmengine - INFO - Epoch(train)  [97][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:19:29  time: 0.1424  data_time: 0.0172  memory: 414  loss: 0.9525  loss_cls: 0.3423  loss_bbox: 0.6102
2025/06/09 12:45:40 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:45:40 - mmengine - INFO - Epoch(train)  [98][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:19:21  time: 0.1398  data_time: 0.0128  memory: 418  loss: 0.9516  loss_cls: 0.3401  loss_bbox: 0.6115
2025/06/09 12:45:45 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:45:45 - mmengine - INFO - Epoch(train)  [99][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:19:14  time: 0.1404  data_time: 0.0123  memory: 425  loss: 0.9584  loss_cls: 0.3419  loss_bbox: 0.6165
2025/06/09 12:45:50 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:45:50 - mmengine - INFO - Epoch(train) [100][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:19:06  time: 0.1417  data_time: 0.0220  memory: 418  loss: 0.9495  loss_cls: 0.3409  loss_bbox: 0.6087
2025/06/09 12:45:50 - mmengine - INFO - Saving checkpoint at 100 epochs
2025/06/09 12:45:53 - mmengine - INFO - Evaluating bbox...
2025/06/09 12:45:55 - mmengine - INFO - bbox_mAP_copypaste: 0.100 0.256 0.063 0.000 0.000 0.202
2025/06/09 12:45:55 - mmengine - INFO - Epoch(val) [100][20/20]    coco/bbox_mAP: 0.1000  coco/bbox_mAP_50: 0.2560  coco/bbox_mAP_75: 0.0630  coco/bbox_mAP_s: 0.0000  coco/bbox_mAP_m: 0.0000  coco/bbox_mAP_l: 0.2020  data_time: 0.0554  time: 0.0975
2025/06/09 12:45:55 - mmengine - INFO - The previous best checkpoint D:\OBJECT_DETECTION\sscma\ModelAssistant\work_dirs\rtmdet_nano_config_motherboard_led_192x192\best_coco_bbox_mAP_epoch_90.pth is removed
2025/06/09 12:45:55 - mmengine - INFO - The best checkpoint with 0.1000 coco/bbox_mAP at 100 epoch is saved to best_coco_bbox_mAP_epoch_100.pth.
2025/06/09 12:46:01 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:46:01 - mmengine - INFO - Epoch(train) [101][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:19:00  time: 0.1459  data_time: 0.0295  memory: 426  loss: 0.9471  loss_cls: 0.3411  loss_bbox: 0.6060
2025/06/09 12:46:06 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:46:06 - mmengine - INFO - Epoch(train) [102][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:18:53  time: 0.1449  data_time: 0.0171  memory: 423  loss: 0.9408  loss_cls: 0.3374  loss_bbox: 0.6033
2025/06/09 12:46:11 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:46:11 - mmengine - INFO - Epoch(train) [103][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:18:45  time: 0.1426  data_time: 0.0181  memory: 419  loss: 0.9437  loss_cls: 0.3402  loss_bbox: 0.6035
2025/06/09 12:46:16 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:46:16 - mmengine - INFO - Epoch(train) [104][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:18:38  time: 0.1434  data_time: 0.0269  memory: 411  loss: 0.9440  loss_cls: 0.3370  loss_bbox: 0.6070
2025/06/09 12:46:21 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:46:21 - mmengine - INFO - Epoch(train) [105][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:18:31  time: 0.1398  data_time: 0.0138  memory: 417  loss: 0.9368  loss_cls: 0.3338  loss_bbox: 0.6030
2025/06/09 12:46:25 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:46:25 - mmengine - INFO - Epoch(train) [106][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:18:24  time: 0.1384  data_time: 0.0150  memory: 417  loss: 0.9320  loss_cls: 0.3383  loss_bbox: 0.5937
2025/06/09 12:46:30 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:46:30 - mmengine - INFO - Epoch(train) [107][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:18:17  time: 0.1383  data_time: 0.0198  memory: 414  loss: 0.9309  loss_cls: 0.3355  loss_bbox: 0.5954
2025/06/09 12:46:35 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:46:35 - mmengine - INFO - Epoch(train) [108][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:18:10  time: 0.1399  data_time: 0.0136  memory: 411  loss: 0.9275  loss_cls: 0.3368  loss_bbox: 0.5907
2025/06/09 12:46:40 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:46:40 - mmengine - INFO - Epoch(train) [109][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:18:03  time: 0.1406  data_time: 0.0187  memory: 411  loss: 0.9232  loss_cls: 0.3341  loss_bbox: 0.5892
2025/06/09 12:46:45 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:46:45 - mmengine - INFO - Epoch(train) [110][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:17:56  time: 0.1454  data_time: 0.0188  memory: 414  loss: 0.9265  loss_cls: 0.3341  loss_bbox: 0.5924
2025/06/09 12:46:45 - mmengine - INFO - Saving checkpoint at 110 epochs
2025/06/09 12:46:48 - mmengine - INFO - Evaluating bbox...
2025/06/09 12:46:49 - mmengine - INFO - bbox_mAP_copypaste: 0.129 0.308 0.083 0.000 0.000 0.260
2025/06/09 12:46:49 - mmengine - INFO - Epoch(val) [110][20/20]    coco/bbox_mAP: 0.1290  coco/bbox_mAP_50: 0.3080  coco/bbox_mAP_75: 0.0830  coco/bbox_mAP_s: 0.0000  coco/bbox_mAP_m: 0.0000  coco/bbox_mAP_l: 0.2600  data_time: 0.0140  time: 0.0665
2025/06/09 12:46:49 - mmengine - INFO - The previous best checkpoint D:\OBJECT_DETECTION\sscma\ModelAssistant\work_dirs\rtmdet_nano_config_motherboard_led_192x192\best_coco_bbox_mAP_epoch_100.pth is removed
2025/06/09 12:46:50 - mmengine - INFO - The best checkpoint with 0.1290 coco/bbox_mAP at 110 epoch is saved to best_coco_bbox_mAP_epoch_110.pth.
2025/06/09 12:46:55 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:46:55 - mmengine - INFO - Epoch(train) [111][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:17:49  time: 0.1421  data_time: 0.0165  memory: 427  loss: 0.9266  loss_cls: 0.3384  loss_bbox: 0.5882
2025/06/09 12:47:00 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:47:00 - mmengine - INFO - Epoch(train) [112][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:17:42  time: 0.1391  data_time: 0.0160  memory: 408  loss: 0.9247  loss_cls: 0.3387  loss_bbox: 0.5860
2025/06/09 12:47:05 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:47:05 - mmengine - INFO - Epoch(train) [113][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:17:36  time: 0.1415  data_time: 0.0236  memory: 414  loss: 0.9203  loss_cls: 0.3351  loss_bbox: 0.5853
2025/06/09 12:47:10 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:47:10 - mmengine - INFO - Epoch(train) [114][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:17:29  time: 0.1412  data_time: 0.0119  memory: 419  loss: 0.9144  loss_cls: 0.3332  loss_bbox: 0.5811
2025/06/09 12:47:15 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:47:15 - mmengine - INFO - Epoch(train) [115][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:17:22  time: 0.1426  data_time: 0.0202  memory: 409  loss: 0.9147  loss_cls: 0.3326  loss_bbox: 0.5820
2025/06/09 12:47:20 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:47:20 - mmengine - INFO - Epoch(train) [116][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:17:16  time: 0.1424  data_time: 0.0221  memory: 417  loss: 0.9124  loss_cls: 0.3301  loss_bbox: 0.5823
2025/06/09 12:47:25 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:47:25 - mmengine - INFO - Epoch(train) [117][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:17:09  time: 0.1389  data_time: 0.0155  memory: 411  loss: 0.9146  loss_cls: 0.3314  loss_bbox: 0.5832
2025/06/09 12:47:30 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:47:30 - mmengine - INFO - Epoch(train) [118][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:17:02  time: 0.1408  data_time: 0.0150  memory: 414  loss: 0.9085  loss_cls: 0.3318  loss_bbox: 0.5767
2025/06/09 12:47:35 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:47:35 - mmengine - INFO - Epoch(train) [119][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:16:56  time: 0.1491  data_time: 0.0145  memory: 419  loss: 0.9112  loss_cls: 0.3340  loss_bbox: 0.5772
2025/06/09 12:47:40 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:47:40 - mmengine - INFO - Epoch(train) [120][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:16:49  time: 0.1442  data_time: 0.0160  memory: 422  loss: 0.9079  loss_cls: 0.3315  loss_bbox: 0.5764
2025/06/09 12:47:40 - mmengine - INFO - Saving checkpoint at 120 epochs
2025/06/09 12:47:42 - mmengine - INFO - Evaluating bbox...
2025/06/09 12:47:44 - mmengine - INFO - bbox_mAP_copypaste: 0.148 0.308 0.149 0.000 0.000 0.297
2025/06/09 12:47:44 - mmengine - INFO - Epoch(val) [120][20/20]    coco/bbox_mAP: 0.1480  coco/bbox_mAP_50: 0.3080  coco/bbox_mAP_75: 0.1490  coco/bbox_mAP_s: 0.0000  coco/bbox_mAP_m: 0.0000  coco/bbox_mAP_l: 0.2970  data_time: 0.0141  time: 0.0562
2025/06/09 12:47:44 - mmengine - INFO - The previous best checkpoint D:\OBJECT_DETECTION\sscma\ModelAssistant\work_dirs\rtmdet_nano_config_motherboard_led_192x192\best_coco_bbox_mAP_epoch_110.pth is removed
2025/06/09 12:47:44 - mmengine - INFO - The best checkpoint with 0.1480 coco/bbox_mAP at 120 epoch is saved to best_coco_bbox_mAP_epoch_120.pth.
2025/06/09 12:47:50 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:47:50 - mmengine - INFO - Epoch(train) [121][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:16:43  time: 0.1423  data_time: 0.0203  memory: 417  loss: 0.9049  loss_cls: 0.3290  loss_bbox: 0.5759
2025/06/09 12:47:55 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:47:55 - mmengine - INFO - Epoch(train) [122][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:16:37  time: 0.1457  data_time: 0.0144  memory: 409  loss: 0.9026  loss_cls: 0.3288  loss_bbox: 0.5738
2025/06/09 12:48:00 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:48:00 - mmengine - INFO - Epoch(train) [123][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:16:30  time: 0.1478  data_time: 0.0141  memory: 409  loss: 0.8950  loss_cls: 0.3344  loss_bbox: 0.5606
2025/06/09 12:48:05 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:48:05 - mmengine - INFO - Epoch(train) [124][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:16:24  time: 0.1447  data_time: 0.0259  memory: 418  loss: 0.8940  loss_cls: 0.3321  loss_bbox: 0.5619
2025/06/09 12:48:10 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:48:10 - mmengine - INFO - Epoch(train) [125][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:16:18  time: 0.1406  data_time: 0.0189  memory: 409  loss: 0.8971  loss_cls: 0.3337  loss_bbox: 0.5634
2025/06/09 12:48:15 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:48:15 - mmengine - INFO - Epoch(train) [126][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:16:11  time: 0.1436  data_time: 0.0182  memory: 408  loss: 0.8948  loss_cls: 0.3328  loss_bbox: 0.5620
2025/06/09 12:48:20 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:48:20 - mmengine - INFO - Epoch(train) [127][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:16:05  time: 0.1463  data_time: 0.0201  memory: 425  loss: 0.8928  loss_cls: 0.3297  loss_bbox: 0.5631
2025/06/09 12:48:26 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:48:26 - mmengine - INFO - Epoch(train) [128][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:15:59  time: 0.1508  data_time: 0.0216  memory: 427  loss: 0.8913  loss_cls: 0.3296  loss_bbox: 0.5617
2025/06/09 12:48:31 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:48:31 - mmengine - INFO - Epoch(train) [129][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:15:53  time: 0.1491  data_time: 0.0186  memory: 436  loss: 0.8962  loss_cls: 0.3338  loss_bbox: 0.5623
2025/06/09 12:48:32 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:48:36 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:48:36 - mmengine - INFO - Epoch(train) [130][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:15:47  time: 0.1444  data_time: 0.0272  memory: 417  loss: 0.8899  loss_cls: 0.3339  loss_bbox: 0.5559
2025/06/09 12:48:36 - mmengine - INFO - Saving checkpoint at 130 epochs
2025/06/09 12:48:38 - mmengine - INFO - Evaluating bbox...
2025/06/09 12:48:40 - mmengine - INFO - bbox_mAP_copypaste: 0.170 0.337 0.162 0.000 0.000 0.343
2025/06/09 12:48:40 - mmengine - INFO - Epoch(val) [130][20/20]    coco/bbox_mAP: 0.1700  coco/bbox_mAP_50: 0.3370  coco/bbox_mAP_75: 0.1620  coco/bbox_mAP_s: 0.0000  coco/bbox_mAP_m: 0.0000  coco/bbox_mAP_l: 0.3430  data_time: 0.0142  time: 0.0711
2025/06/09 12:48:40 - mmengine - INFO - The previous best checkpoint D:\OBJECT_DETECTION\sscma\ModelAssistant\work_dirs\rtmdet_nano_config_motherboard_led_192x192\best_coco_bbox_mAP_epoch_120.pth is removed
2025/06/09 12:48:40 - mmengine - INFO - The best checkpoint with 0.1700 coco/bbox_mAP at 130 epoch is saved to best_coco_bbox_mAP_epoch_130.pth.
2025/06/09 12:48:46 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:48:46 - mmengine - INFO - Epoch(train) [131][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:15:40  time: 0.1367  data_time: 0.0153  memory: 417  loss: 0.8930  loss_cls: 0.3320  loss_bbox: 0.5610
2025/06/09 12:48:51 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:48:51 - mmengine - INFO - Epoch(train) [132][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:15:34  time: 0.1434  data_time: 0.0162  memory: 408  loss: 0.8827  loss_cls: 0.3301  loss_bbox: 0.5526
2025/06/09 12:48:56 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:48:56 - mmengine - INFO - Epoch(train) [133][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:15:28  time: 0.1413  data_time: 0.0166  memory: 413  loss: 0.8782  loss_cls: 0.3290  loss_bbox: 0.5492
2025/06/09 12:49:00 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:49:00 - mmengine - INFO - Epoch(train) [134][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:15:21  time: 0.1406  data_time: 0.0172  memory: 414  loss: 0.8791  loss_cls: 0.3269  loss_bbox: 0.5522
2025/06/09 12:49:06 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:49:06 - mmengine - INFO - Epoch(train) [135][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:15:15  time: 0.1449  data_time: 0.0231  memory: 419  loss: 0.8708  loss_cls: 0.3233  loss_bbox: 0.5474
2025/06/09 12:49:11 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:49:11 - mmengine - INFO - Epoch(train) [136][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:15:09  time: 0.1505  data_time: 0.0277  memory: 409  loss: 0.8746  loss_cls: 0.3258  loss_bbox: 0.5488
2025/06/09 12:49:16 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:49:16 - mmengine - INFO - Epoch(train) [137][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:15:03  time: 0.1439  data_time: 0.0295  memory: 417  loss: 0.8660  loss_cls: 0.3188  loss_bbox: 0.5472
2025/06/09 12:49:21 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:49:21 - mmengine - INFO - Epoch(train) [138][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:14:57  time: 0.1389  data_time: 0.0136  memory: 414  loss: 0.8767  loss_cls: 0.3259  loss_bbox: 0.5508
2025/06/09 12:49:26 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:49:26 - mmengine - INFO - Epoch(train) [139][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:14:51  time: 0.1388  data_time: 0.0157  memory: 411  loss: 0.8720  loss_cls: 0.3226  loss_bbox: 0.5494
2025/06/09 12:49:31 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:49:31 - mmengine - INFO - Epoch(train) [140][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:14:45  time: 0.1395  data_time: 0.0166  memory: 411  loss: 0.8685  loss_cls: 0.3209  loss_bbox: 0.5475
2025/06/09 12:49:31 - mmengine - INFO - Saving checkpoint at 140 epochs
2025/06/09 12:49:33 - mmengine - INFO - Evaluating bbox...
2025/06/09 12:49:35 - mmengine - INFO - bbox_mAP_copypaste: 0.182 0.363 0.160 0.000 0.000 0.366
2025/06/09 12:49:35 - mmengine - INFO - Epoch(val) [140][20/20]    coco/bbox_mAP: 0.1820  coco/bbox_mAP_50: 0.3630  coco/bbox_mAP_75: 0.1600  coco/bbox_mAP_s: 0.0000  coco/bbox_mAP_m: 0.0000  coco/bbox_mAP_l: 0.3660  data_time: 0.0213  time: 0.0648
2025/06/09 12:49:35 - mmengine - INFO - The previous best checkpoint D:\OBJECT_DETECTION\sscma\ModelAssistant\work_dirs\rtmdet_nano_config_motherboard_led_192x192\best_coco_bbox_mAP_epoch_130.pth is removed
2025/06/09 12:49:35 - mmengine - INFO - The best checkpoint with 0.1820 coco/bbox_mAP at 140 epoch is saved to best_coco_bbox_mAP_epoch_140.pth.
2025/06/09 12:49:40 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:49:40 - mmengine - INFO - Epoch(train) [141][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:14:38  time: 0.1417  data_time: 0.0150  memory: 417  loss: 0.8646  loss_cls: 0.3224  loss_bbox: 0.5422
2025/06/09 12:49:45 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:49:45 - mmengine - INFO - Epoch(train) [142][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:14:32  time: 0.1404  data_time: 0.0174  memory: 425  loss: 0.8624  loss_cls: 0.3229  loss_bbox: 0.5395
2025/06/09 12:49:50 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:49:50 - mmengine - INFO - Epoch(train) [143][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:14:26  time: 0.1419  data_time: 0.0231  memory: 408  loss: 0.8636  loss_cls: 0.3227  loss_bbox: 0.5410
2025/06/09 12:49:55 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:49:55 - mmengine - INFO - Epoch(train) [144][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:14:20  time: 0.1422  data_time: 0.0250  memory: 418  loss: 0.8673  loss_cls: 0.3236  loss_bbox: 0.5437
2025/06/09 12:50:00 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:50:00 - mmengine - INFO - Epoch(train) [145][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:14:14  time: 0.1401  data_time: 0.0249  memory: 411  loss: 0.8531  loss_cls: 0.3205  loss_bbox: 0.5326
2025/06/09 12:50:05 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:50:05 - mmengine - INFO - Epoch(train) [146][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:14:08  time: 0.1409  data_time: 0.0262  memory: 417  loss: 0.8561  loss_cls: 0.3255  loss_bbox: 0.5306
2025/06/09 12:50:10 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:50:10 - mmengine - INFO - Epoch(train) [147][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:14:02  time: 0.1441  data_time: 0.0231  memory: 415  loss: 0.8592  loss_cls: 0.3266  loss_bbox: 0.5326
2025/06/09 12:50:16 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:50:16 - mmengine - INFO - Epoch(train) [148][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:13:56  time: 0.1476  data_time: 0.0130  memory: 425  loss: 0.8654  loss_cls: 0.3310  loss_bbox: 0.5344
2025/06/09 12:50:21 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:50:21 - mmengine - INFO - Epoch(train) [149][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:13:50  time: 0.1471  data_time: 0.0170  memory: 420  loss: 0.8599  loss_cls: 0.3322  loss_bbox: 0.5277
2025/06/09 12:50:26 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:50:26 - mmengine - INFO - Epoch(train) [150][31/31]  base_lr: 4.0000e-04 lr: 4.0000e-04  eta: 0:13:44  time: 0.1416  data_time: 0.0118  memory: 417  loss: 0.8534  loss_cls: 0.3284  loss_bbox: 0.5249
2025/06/09 12:50:26 - mmengine - INFO - Saving checkpoint at 150 epochs
2025/06/09 12:50:29 - mmengine - INFO - Evaluating bbox...
2025/06/09 12:50:30 - mmengine - INFO - bbox_mAP_copypaste: 0.190 0.364 0.167 0.000 0.000 0.382
2025/06/09 12:50:31 - mmengine - INFO - Epoch(val) [150][20/20]    coco/bbox_mAP: 0.1900  coco/bbox_mAP_50: 0.3640  coco/bbox_mAP_75: 0.1670  coco/bbox_mAP_s: 0.0000  coco/bbox_mAP_m: 0.0000  coco/bbox_mAP_l: 0.3820  data_time: 0.0549  time: 0.0970
2025/06/09 12:50:31 - mmengine - INFO - The previous best checkpoint D:\OBJECT_DETECTION\sscma\ModelAssistant\work_dirs\rtmdet_nano_config_motherboard_led_192x192\best_coco_bbox_mAP_epoch_140.pth is removed
2025/06/09 12:50:31 - mmengine - INFO - The best checkpoint with 0.1900 coco/bbox_mAP at 150 epoch is saved to best_coco_bbox_mAP_epoch_150.pth.
2025/06/09 12:50:36 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:50:36 - mmengine - INFO - Epoch(train) [151][31/31]  base_lr: 3.9996e-04 lr: 3.9996e-04  eta: 0:13:38  time: 0.1422  data_time: 0.0241  memory: 419  loss: 0.8512  loss_cls: 0.3262  loss_bbox: 0.5250
2025/06/09 12:50:41 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:50:41 - mmengine - INFO - Epoch(train) [152][31/31]  base_lr: 3.9984e-04 lr: 3.9984e-04  eta: 0:13:32  time: 0.1412  data_time: 0.0164  memory: 419  loss: 0.8583  loss_cls: 0.3305  loss_bbox: 0.5278
2025/06/09 12:50:46 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:50:46 - mmengine - INFO - Epoch(train) [153][31/31]  base_lr: 3.9964e-04 lr: 3.9964e-04  eta: 0:13:26  time: 0.1439  data_time: 0.0169  memory: 427  loss: 0.8433  loss_cls: 0.3223  loss_bbox: 0.5210
2025/06/09 12:50:51 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:50:51 - mmengine - INFO - Epoch(train) [154][31/31]  base_lr: 3.9935e-04 lr: 3.9935e-04  eta: 0:13:20  time: 0.1435  data_time: 0.0223  memory: 408  loss: 0.8444  loss_cls: 0.3249  loss_bbox: 0.5195
2025/06/09 12:50:57 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:50:57 - mmengine - INFO - Epoch(train) [155][31/31]  base_lr: 3.9899e-04 lr: 3.9899e-04  eta: 0:13:14  time: 0.1438  data_time: 0.0229  memory: 417  loss: 0.8545  loss_cls: 0.3290  loss_bbox: 0.5255
2025/06/09 12:51:02 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:51:02 - mmengine - INFO - Epoch(train) [156][31/31]  base_lr: 3.9854e-04 lr: 3.9854e-04  eta: 0:13:08  time: 0.1424  data_time: 0.0133  memory: 425  loss: 0.8495  loss_cls: 0.3251  loss_bbox: 0.5244
2025/06/09 12:51:07 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:51:07 - mmengine - INFO - Epoch(train) [157][31/31]  base_lr: 3.9801e-04 lr: 3.9801e-04  eta: 0:13:03  time: 0.1479  data_time: 0.0274  memory: 411  loss: 0.8343  loss_cls: 0.3209  loss_bbox: 0.5134
2025/06/09 12:51:12 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:51:12 - mmengine - INFO - Epoch(train) [158][31/31]  base_lr: 3.9740e-04 lr: 3.9740e-04  eta: 0:12:57  time: 0.1502  data_time: 0.0296  memory: 408  loss: 0.8325  loss_cls: 0.3191  loss_bbox: 0.5134
2025/06/09 12:51:17 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:51:17 - mmengine - INFO - Epoch(train) [159][31/31]  base_lr: 3.9670e-04 lr: 3.9670e-04  eta: 0:12:51  time: 0.1426  data_time: 0.0139  memory: 436  loss: 0.8294  loss_cls: 0.3212  loss_bbox: 0.5082
2025/06/09 12:51:23 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:51:23 - mmengine - INFO - Epoch(train) [160][31/31]  base_lr: 3.9593e-04 lr: 3.9593e-04  eta: 0:12:46  time: 0.1530  data_time: 0.0240  memory: 411  loss: 0.8392  loss_cls: 0.3268  loss_bbox: 0.5124
2025/06/09 12:51:23 - mmengine - INFO - Saving checkpoint at 160 epochs
2025/06/09 12:51:25 - mmengine - INFO - Evaluating bbox...
2025/06/09 12:51:27 - mmengine - INFO - bbox_mAP_copypaste: 0.204 0.376 0.183 0.000 0.000 0.410
2025/06/09 12:51:27 - mmengine - INFO - Epoch(val) [160][20/20]    coco/bbox_mAP: 0.2040  coco/bbox_mAP_50: 0.3760  coco/bbox_mAP_75: 0.1830  coco/bbox_mAP_s: 0.0000  coco/bbox_mAP_m: 0.0000  coco/bbox_mAP_l: 0.4100  data_time: 0.0154  time: 0.0596
2025/06/09 12:51:27 - mmengine - INFO - The previous best checkpoint D:\OBJECT_DETECTION\sscma\ModelAssistant\work_dirs\rtmdet_nano_config_motherboard_led_192x192\best_coco_bbox_mAP_epoch_150.pth is removed
2025/06/09 12:51:27 - mmengine - INFO - The best checkpoint with 0.2040 coco/bbox_mAP at 160 epoch is saved to best_coco_bbox_mAP_epoch_160.pth.
2025/06/09 12:51:33 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:51:33 - mmengine - INFO - Epoch(train) [161][31/31]  base_lr: 3.9507e-04 lr: 3.9507e-04  eta: 0:12:40  time: 0.1549  data_time: 0.0237  memory: 419  loss: 0.8355  loss_cls: 0.3263  loss_bbox: 0.5092
2025/06/09 12:51:36 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:51:38 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:51:38 - mmengine - INFO - Epoch(train) [162][31/31]  base_lr: 3.9414e-04 lr: 3.9414e-04  eta: 0:12:35  time: 0.1485  data_time: 0.0268  memory: 422  loss: 0.8324  loss_cls: 0.3207  loss_bbox: 0.5117
2025/06/09 12:51:43 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:51:43 - mmengine - INFO - Epoch(train) [163][31/31]  base_lr: 3.9313e-04 lr: 3.9313e-04  eta: 0:12:29  time: 0.1446  data_time: 0.0175  memory: 411  loss: 0.8337  loss_cls: 0.3218  loss_bbox: 0.5119
2025/06/09 12:51:48 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:51:48 - mmengine - INFO - Epoch(train) [164][31/31]  base_lr: 3.9203e-04 lr: 3.9203e-04  eta: 0:12:23  time: 0.1469  data_time: 0.0206  memory: 417  loss: 0.8492  loss_cls: 0.3305  loss_bbox: 0.5188
2025/06/09 12:51:54 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:51:54 - mmengine - INFO - Epoch(train) [165][31/31]  base_lr: 3.9086e-04 lr: 3.9086e-04  eta: 0:12:18  time: 0.1493  data_time: 0.0230  memory: 414  loss: 0.8420  loss_cls: 0.3314  loss_bbox: 0.5106
2025/06/09 12:51:59 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:51:59 - mmengine - INFO - Epoch(train) [166][31/31]  base_lr: 3.8961e-04 lr: 3.8961e-04  eta: 0:12:12  time: 0.1506  data_time: 0.0140  memory: 414  loss: 0.8241  loss_cls: 0.3244  loss_bbox: 0.4997
2025/06/09 12:52:04 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:52:04 - mmengine - INFO - Epoch(train) [167][31/31]  base_lr: 3.8828e-04 lr: 3.8828e-04  eta: 0:12:06  time: 0.1429  data_time: 0.0136  memory: 411  loss: 0.8279  loss_cls: 0.3237  loss_bbox: 0.5041
2025/06/09 12:52:09 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:52:09 - mmengine - INFO - Epoch(train) [168][31/31]  base_lr: 3.8688e-04 lr: 3.8688e-04  eta: 0:12:00  time: 0.1449  data_time: 0.0194  memory: 427  loss: 0.8169  loss_cls: 0.3170  loss_bbox: 0.4999
2025/06/09 12:52:14 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:52:14 - mmengine - INFO - Epoch(train) [169][31/31]  base_lr: 3.8540e-04 lr: 3.8540e-04  eta: 0:11:55  time: 0.1493  data_time: 0.0297  memory: 414  loss: 0.8164  loss_cls: 0.3169  loss_bbox: 0.4995
2025/06/09 12:52:19 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:52:19 - mmengine - INFO - Epoch(train) [170][31/31]  base_lr: 3.8384e-04 lr: 3.8384e-04  eta: 0:11:49  time: 0.1419  data_time: 0.0120  memory: 417  loss: 0.8207  loss_cls: 0.3219  loss_bbox: 0.4987
2025/06/09 12:52:19 - mmengine - INFO - Saving checkpoint at 170 epochs
2025/06/09 12:52:22 - mmengine - INFO - Evaluating bbox...
2025/06/09 12:52:23 - mmengine - INFO - bbox_mAP_copypaste: 0.211 0.379 0.213 0.000 0.000 0.424
2025/06/09 12:52:23 - mmengine - INFO - Epoch(val) [170][20/20]    coco/bbox_mAP: 0.2110  coco/bbox_mAP_50: 0.3790  coco/bbox_mAP_75: 0.2130  coco/bbox_mAP_s: 0.0000  coco/bbox_mAP_m: 0.0000  coco/bbox_mAP_l: 0.4240  data_time: 0.0138  time: 0.0696
2025/06/09 12:52:23 - mmengine - INFO - The previous best checkpoint D:\OBJECT_DETECTION\sscma\ModelAssistant\work_dirs\rtmdet_nano_config_motherboard_led_192x192\best_coco_bbox_mAP_epoch_160.pth is removed
2025/06/09 12:52:23 - mmengine - INFO - The best checkpoint with 0.2110 coco/bbox_mAP at 170 epoch is saved to best_coco_bbox_mAP_epoch_170.pth.
2025/06/09 12:52:29 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:52:29 - mmengine - INFO - Epoch(train) [171][31/31]  base_lr: 3.8221e-04 lr: 3.8221e-04  eta: 0:11:43  time: 0.1453  data_time: 0.0199  memory: 420  loss: 0.8156  loss_cls: 0.3174  loss_bbox: 0.4982
2025/06/09 12:52:34 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:52:34 - mmengine - INFO - Epoch(train) [172][31/31]  base_lr: 3.8050e-04 lr: 3.8050e-04  eta: 0:11:37  time: 0.1438  data_time: 0.0130  memory: 413  loss: 0.8238  loss_cls: 0.3201  loss_bbox: 0.5037
2025/06/09 12:52:39 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:52:39 - mmengine - INFO - Epoch(train) [173][31/31]  base_lr: 3.7872e-04 lr: 3.7872e-04  eta: 0:11:31  time: 0.1386  data_time: 0.0128  memory: 419  loss: 0.8190  loss_cls: 0.3209  loss_bbox: 0.4981
2025/06/09 12:52:44 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:52:44 - mmengine - INFO - Epoch(train) [174][31/31]  base_lr: 3.7687e-04 lr: 3.7687e-04  eta: 0:11:26  time: 0.1438  data_time: 0.0146  memory: 425  loss: 0.8250  loss_cls: 0.3229  loss_bbox: 0.5020
2025/06/09 12:52:49 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:52:49 - mmengine - INFO - Epoch(train) [175][31/31]  base_lr: 3.7494e-04 lr: 3.7494e-04  eta: 0:11:20  time: 0.1435  data_time: 0.0209  memory: 414  loss: 0.8187  loss_cls: 0.3175  loss_bbox: 0.5012
2025/06/09 12:52:54 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:52:54 - mmengine - INFO - Epoch(train) [176][31/31]  base_lr: 3.7295e-04 lr: 3.7295e-04  eta: 0:11:14  time: 0.1420  data_time: 0.0218  memory: 408  loss: 0.8152  loss_cls: 0.3182  loss_bbox: 0.4971
2025/06/09 12:52:59 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:52:59 - mmengine - INFO - Epoch(train) [177][31/31]  base_lr: 3.7088e-04 lr: 3.7088e-04  eta: 0:11:08  time: 0.1427  data_time: 0.0190  memory: 411  loss: 0.8201  loss_cls: 0.3218  loss_bbox: 0.4983
2025/06/09 12:53:04 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:53:04 - mmengine - INFO - Epoch(train) [178][31/31]  base_lr: 3.6874e-04 lr: 3.6874e-04  eta: 0:11:02  time: 0.1396  data_time: 0.0136  memory: 419  loss: 0.8126  loss_cls: 0.3180  loss_bbox: 0.4946
2025/06/09 12:53:09 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:53:09 - mmengine - INFO - Epoch(train) [179][31/31]  base_lr: 3.6654e-04 lr: 3.6654e-04  eta: 0:10:57  time: 0.1443  data_time: 0.0211  memory: 411  loss: 0.8152  loss_cls: 0.3205  loss_bbox: 0.4947
2025/06/09 12:53:14 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:53:14 - mmengine - INFO - Epoch(train) [180][31/31]  base_lr: 3.6427e-04 lr: 3.6427e-04  eta: 0:10:51  time: 0.1432  data_time: 0.0153  memory: 417  loss: 0.7995  loss_cls: 0.3136  loss_bbox: 0.4859
2025/06/09 12:53:14 - mmengine - INFO - Saving checkpoint at 180 epochs
2025/06/09 12:53:16 - mmengine - INFO - Evaluating bbox...
2025/06/09 12:53:18 - mmengine - INFO - bbox_mAP_copypaste: 0.224 0.394 0.232 0.000 0.000 0.452
2025/06/09 12:53:18 - mmengine - INFO - Epoch(val) [180][20/20]    coco/bbox_mAP: 0.2240  coco/bbox_mAP_50: 0.3940  coco/bbox_mAP_75: 0.2320  coco/bbox_mAP_s: 0.0000  coco/bbox_mAP_m: 0.0000  coco/bbox_mAP_l: 0.4520  data_time: 0.0143  time: 0.0569
2025/06/09 12:53:18 - mmengine - INFO - The previous best checkpoint D:\OBJECT_DETECTION\sscma\ModelAssistant\work_dirs\rtmdet_nano_config_motherboard_led_192x192\best_coco_bbox_mAP_epoch_170.pth is removed
2025/06/09 12:53:18 - mmengine - INFO - The best checkpoint with 0.2240 coco/bbox_mAP at 180 epoch is saved to best_coco_bbox_mAP_epoch_180.pth.
2025/06/09 12:53:23 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:53:23 - mmengine - INFO - Epoch(train) [181][31/31]  base_lr: 3.6193e-04 lr: 3.6193e-04  eta: 0:10:45  time: 0.1412  data_time: 0.0161  memory: 417  loss: 0.7957  loss_cls: 0.3100  loss_bbox: 0.4857
2025/06/09 12:53:28 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:53:28 - mmengine - INFO - Epoch(train) [182][31/31]  base_lr: 3.5952e-04 lr: 3.5952e-04  eta: 0:10:40  time: 0.1407  data_time: 0.0233  memory: 414  loss: 0.7946  loss_cls: 0.3126  loss_bbox: 0.4820
2025/06/09 12:53:34 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:53:34 - mmengine - INFO - Epoch(train) [183][31/31]  base_lr: 3.5705e-04 lr: 3.5705e-04  eta: 0:10:34  time: 0.1442  data_time: 0.0229  memory: 417  loss: 0.7939  loss_cls: 0.3095  loss_bbox: 0.4845
2025/06/09 12:53:39 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:53:39 - mmengine - INFO - Epoch(train) [184][31/31]  base_lr: 3.5452e-04 lr: 3.5452e-04  eta: 0:10:28  time: 0.1435  data_time: 0.0238  memory: 413  loss: 0.7919  loss_cls: 0.3079  loss_bbox: 0.4840
2025/06/09 12:53:44 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:53:44 - mmengine - INFO - Epoch(train) [185][31/31]  base_lr: 3.5192e-04 lr: 3.5192e-04  eta: 0:10:23  time: 0.1472  data_time: 0.0180  memory: 414  loss: 0.7971  loss_cls: 0.3120  loss_bbox: 0.4851
2025/06/09 12:53:49 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:53:49 - mmengine - INFO - Epoch(train) [186][31/31]  base_lr: 3.4927e-04 lr: 3.4927e-04  eta: 0:10:17  time: 0.1446  data_time: 0.0199  memory: 419  loss: 0.7948  loss_cls: 0.3103  loss_bbox: 0.4845
2025/06/09 12:53:54 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:53:54 - mmengine - INFO - Epoch(train) [187][31/31]  base_lr: 3.4655e-04 lr: 3.4655e-04  eta: 0:10:12  time: 0.1454  data_time: 0.0121  memory: 414  loss: 0.7986  loss_cls: 0.3116  loss_bbox: 0.4870
2025/06/09 12:53:59 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:53:59 - mmengine - INFO - Epoch(train) [188][31/31]  base_lr: 3.4378e-04 lr: 3.4378e-04  eta: 0:10:06  time: 0.1495  data_time: 0.0151  memory: 430  loss: 0.7961  loss_cls: 0.3094  loss_bbox: 0.4867
2025/06/09 12:54:04 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:54:04 - mmengine - INFO - Epoch(train) [189][31/31]  base_lr: 3.4094e-04 lr: 3.4094e-04  eta: 0:10:01  time: 0.1472  data_time: 0.0197  memory: 417  loss: 0.7977  loss_cls: 0.3127  loss_bbox: 0.4850
2025/06/09 12:54:10 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:54:10 - mmengine - INFO - Epoch(train) [190][31/31]  base_lr: 3.3806e-04 lr: 3.3806e-04  eta: 0:09:55  time: 0.1460  data_time: 0.0160  memory: 411  loss: 0.7998  loss_cls: 0.3122  loss_bbox: 0.4875
2025/06/09 12:54:10 - mmengine - INFO - Saving checkpoint at 190 epochs
2025/06/09 12:54:13 - mmengine - INFO - Evaluating bbox...
2025/06/09 12:54:14 - mmengine - INFO - bbox_mAP_copypaste: 0.236 0.405 0.260 0.000 0.000 0.476
2025/06/09 12:54:14 - mmengine - INFO - Epoch(val) [190][20/20]    coco/bbox_mAP: 0.2360  coco/bbox_mAP_50: 0.4050  coco/bbox_mAP_75: 0.2600  coco/bbox_mAP_s: 0.0000  coco/bbox_mAP_m: 0.0000  coco/bbox_mAP_l: 0.4760  data_time: 0.0215  time: 0.0661
2025/06/09 12:54:14 - mmengine - INFO - The previous best checkpoint D:\OBJECT_DETECTION\sscma\ModelAssistant\work_dirs\rtmdet_nano_config_motherboard_led_192x192\best_coco_bbox_mAP_epoch_180.pth is removed
2025/06/09 12:54:14 - mmengine - INFO - The best checkpoint with 0.2360 coco/bbox_mAP at 190 epoch is saved to best_coco_bbox_mAP_epoch_190.pth.
2025/06/09 12:54:21 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:54:21 - mmengine - INFO - Epoch(train) [191][31/31]  base_lr: 3.3511e-04 lr: 3.3511e-04  eta: 0:09:50  time: 0.1647  data_time: 0.0403  memory: 409  loss: 0.7879  loss_cls: 0.3067  loss_bbox: 0.4812
2025/06/09 12:54:26 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:54:26 - mmengine - INFO - Epoch(train) [192][31/31]  base_lr: 3.3211e-04 lr: 3.3211e-04  eta: 0:09:44  time: 0.1518  data_time: 0.0278  memory: 419  loss: 0.7860  loss_cls: 0.3064  loss_bbox: 0.4795
2025/06/09 12:54:32 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:54:32 - mmengine - INFO - Epoch(train) [193][31/31]  base_lr: 3.2906e-04 lr: 3.2906e-04  eta: 0:09:39  time: 0.1504  data_time: 0.0240  memory: 422  loss: 0.7881  loss_cls: 0.3070  loss_bbox: 0.4812
2025/06/09 12:54:35 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:54:37 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:54:37 - mmengine - INFO - Epoch(train) [194][31/31]  base_lr: 3.2596e-04 lr: 3.2596e-04  eta: 0:09:34  time: 0.1548  data_time: 0.0190  memory: 417  loss: 0.7969  loss_cls: 0.3079  loss_bbox: 0.4890
2025/06/09 12:54:43 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:54:43 - mmengine - INFO - Epoch(train) [195][31/31]  base_lr: 3.2281e-04 lr: 3.2281e-04  eta: 0:09:28  time: 0.1641  data_time: 0.0377  memory: 427  loss: 0.7836  loss_cls: 0.3043  loss_bbox: 0.4793
2025/06/09 12:54:48 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:54:48 - mmengine - INFO - Epoch(train) [196][31/31]  base_lr: 3.1961e-04 lr: 3.1961e-04  eta: 0:09:23  time: 0.1582  data_time: 0.0239  memory: 411  loss: 0.7807  loss_cls: 0.3050  loss_bbox: 0.4757
2025/06/09 12:54:54 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:54:54 - mmengine - INFO - Epoch(train) [197][31/31]  base_lr: 3.1637e-04 lr: 3.1637e-04  eta: 0:09:18  time: 0.1620  data_time: 0.0385  memory: 415  loss: 0.7796  loss_cls: 0.3050  loss_bbox: 0.4745
2025/06/09 12:55:00 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:55:00 - mmengine - INFO - Epoch(train) [198][31/31]  base_lr: 3.1307e-04 lr: 3.1307e-04  eta: 0:09:12  time: 0.1486  data_time: 0.0134  memory: 413  loss: 0.7750  loss_cls: 0.3032  loss_bbox: 0.4718
2025/06/09 12:55:05 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:55:05 - mmengine - INFO - Epoch(train) [199][31/31]  base_lr: 3.0974e-04 lr: 3.0974e-04  eta: 0:09:07  time: 0.1513  data_time: 0.0274  memory: 409  loss: 0.7767  loss_cls: 0.3026  loss_bbox: 0.4741
2025/06/09 12:55:10 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:55:10 - mmengine - INFO - Epoch(train) [200][31/31]  base_lr: 3.0636e-04 lr: 3.0636e-04  eta: 0:09:01  time: 0.1449  data_time: 0.0212  memory: 417  loss: 0.7813  loss_cls: 0.3049  loss_bbox: 0.4764
2025/06/09 12:55:10 - mmengine - INFO - Saving checkpoint at 200 epochs
2025/06/09 12:55:13 - mmengine - INFO - Evaluating bbox...
2025/06/09 12:55:15 - mmengine - INFO - bbox_mAP_copypaste: 0.233 0.388 0.244 0.000 0.000 0.470
2025/06/09 12:55:15 - mmengine - INFO - Epoch(val) [200][20/20]    coco/bbox_mAP: 0.2330  coco/bbox_mAP_50: 0.3880  coco/bbox_mAP_75: 0.2440  coco/bbox_mAP_s: 0.0000  coco/bbox_mAP_m: 0.0000  coco/bbox_mAP_l: 0.4700  data_time: 0.0605  time: 0.1053
2025/06/09 12:55:20 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:55:20 - mmengine - INFO - Epoch(train) [201][31/31]  base_lr: 3.0294e-04 lr: 3.0294e-04  eta: 0:08:56  time: 0.1538  data_time: 0.0127  memory: 411  loss: 0.7789  loss_cls: 0.3013  loss_bbox: 0.4776
2025/06/09 12:55:26 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:55:26 - mmengine - INFO - Epoch(train) [202][31/31]  base_lr: 2.9948e-04 lr: 2.9948e-04  eta: 0:08:51  time: 0.1580  data_time: 0.0230  memory: 409  loss: 0.7728  loss_cls: 0.2985  loss_bbox: 0.4743
2025/06/09 12:55:31 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:55:31 - mmengine - INFO - Epoch(train) [203][31/31]  base_lr: 2.9598e-04 lr: 2.9598e-04  eta: 0:08:45  time: 0.1543  data_time: 0.0246  memory: 430  loss: 0.7701  loss_cls: 0.2997  loss_bbox: 0.4703
2025/06/09 12:55:37 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:55:37 - mmengine - INFO - Epoch(train) [204][31/31]  base_lr: 2.9245e-04 lr: 2.9245e-04  eta: 0:08:40  time: 0.1514  data_time: 0.0284  memory: 413  loss: 0.7689  loss_cls: 0.2991  loss_bbox: 0.4698
2025/06/09 12:55:42 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:55:42 - mmengine - INFO - Epoch(train) [205][31/31]  base_lr: 2.8888e-04 lr: 2.8888e-04  eta: 0:08:34  time: 0.1491  data_time: 0.0208  memory: 417  loss: 0.7705  loss_cls: 0.3010  loss_bbox: 0.4696
2025/06/09 12:55:47 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:55:47 - mmengine - INFO - Epoch(train) [206][31/31]  base_lr: 2.8528e-04 lr: 2.8528e-04  eta: 0:08:29  time: 0.1450  data_time: 0.0134  memory: 417  loss: 0.7705  loss_cls: 0.3000  loss_bbox: 0.4705
2025/06/09 12:55:52 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:55:52 - mmengine - INFO - Epoch(train) [207][31/31]  base_lr: 2.8164e-04 lr: 2.8164e-04  eta: 0:08:23  time: 0.1424  data_time: 0.0222  memory: 415  loss: 0.7642  loss_cls: 0.2993  loss_bbox: 0.4649
2025/06/09 12:55:57 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:55:57 - mmengine - INFO - Epoch(train) [208][31/31]  base_lr: 2.7798e-04 lr: 2.7798e-04  eta: 0:08:17  time: 0.1416  data_time: 0.0225  memory: 419  loss: 0.7656  loss_cls: 0.2984  loss_bbox: 0.4672
2025/06/09 12:56:03 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:56:03 - mmengine - INFO - Epoch(train) [209][31/31]  base_lr: 2.7428e-04 lr: 2.7428e-04  eta: 0:08:12  time: 0.1482  data_time: 0.0222  memory: 411  loss: 0.7673  loss_cls: 0.2998  loss_bbox: 0.4675
2025/06/09 12:56:08 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:56:08 - mmengine - INFO - Epoch(train) [210][31/31]  base_lr: 2.7056e-04 lr: 2.7056e-04  eta: 0:08:07  time: 0.1496  data_time: 0.0155  memory: 415  loss: 0.7613  loss_cls: 0.2979  loss_bbox: 0.4635
2025/06/09 12:56:08 - mmengine - INFO - Saving checkpoint at 210 epochs
2025/06/09 12:56:10 - mmengine - INFO - Evaluating bbox...
2025/06/09 12:56:12 - mmengine - INFO - bbox_mAP_copypaste: 0.241 0.407 0.257 0.000 0.000 0.486
2025/06/09 12:56:12 - mmengine - INFO - Epoch(val) [210][20/20]    coco/bbox_mAP: 0.2410  coco/bbox_mAP_50: 0.4070  coco/bbox_mAP_75: 0.2570  coco/bbox_mAP_s: 0.0000  coco/bbox_mAP_m: 0.0000  coco/bbox_mAP_l: 0.4860  data_time: 0.0147  time: 0.0611
2025/06/09 12:56:12 - mmengine - INFO - The previous best checkpoint D:\OBJECT_DETECTION\sscma\ModelAssistant\work_dirs\rtmdet_nano_config_motherboard_led_192x192\best_coco_bbox_mAP_epoch_190.pth is removed
2025/06/09 12:56:12 - mmengine - INFO - The best checkpoint with 0.2410 coco/bbox_mAP at 210 epoch is saved to best_coco_bbox_mAP_epoch_210.pth.
2025/06/09 12:56:18 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:56:18 - mmengine - INFO - Epoch(train) [211][31/31]  base_lr: 2.6681e-04 lr: 2.6681e-04  eta: 0:08:01  time: 0.1411  data_time: 0.0146  memory: 425  loss: 0.7573  loss_cls: 0.2982  loss_bbox: 0.4591
2025/06/09 12:56:23 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:56:23 - mmengine - INFO - Epoch(train) [212][31/31]  base_lr: 2.6304e-04 lr: 2.6304e-04  eta: 0:07:55  time: 0.1457  data_time: 0.0172  memory: 417  loss: 0.7542  loss_cls: 0.2922  loss_bbox: 0.4620
2025/06/09 12:56:28 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:56:28 - mmengine - INFO - Epoch(train) [213][31/31]  base_lr: 2.5925e-04 lr: 2.5925e-04  eta: 0:07:50  time: 0.1431  data_time: 0.0222  memory: 414  loss: 0.7468  loss_cls: 0.2896  loss_bbox: 0.4572
2025/06/09 12:56:33 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:56:33 - mmengine - INFO - Epoch(train) [214][31/31]  base_lr: 2.5544e-04 lr: 2.5544e-04  eta: 0:07:44  time: 0.1430  data_time: 0.0182  memory: 418  loss: 0.7477  loss_cls: 0.2901  loss_bbox: 0.4576
2025/06/09 12:56:38 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:56:38 - mmengine - INFO - Epoch(train) [215][31/31]  base_lr: 2.5161e-04 lr: 2.5161e-04  eta: 0:07:39  time: 0.1419  data_time: 0.0197  memory: 411  loss: 0.7463  loss_cls: 0.2887  loss_bbox: 0.4575
2025/06/09 12:56:43 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:56:43 - mmengine - INFO - Epoch(train) [216][31/31]  base_lr: 2.4776e-04 lr: 2.4776e-04  eta: 0:07:33  time: 0.1522  data_time: 0.0364  memory: 414  loss: 0.7557  loss_cls: 0.2919  loss_bbox: 0.4637
2025/06/09 12:56:49 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:56:49 - mmengine - INFO - Epoch(train) [217][31/31]  base_lr: 2.4389e-04 lr: 2.4389e-04  eta: 0:07:28  time: 0.1502  data_time: 0.0252  memory: 418  loss: 0.7504  loss_cls: 0.2935  loss_bbox: 0.4569
2025/06/09 12:56:54 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:56:54 - mmengine - INFO - Epoch(train) [218][31/31]  base_lr: 2.4002e-04 lr: 2.4002e-04  eta: 0:07:22  time: 0.1443  data_time: 0.0143  memory: 417  loss: 0.7482  loss_cls: 0.2951  loss_bbox: 0.4531
2025/06/09 12:56:58 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:56:58 - mmengine - INFO - Epoch(train) [219][31/31]  base_lr: 2.3613e-04 lr: 2.3613e-04  eta: 0:07:17  time: 0.1406  data_time: 0.0149  memory: 411  loss: 0.7346  loss_cls: 0.2865  loss_bbox: 0.4481
2025/06/09 12:57:03 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:57:03 - mmengine - INFO - Epoch(train) [220][31/31]  base_lr: 2.3223e-04 lr: 2.3223e-04  eta: 0:07:11  time: 0.1402  data_time: 0.0137  memory: 408  loss: 0.7443  loss_cls: 0.2889  loss_bbox: 0.4554
2025/06/09 12:57:03 - mmengine - INFO - Saving checkpoint at 220 epochs
2025/06/09 12:57:06 - mmengine - INFO - Evaluating bbox...
2025/06/09 12:57:07 - mmengine - INFO - bbox_mAP_copypaste: 0.249 0.396 0.271 0.000 0.000 0.501
2025/06/09 12:57:07 - mmengine - INFO - Epoch(val) [220][20/20]    coco/bbox_mAP: 0.2490  coco/bbox_mAP_50: 0.3960  coco/bbox_mAP_75: 0.2710  coco/bbox_mAP_s: 0.0000  coco/bbox_mAP_m: 0.0000  coco/bbox_mAP_l: 0.5010  data_time: 0.0144  time: 0.0674
2025/06/09 12:57:07 - mmengine - INFO - The previous best checkpoint D:\OBJECT_DETECTION\sscma\ModelAssistant\work_dirs\rtmdet_nano_config_motherboard_led_192x192\best_coco_bbox_mAP_epoch_210.pth is removed
2025/06/09 12:57:08 - mmengine - INFO - The best checkpoint with 0.2490 coco/bbox_mAP at 220 epoch is saved to best_coco_bbox_mAP_epoch_220.pth.
2025/06/09 12:57:13 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:57:13 - mmengine - INFO - Epoch(train) [221][31/31]  base_lr: 2.2832e-04 lr: 2.2832e-04  eta: 0:07:06  time: 0.1412  data_time: 0.0179  memory: 426  loss: 0.7432  loss_cls: 0.2893  loss_bbox: 0.4539
2025/06/09 12:57:18 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:57:18 - mmengine - INFO - Epoch(train) [222][31/31]  base_lr: 2.2440e-04 lr: 2.2440e-04  eta: 0:07:00  time: 0.1420  data_time: 0.0184  memory: 411  loss: 0.7441  loss_cls: 0.2878  loss_bbox: 0.4563
2025/06/09 12:57:23 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:57:23 - mmengine - INFO - Epoch(train) [223][31/31]  base_lr: 2.2048e-04 lr: 2.2048e-04  eta: 0:06:55  time: 0.1437  data_time: 0.0212  memory: 411  loss: 0.7476  loss_cls: 0.2919  loss_bbox: 0.4558
2025/06/09 12:57:28 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:57:28 - mmengine - INFO - Epoch(train) [224][31/31]  base_lr: 2.1655e-04 lr: 2.1655e-04  eta: 0:06:49  time: 0.1416  data_time: 0.0202  memory: 409  loss: 0.7336  loss_cls: 0.2868  loss_bbox: 0.4468
2025/06/09 12:57:34 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:57:34 - mmengine - INFO - Epoch(train) [225][31/31]  base_lr: 2.1263e-04 lr: 2.1263e-04  eta: 0:06:44  time: 0.1543  data_time: 0.0291  memory: 411  loss: 0.7433  loss_cls: 0.2902  loss_bbox: 0.4531
2025/06/09 12:57:38 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:57:39 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:57:39 - mmengine - INFO - Epoch(train) [226][31/31]  base_lr: 2.0870e-04 lr: 2.0870e-04  eta: 0:06:38  time: 0.1447  data_time: 0.0142  memory: 422  loss: 0.7507  loss_cls: 0.2924  loss_bbox: 0.4583
2025/06/09 12:57:44 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:57:44 - mmengine - INFO - Epoch(train) [227][31/31]  base_lr: 2.0477e-04 lr: 2.0477e-04  eta: 0:06:33  time: 0.1465  data_time: 0.0261  memory: 422  loss: 0.7435  loss_cls: 0.2882  loss_bbox: 0.4554
2025/06/09 12:57:49 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:57:49 - mmengine - INFO - Epoch(train) [228][31/31]  base_lr: 2.0085e-04 lr: 2.0085e-04  eta: 0:06:27  time: 0.1478  data_time: 0.0230  memory: 414  loss: 0.7333  loss_cls: 0.2825  loss_bbox: 0.4508
2025/06/09 12:57:54 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:57:54 - mmengine - INFO - Epoch(train) [229][31/31]  base_lr: 1.9694e-04 lr: 1.9694e-04  eta: 0:06:22  time: 0.1403  data_time: 0.0184  memory: 417  loss: 0.7393  loss_cls: 0.2867  loss_bbox: 0.4526
2025/06/09 12:58:00 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:58:00 - mmengine - INFO - Epoch(train) [230][31/31]  base_lr: 1.9303e-04 lr: 1.9303e-04  eta: 0:06:17  time: 0.1496  data_time: 0.0250  memory: 414  loss: 0.7387  loss_cls: 0.2901  loss_bbox: 0.4486
2025/06/09 12:58:00 - mmengine - INFO - Saving checkpoint at 230 epochs
2025/06/09 12:58:02 - mmengine - INFO - Evaluating bbox...
2025/06/09 12:58:04 - mmengine - INFO - bbox_mAP_copypaste: 0.260 0.401 0.279 0.000 0.000 0.524
2025/06/09 12:58:04 - mmengine - INFO - Epoch(val) [230][20/20]    coco/bbox_mAP: 0.2600  coco/bbox_mAP_50: 0.4010  coco/bbox_mAP_75: 0.2790  coco/bbox_mAP_s: 0.0000  coco/bbox_mAP_m: 0.0000  coco/bbox_mAP_l: 0.5240  data_time: 0.0158  time: 0.0611
2025/06/09 12:58:04 - mmengine - INFO - The previous best checkpoint D:\OBJECT_DETECTION\sscma\ModelAssistant\work_dirs\rtmdet_nano_config_motherboard_led_192x192\best_coco_bbox_mAP_epoch_220.pth is removed
2025/06/09 12:58:04 - mmengine - INFO - The best checkpoint with 0.2600 coco/bbox_mAP at 230 epoch is saved to best_coco_bbox_mAP_epoch_230.pth.
2025/06/09 12:58:10 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:58:10 - mmengine - INFO - Epoch(train) [231][31/31]  base_lr: 1.8913e-04 lr: 1.8913e-04  eta: 0:06:11  time: 0.1522  data_time: 0.0302  memory: 417  loss: 0.7388  loss_cls: 0.2899  loss_bbox: 0.4489
2025/06/09 12:58:16 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:58:16 - mmengine - INFO - Epoch(train) [232][31/31]  base_lr: 1.8523e-04 lr: 1.8523e-04  eta: 0:06:06  time: 0.1579  data_time: 0.0318  memory: 413  loss: 0.7355  loss_cls: 0.2853  loss_bbox: 0.4501
2025/06/09 12:58:21 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:58:21 - mmengine - INFO - Epoch(train) [233][31/31]  base_lr: 1.8136e-04 lr: 1.8136e-04  eta: 0:06:01  time: 0.1571  data_time: 0.0292  memory: 425  loss: 0.7290  loss_cls: 0.2798  loss_bbox: 0.4492
2025/06/09 12:58:27 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:58:27 - mmengine - INFO - Epoch(train) [234][31/31]  base_lr: 1.7749e-04 lr: 1.7749e-04  eta: 0:05:55  time: 0.1579  data_time: 0.0264  memory: 413  loss: 0.7398  loss_cls: 0.2887  loss_bbox: 0.4511
2025/06/09 12:58:32 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:58:32 - mmengine - INFO - Epoch(train) [235][31/31]  base_lr: 1.7364e-04 lr: 1.7364e-04  eta: 0:05:50  time: 0.1511  data_time: 0.0191  memory: 411  loss: 0.7320  loss_cls: 0.2870  loss_bbox: 0.4450
2025/06/09 12:58:38 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:58:38 - mmengine - INFO - Epoch(train) [236][31/31]  base_lr: 1.6981e-04 lr: 1.6981e-04  eta: 0:05:44  time: 0.1516  data_time: 0.0249  memory: 414  loss: 0.7299  loss_cls: 0.2866  loss_bbox: 0.4433
2025/06/09 12:58:43 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:58:43 - mmengine - INFO - Epoch(train) [237][31/31]  base_lr: 1.6599e-04 lr: 1.6599e-04  eta: 0:05:39  time: 0.1501  data_time: 0.0207  memory: 425  loss: 0.7340  loss_cls: 0.2862  loss_bbox: 0.4478
2025/06/09 12:58:48 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:58:48 - mmengine - INFO - Epoch(train) [238][31/31]  base_lr: 1.6220e-04 lr: 1.6220e-04  eta: 0:05:34  time: 0.1497  data_time: 0.0227  memory: 411  loss: 0.7291  loss_cls: 0.2844  loss_bbox: 0.4447
2025/06/09 12:58:54 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:58:54 - mmengine - INFO - Epoch(train) [239][31/31]  base_lr: 1.5843e-04 lr: 1.5843e-04  eta: 0:05:28  time: 0.1593  data_time: 0.0200  memory: 425  loss: 0.7194  loss_cls: 0.2804  loss_bbox: 0.4391
2025/06/09 12:58:59 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:58:59 - mmengine - INFO - Epoch(train) [240][31/31]  base_lr: 1.5468e-04 lr: 1.5468e-04  eta: 0:05:23  time: 0.1549  data_time: 0.0229  memory: 418  loss: 0.7329  loss_cls: 0.2845  loss_bbox: 0.4485
2025/06/09 12:58:59 - mmengine - INFO - Saving checkpoint at 240 epochs
2025/06/09 12:59:02 - mmengine - INFO - Evaluating bbox...
2025/06/09 12:59:04 - mmengine - INFO - bbox_mAP_copypaste: 0.262 0.405 0.289 0.000 0.000 0.528
2025/06/09 12:59:04 - mmengine - INFO - Epoch(val) [240][20/20]    coco/bbox_mAP: 0.2620  coco/bbox_mAP_50: 0.4050  coco/bbox_mAP_75: 0.2890  coco/bbox_mAP_s: 0.0000  coco/bbox_mAP_m: 0.0000  coco/bbox_mAP_l: 0.5280  data_time: 0.0237  time: 0.0736
2025/06/09 12:59:04 - mmengine - INFO - The previous best checkpoint D:\OBJECT_DETECTION\sscma\ModelAssistant\work_dirs\rtmdet_nano_config_motherboard_led_192x192\best_coco_bbox_mAP_epoch_230.pth is removed
2025/06/09 12:59:04 - mmengine - INFO - The best checkpoint with 0.2620 coco/bbox_mAP at 240 epoch is saved to best_coco_bbox_mAP_epoch_240.pth.
2025/06/09 12:59:10 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:59:10 - mmengine - INFO - Epoch(train) [241][31/31]  base_lr: 1.5096e-04 lr: 1.5096e-04  eta: 0:05:17  time: 0.1494  data_time: 0.0137  memory: 427  loss: 0.7297  loss_cls: 0.2839  loss_bbox: 0.4457
2025/06/09 12:59:15 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:59:15 - mmengine - INFO - Epoch(train) [242][31/31]  base_lr: 1.4726e-04 lr: 1.4726e-04  eta: 0:05:12  time: 0.1455  data_time: 0.0253  memory: 415  loss: 0.7154  loss_cls: 0.2808  loss_bbox: 0.4347
2025/06/09 12:59:21 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:59:21 - mmengine - INFO - Epoch(train) [243][31/31]  base_lr: 1.4359e-04 lr: 1.4359e-04  eta: 0:05:07  time: 0.1560  data_time: 0.0212  memory: 425  loss: 0.7203  loss_cls: 0.2806  loss_bbox: 0.4398
2025/06/09 12:59:26 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:59:26 - mmengine - INFO - Epoch(train) [244][31/31]  base_lr: 1.3996e-04 lr: 1.3996e-04  eta: 0:05:01  time: 0.1553  data_time: 0.0240  memory: 415  loss: 0.7239  loss_cls: 0.2816  loss_bbox: 0.4423
2025/06/09 12:59:32 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:59:32 - mmengine - INFO - Epoch(train) [245][31/31]  base_lr: 1.3635e-04 lr: 1.3635e-04  eta: 0:04:56  time: 0.1568  data_time: 0.0302  memory: 436  loss: 0.7222  loss_cls: 0.2811  loss_bbox: 0.4412
2025/06/09 12:59:38 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:59:38 - mmengine - INFO - Epoch(train) [246][31/31]  base_lr: 1.3278e-04 lr: 1.3278e-04  eta: 0:04:51  time: 0.1583  data_time: 0.0178  memory: 413  loss: 0.7189  loss_cls: 0.2759  loss_bbox: 0.4430
2025/06/09 12:59:43 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:59:43 - mmengine - INFO - Epoch(train) [247][31/31]  base_lr: 1.2924e-04 lr: 1.2924e-04  eta: 0:04:45  time: 0.1580  data_time: 0.0300  memory: 411  loss: 0.7150  loss_cls: 0.2743  loss_bbox: 0.4407
2025/06/09 12:59:49 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:59:49 - mmengine - INFO - Epoch(train) [248][31/31]  base_lr: 1.2574e-04 lr: 1.2574e-04  eta: 0:04:40  time: 0.1590  data_time: 0.0344  memory: 406  loss: 0.7108  loss_cls: 0.2734  loss_bbox: 0.4374
2025/06/09 12:59:54 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 12:59:54 - mmengine - INFO - Epoch(train) [249][31/31]  base_lr: 1.2228e-04 lr: 1.2228e-04  eta: 0:04:35  time: 0.1564  data_time: 0.0206  memory: 419  loss: 0.7215  loss_cls: 0.2780  loss_bbox: 0.4435
2025/06/09 13:00:00 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 13:00:00 - mmengine - INFO - Epoch(train) [250][31/31]  base_lr: 1.1886e-04 lr: 1.1886e-04  eta: 0:04:29  time: 0.1568  data_time: 0.0158  memory: 414  loss: 0.7206  loss_cls: 0.2784  loss_bbox: 0.4422
2025/06/09 13:00:00 - mmengine - INFO - Saving checkpoint at 250 epochs
2025/06/09 13:00:03 - mmengine - INFO - Evaluating bbox...
2025/06/09 13:00:05 - mmengine - INFO - bbox_mAP_copypaste: 0.265 0.413 0.287 0.000 0.000 0.534
2025/06/09 13:00:05 - mmengine - INFO - Epoch(val) [250][20/20]    coco/bbox_mAP: 0.2650  coco/bbox_mAP_50: 0.4130  coco/bbox_mAP_75: 0.2870  coco/bbox_mAP_s: 0.0000  coco/bbox_mAP_m: 0.0000  coco/bbox_mAP_l: 0.5340  data_time: 0.0596  time: 0.1093
2025/06/09 13:00:05 - mmengine - INFO - The previous best checkpoint D:\OBJECT_DETECTION\sscma\ModelAssistant\work_dirs\rtmdet_nano_config_motherboard_led_192x192\best_coco_bbox_mAP_epoch_240.pth is removed
2025/06/09 13:00:05 - mmengine - INFO - The best checkpoint with 0.2650 coco/bbox_mAP at 250 epoch is saved to best_coco_bbox_mAP_epoch_250.pth.
2025/06/09 13:00:11 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 13:00:11 - mmengine - INFO - Epoch(train) [251][31/31]  base_lr: 1.1548e-04 lr: 1.1548e-04  eta: 0:04:24  time: 0.1554  data_time: 0.0232  memory: 430  loss: 0.7070  loss_cls: 0.2743  loss_bbox: 0.4327
2025/06/09 13:00:17 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 13:00:17 - mmengine - INFO - Epoch(train) [252][31/31]  base_lr: 1.1214e-04 lr: 1.1214e-04  eta: 0:04:18  time: 0.1527  data_time: 0.0258  memory: 415  loss: 0.7064  loss_cls: 0.2726  loss_bbox: 0.4339
2025/06/09 13:00:22 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 13:00:22 - mmengine - INFO - Epoch(train) [253][31/31]  base_lr: 1.0884e-04 lr: 1.0884e-04  eta: 0:04:13  time: 0.1504  data_time: 0.0190  memory: 411  loss: 0.7199  loss_cls: 0.2772  loss_bbox: 0.4427
2025/06/09 13:00:27 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 13:00:27 - mmengine - INFO - Epoch(train) [254][31/31]  base_lr: 1.0560e-04 lr: 1.0560e-04  eta: 0:04:08  time: 0.1538  data_time: 0.0231  memory: 419  loss: 0.7169  loss_cls: 0.2781  loss_bbox: 0.4388
2025/06/09 13:00:33 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 13:00:33 - mmengine - INFO - Epoch(train) [255][31/31]  base_lr: 1.0239e-04 lr: 1.0239e-04  eta: 0:04:02  time: 0.1481  data_time: 0.0150  memory: 417  loss: 0.7147  loss_cls: 0.2784  loss_bbox: 0.4363
2025/06/09 13:00:38 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 13:00:38 - mmengine - INFO - Epoch(train) [256][31/31]  base_lr: 9.9239e-05 lr: 9.9239e-05  eta: 0:03:57  time: 0.1530  data_time: 0.0286  memory: 417  loss: 0.7241  loss_cls: 0.2831  loss_bbox: 0.4411
2025/06/09 13:00:43 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 13:00:43 - mmengine - INFO - Epoch(train) [257][31/31]  base_lr: 9.6134e-05 lr: 9.6134e-05  eta: 0:03:51  time: 0.1535  data_time: 0.0201  memory: 411  loss: 0.7191  loss_cls: 0.2792  loss_bbox: 0.4400
2025/06/09 13:00:49 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 13:00:49 - mmengine - INFO - Epoch(train) [258][31/31]  base_lr: 9.3081e-05 lr: 9.3081e-05  eta: 0:03:46  time: 0.1567  data_time: 0.0272  memory: 415  loss: 0.7132  loss_cls: 0.2775  loss_bbox: 0.4358
2025/06/09 13:00:50 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 13:00:54 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 13:00:54 - mmengine - INFO - Epoch(train) [259][31/31]  base_lr: 9.0080e-05 lr: 9.0080e-05  eta: 0:03:41  time: 0.1527  data_time: 0.0160  memory: 417  loss: 0.7115  loss_cls: 0.2762  loss_bbox: 0.4353
2025/06/09 13:01:00 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 13:01:00 - mmengine - INFO - Epoch(train) [260][31/31]  base_lr: 8.7132e-05 lr: 8.7132e-05  eta: 0:03:35  time: 0.1544  data_time: 0.0277  memory: 406  loss: 0.7063  loss_cls: 0.2729  loss_bbox: 0.4333
2025/06/09 13:01:00 - mmengine - INFO - Saving checkpoint at 260 epochs
2025/06/09 13:01:02 - mmengine - INFO - Evaluating bbox...
2025/06/09 13:01:04 - mmengine - INFO - bbox_mAP_copypaste: 0.273 0.411 0.301 0.000 0.000 0.551
2025/06/09 13:01:04 - mmengine - INFO - Epoch(val) [260][20/20]    coco/bbox_mAP: 0.2730  coco/bbox_mAP_50: 0.4110  coco/bbox_mAP_75: 0.3010  coco/bbox_mAP_s: 0.0000  coco/bbox_mAP_m: 0.0000  coco/bbox_mAP_l: 0.5510  data_time: 0.0153  time: 0.0735
2025/06/09 13:01:04 - mmengine - INFO - The previous best checkpoint D:\OBJECT_DETECTION\sscma\ModelAssistant\work_dirs\rtmdet_nano_config_motherboard_led_192x192\best_coco_bbox_mAP_epoch_250.pth is removed
2025/06/09 13:01:04 - mmengine - INFO - The best checkpoint with 0.2730 coco/bbox_mAP at 260 epoch is saved to best_coco_bbox_mAP_epoch_260.pth.
2025/06/09 13:01:10 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 13:01:10 - mmengine - INFO - Epoch(train) [261][31/31]  base_lr: 8.4240e-05 lr: 8.4240e-05  eta: 0:03:30  time: 0.1578  data_time: 0.0173  memory: 415  loss: 0.7065  loss_cls: 0.2738  loss_bbox: 0.4328
2025/06/09 13:01:16 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 13:01:16 - mmengine - INFO - Epoch(train) [262][31/31]  base_lr: 8.1404e-05 lr: 8.1404e-05  eta: 0:03:24  time: 0.1546  data_time: 0.0164  memory: 411  loss: 0.7056  loss_cls: 0.2747  loss_bbox: 0.4309
2025/06/09 13:01:21 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 13:01:21 - mmengine - INFO - Epoch(train) [263][31/31]  base_lr: 7.8625e-05 lr: 7.8625e-05  eta: 0:03:19  time: 0.1518  data_time: 0.0151  memory: 411  loss: 0.7136  loss_cls: 0.2756  loss_bbox: 0.4381
2025/06/09 13:01:27 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 13:01:27 - mmengine - INFO - Epoch(train) [264][31/31]  base_lr: 7.5905e-05 lr: 7.5905e-05  eta: 0:03:14  time: 0.1557  data_time: 0.0240  memory: 422  loss: 0.7080  loss_cls: 0.2732  loss_bbox: 0.4348
2025/06/09 13:01:32 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 13:01:32 - mmengine - INFO - Epoch(train) [265][31/31]  base_lr: 7.3245e-05 lr: 7.3245e-05  eta: 0:03:08  time: 0.1571  data_time: 0.0219  memory: 414  loss: 0.7062  loss_cls: 0.2739  loss_bbox: 0.4323
2025/06/09 13:01:38 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 13:01:38 - mmengine - INFO - Epoch(train) [266][31/31]  base_lr: 7.0646e-05 lr: 7.0646e-05  eta: 0:03:03  time: 0.1582  data_time: 0.0173  memory: 411  loss: 0.7070  loss_cls: 0.2735  loss_bbox: 0.4336
2025/06/09 13:01:43 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 13:01:43 - mmengine - INFO - Epoch(train) [267][31/31]  base_lr: 6.8110e-05 lr: 6.8110e-05  eta: 0:02:58  time: 0.1577  data_time: 0.0159  memory: 413  loss: 0.7057  loss_cls: 0.2744  loss_bbox: 0.4313
2025/06/09 13:01:49 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 13:01:49 - mmengine - INFO - Epoch(train) [268][31/31]  base_lr: 6.5636e-05 lr: 6.5636e-05  eta: 0:02:52  time: 0.1615  data_time: 0.0268  memory: 409  loss: 0.7057  loss_cls: 0.2727  loss_bbox: 0.4329
2025/06/09 13:01:55 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 13:01:55 - mmengine - INFO - Epoch(train) [269][31/31]  base_lr: 6.3227e-05 lr: 6.3227e-05  eta: 0:02:47  time: 0.1599  data_time: 0.0225  memory: 409  loss: 0.7065  loss_cls: 0.2717  loss_bbox: 0.4349
2025/06/09 13:02:00 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 13:02:00 - mmengine - INFO - Epoch(train) [270][31/31]  base_lr: 6.0884e-05 lr: 6.0884e-05  eta: 0:02:41  time: 0.1592  data_time: 0.0119  memory: 427  loss: 0.6994  loss_cls: 0.2686  loss_bbox: 0.4308
2025/06/09 13:02:00 - mmengine - INFO - Saving checkpoint at 270 epochs
2025/06/09 13:02:03 - mmengine - INFO - Evaluating bbox...
2025/06/09 13:02:05 - mmengine - INFO - bbox_mAP_copypaste: 0.272 0.411 0.298 0.000 0.000 0.547
2025/06/09 13:02:05 - mmengine - INFO - Epoch(val) [270][20/20]    coco/bbox_mAP: 0.2720  coco/bbox_mAP_50: 0.4110  coco/bbox_mAP_75: 0.2980  coco/bbox_mAP_s: 0.0000  coco/bbox_mAP_m: 0.0000  coco/bbox_mAP_l: 0.5470  data_time: 0.0186  time: 0.0710
2025/06/09 13:02:10 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 13:02:10 - mmengine - INFO - Epoch(train) [271][31/31]  base_lr: 5.8607e-05 lr: 5.8607e-05  eta: 0:02:36  time: 0.1562  data_time: 0.0171  memory: 408  loss: 0.6981  loss_cls: 0.2686  loss_bbox: 0.4295
2025/06/09 13:02:15 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 13:02:15 - mmengine - INFO - Epoch(train) [272][31/31]  base_lr: 5.6397e-05 lr: 5.6397e-05  eta: 0:02:31  time: 0.1486  data_time: 0.0171  memory: 413  loss: 0.6916  loss_cls: 0.2665  loss_bbox: 0.4251
2025/06/09 13:02:21 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 13:02:21 - mmengine - INFO - Epoch(train) [273][31/31]  base_lr: 5.4256e-05 lr: 5.4256e-05  eta: 0:02:25  time: 0.1525  data_time: 0.0270  memory: 425  loss: 0.6832  loss_cls: 0.2640  loss_bbox: 0.4192
2025/06/09 13:02:26 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 13:02:26 - mmengine - INFO - Epoch(train) [274][31/31]  base_lr: 5.2185e-05 lr: 5.2185e-05  eta: 0:02:20  time: 0.1454  data_time: 0.0143  memory: 409  loss: 0.6970  loss_cls: 0.2686  loss_bbox: 0.4284
2025/06/09 13:02:31 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 13:02:31 - mmengine - INFO - Epoch(train) [275][31/31]  base_lr: 5.0184e-05 lr: 5.0184e-05  eta: 0:02:14  time: 0.1460  data_time: 0.0117  memory: 425  loss: 0.7026  loss_cls: 0.2717  loss_bbox: 0.4309
2025/06/09 13:02:37 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 13:02:37 - mmengine - INFO - Epoch(train) [276][31/31]  base_lr: 4.8254e-05 lr: 4.8254e-05  eta: 0:02:09  time: 0.1514  data_time: 0.0206  memory: 419  loss: 0.6957  loss_cls: 0.2678  loss_bbox: 0.4279
2025/06/09 13:02:42 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 13:02:42 - mmengine - INFO - Epoch(train) [277][31/31]  base_lr: 4.6396e-05 lr: 4.6396e-05  eta: 0:02:04  time: 0.1535  data_time: 0.0265  memory: 411  loss: 0.6896  loss_cls: 0.2646  loss_bbox: 0.4250
2025/06/09 13:02:47 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 13:02:47 - mmengine - INFO - Epoch(train) [278][31/31]  base_lr: 4.4610e-05 lr: 4.4610e-05  eta: 0:01:58  time: 0.1494  data_time: 0.0260  memory: 419  loss: 0.6986  loss_cls: 0.2687  loss_bbox: 0.4299
2025/06/09 13:02:53 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 13:02:53 - mmengine - INFO - Epoch(train) [279][31/31]  base_lr: 4.2899e-05 lr: 4.2899e-05  eta: 0:01:53  time: 0.1521  data_time: 0.0314  memory: 415  loss: 0.7107  loss_cls: 0.2753  loss_bbox: 0.4355
2025/06/09 13:02:56 - mmengine - INFO - Evaluating bbox...
2025/06/09 13:02:57 - mmengine - INFO - bbox_mAP_copypaste: 0.274 0.415 0.299 0.000 0.000 0.551
2025/06/09 13:02:57 - mmengine - INFO - Epoch(val) [279][20/20]    coco/bbox_mAP: 0.2740  coco/bbox_mAP_50: 0.4150  coco/bbox_mAP_75: 0.2990  coco/bbox_mAP_s: 0.0000  coco/bbox_mAP_m: 0.0000  coco/bbox_mAP_l: 0.5510  data_time: 0.0553  time: 0.0984
2025/06/09 13:02:57 - mmengine - INFO - The previous best checkpoint D:\OBJECT_DETECTION\sscma\ModelAssistant\work_dirs\rtmdet_nano_config_motherboard_led_192x192\best_coco_bbox_mAP_epoch_260.pth is removed
2025/06/09 13:02:57 - mmengine - INFO - The best checkpoint with 0.2740 coco/bbox_mAP at 279 epoch is saved to best_coco_bbox_mAP_epoch_279.pth.
2025/06/09 13:03:03 - mmengine - INFO - Exp name: rtmdet_nano_config_motherboard_led_192x192_20250609_123456
2025/06/09 13:03:03 - mmengine - INFO - Epoch(train) [280][31/31]  base_lr: 4.1262e-05 lr: 4.1262e-05  eta: 0:01:47  time: 0.1510  data_time: 0.0221  memory: 411  loss: 0.6974  loss_cls: 0.2698  loss_bbox: 0.4276
2025/06/09 13:03:03 - mmengine - INFO - Saving checkpoint at 280 epochs
2025/06/09 13:03:06 - mmengine - INFO - Evaluating bbox...
2025/06/09 13:03:07 - mmengine - INFO - bbox_mAP_copypaste: 0.273 0.416 0.297 0.000 0.000 0.550
2025/06/09 13:03:07 - mmengine - INFO - Epoch(val) [280][20/20]    coco/bbox_mAP: 0.2730  coco/bbox_mAP_50: 0.4160  coco/bbox_mAP_75: 0.2970  coco/bbox_mAP_s: 0.0000  coco/bbox_mAP_m: 0.0000  coco/bbox_mAP_l: 0.5500  data_time: 0.0160  time: 0.0595
2025/06/09 13:03:07 - mmengine - INFO - Switch pipeline now!
