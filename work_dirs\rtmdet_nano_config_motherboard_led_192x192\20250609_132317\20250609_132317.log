2025/06/09 13:23:18 - mmengine - INFO - 
------------------------------------------------------------
System environment:
    sys.platform: win32
    Python: 3.12.11 | packaged by Anaconda, Inc. | (main, Jun  5 2025, 12:58:53) [MSC v.1929 64 bit (AMD64)]
    CUDA available: False
    MUSA available: False
    numpy_random_seed: 643210533
    MSVC: Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35209 for x64
    GCC: n/a
    PyTorch: 2.7.1+cpu
    PyTorch compiling details: PyTorch built with:
  - C++ Version: 201703
  - MSVC 192930158
  - Intel(R) oneAPI Math Kernel Library Version 2025.1-Product Build 20250306 for Intel(R) 64 architecture applications
  - Intel(R) MKL-DNN v3.7.1 (Git Hash 8d263e693366ef8db40acc569cc7d8edf644556d)
  - OpenMP 2019
  - LAPACK is enabled (usually provided by MKL)
  - CPU capability usage: AVX512
  - Build settings: BLAS_INFO=mkl, BUILD_TYPE=Release, COMMIT_SHA=e2d141dbde55c2a4370fac5165b0561b6af4798b, CXX_COMPILER=C:/actions-runner/_work/pytorch/pytorch/pytorch/.ci/pytorch/windows/tmp_bin/sccache-cl.exe, CXX_FLAGS=/DWIN32 /D_WINDOWS /GR /EHsc /Zc:__cplusplus /bigobj /FS /utf-8 -DUSE_PTHREADPOOL -DNDEBUG -DUSE_KINETO -DLIBKINETO_NOCUPTI -DLIBKINETO_NOROCTRACER -DLIBKINETO_NOXPUPTI=ON -DUSE_FBGEMM -DUSE_XNNPACK -DSYMBOLICATE_MOBILE_DEBUG_HANDLE /wd4624 /wd4068 /wd4067 /wd4267 /wd4661 /wd4717 /wd4244 /wd4804 /wd4273, LAPACK_INFO=mkl, PERF_WITH_AVX=1, PERF_WITH_AVX2=1, TORCH_VERSION=2.7.1, USE_CUDA=0, USE_CUDNN=OFF, USE_CUSPARSELT=OFF, USE_GFLAGS=OFF, USE_GLOG=OFF, USE_GLOO=ON, USE_MKL=ON, USE_MKLDNN=ON, USE_MPI=OFF, USE_NCCL=OFF, USE_NNPACK=OFF, USE_OPENMP=ON, USE_ROCM=OFF, USE_ROCM_KERNEL_ASSERT=OFF, 

    TorchVision: n/a, reason: operator torchvision::nms does not exist
    OpenCV: 4.11.0
    MMEngine: 0.10.4

Runtime environment:
    cudnn_benchmark: True
    mp_cfg: {'mp_start_method': 'fork', 'opencv_num_threads': 0}
    dist_cfg: {'backend': 'nccl'}
    seed: 643210533
    deterministic: False
    Distributed launcher: none
    Distributed training: False
    GPU number: 1
------------------------------------------------------------

2025/06/09 13:23:19 - mmengine - INFO - Config:
auto_scale_lr = dict(base_batch_size=32, enable=True)
backend_args = None
base_lr = 0.0005
batch_shapes_cfg = dict(
    batch_size=32,
    extra_pad_ratio=0.5,
    img_size=192,
    size_divisor=32,
    type='sscma.datasets.BatchShapePolicy')
batch_size = 32
checkpoint = None
custom_hooks = [
    dict(
        ema_type='sscma.models.ExpMomentumEMA',
        momentum=0.0002,
        priority=49,
        type='mmengine.hooks.EMAHook',
        update_buffers=True),
    dict(
        switch_epoch=280,
        switch_pipeline=[
            dict(
                backend_args=None,
                imdecode_backend='cv2',
                type='sscma.datasets.transforms.LoadImageFromFile'),
            dict(
                imdecode_backend='cv2',
                type='sscma.datasets.transforms.LoadAnnotations',
                with_bbox=True),
            dict(
                keep_ratio=True,
                ratio_range=(
                    0.8,
                    1.2,
                ),
                resize_type='sscma.datasets.transforms.Resize',
                scale=(
                    384,
                    384,
                ),
                type='sscma.datasets.transforms.RandomResize'),
            dict(
                crop_size=(
                    192,
                    192,
                ),
                type='sscma.datasets.transforms.RandomCrop'),
            dict(type='sscma.datasets.transforms.HSVRandomAug'),
            dict(prob=0.5, type='sscma.datasets.transforms.RandomFlip'),
            dict(
                pad_val=dict(img=(
                    114,
                    114,
                    114,
                )),
                size=(
                    192,
                    192,
                ),
                type='sscma.datasets.transforms.Pad'),
            dict(type='sscma.datasets.transforms.PackDetInputs'),
        ],
        type='sscma.engine.PipelineSwitchHook'),
]
d_factor = 0.33
data_root = '../datasets/motherboard_led_detection_coco/'
dataset_type = 'sscma.datasets.CustomFomoCocoDataset'
default_hooks = dict(
    checkpoint=dict(
        interval=10,
        max_keep_ckpts=3,
        save_best='auto',
        type='mmengine.hooks.CheckpointHook'),
    logger=dict(interval=100, type='mmengine.hooks.LoggerHook'),
    param_scheduler=dict(type='mmengine.hooks.ParamSchedulerHook'),
    sampler_seed=dict(type='mmengine.hooks.DistSamplerSeedHook'),
    timer=dict(type='mmengine.hooks.IterTimerHook'),
    visualization=dict(
        draw=True,
        test_out_dir='works',
        type='sscma.engine.DetVisualizationHook'))
default_scope = None
deploy = dict(
    data_preprocessor=dict(
        batch_augments=None,
        bgr_to_rgb=False,
        mean=[
            0,
            0,
            0,
        ],
        std=[
            255,
            255,
            255,
        ],
        type='sscma.datasets.DetDataPreprocessor'),
    type='sscma.deploy.models.RTMDetInfer')
dump_config = True
env_cfg = dict(
    cudnn_benchmark=True,
    dist_cfg=dict(backend='nccl'),
    mp_cfg=dict(mp_start_method='fork', opencv_num_threads=0))
epochs = 300
imdecode_backend = 'cv2'
imgsz = (
    192,
    192,
)
interval = 10
launcher = 'none'
load_from = None
log_level = 'INFO'
log_processor = dict(
    by_epoch=True, type='mmengine.runner.LogProcessor', window_size=50)
mixup_max_cached_images = 10
model = dict(
    backbone=dict(
        act_cfg=dict(inplace=True, type='torch.nn.ReLU6'),
        arch='P5',
        channel_attention=False,
        deepen_factor=0.33,
        expand_ratio=0.5,
        init_cfg=dict(layer='Conv2d', type='Kaiming'),
        norm_cfg=dict(type='torch.nn.BatchNorm2d'),
        split_max_pool_kernel=False,
        type='sscma.models.CSPNeXt',
        widen_factor=0.25),
    bbox_head=dict(
        bbox_coder=dict(type='sscma.models.DistancePointBBoxCoder'),
        head_module=dict(
            act_cfg=dict(inplace=True, type='torch.nn.ReLU6'),
            feat_channels=256,
            featmap_strides=[
                8,
                16,
                32,
            ],
            in_channels=256,
            norm_cfg=dict(type='torch.nn.BatchNorm2d'),
            num_classes=2,
            pred_kernel_size=1,
            share_conv=False,
            stacked_convs=2,
            type='sscma.models.RTMDetSepBNHeadModule',
            widen_factor=0.25),
        loss_bbox=dict(loss_weight=2.0, type='sscma.models.GIoULoss'),
        loss_cls=dict(
            beta=2.0,
            loss_weight=1.0,
            type='sscma.models.QualityFocalLoss',
            use_sigmoid=True),
        prior_generator=dict(
            offset=0,
            strides=[
                8,
                16,
                32,
            ],
            type='sscma.models.MlvlPointGenerator'),
        test_cfg=dict(
            max_per_img=300,
            min_bbox_size=0,
            multi_label=True,
            nms=dict(iou_threshold=0.65, type='torchvision.ops.nms'),
            nms_pre=30000,
            score_thr=0.001),
        train_cfg=dict(
            allowed_border=-1,
            assigner=dict(
                iou_calculator=dict(type='sscma.models.BboxOverlaps2D'),
                num_classes=2,
                topk=13,
                type='sscma.models.BatchDynamicSoftLabelAssigner'),
            debug=False,
            pos_weight=-1),
        type='sscma.models.RTMDetHead'),
    data_preprocessor=dict(
        batch_augments=None,
        bgr_to_rgb=False,
        mean=[
            0,
            0,
            0,
        ],
        std=[
            255,
            255,
            255,
        ],
        type='sscma.datasets.DetDataPreprocessor'),
    neck=dict(
        act_cfg=dict(inplace=True, type='torch.nn.ReLU6'),
        deepen_factor=0.33,
        expand_ratio=0.5,
        in_channels=[
            256,
            512,
            1024,
        ],
        norm_cfg=dict(type='torch.nn.BatchNorm2d'),
        num_csp_blocks=3,
        out_channels=256,
        type='sscma.models.CSPNeXtPAFPN',
        widen_factor=0.25),
    test_cfg=dict(
        max_per_img=300,
        min_bbox_size=0,
        multi_label=True,
        nms=dict(iou_threshold=0.65, type='torchvision.ops.nms'),
        nms_pre=30000,
        score_thr=0.001),
    train_cfg=dict(
        allowed_border=-1,
        assigner=dict(
            iou_calculator=dict(type='sscma.models.BboxOverlaps2D'),
            num_classes=2,
            topk=13,
            type='sscma.models.BatchDynamicSoftLabelAssigner'),
        debug=False,
        pos_weight=-1),
    type='sscma.models.RTMDet')
mosaic_max_cached_images = 20
num_classes = 2
num_workers = 16
optim_wrapper = dict(
    optimizer=dict(
        lr=0.0004, type='torch.optim.adamw.AdamW', weight_decay=0.05),
    paramwise_cfg=dict(
        bias_decay_mult=0, bypass_duplicate=True, norm_decay_mult=0),
    type='mmengine.optim.AmpOptimWrapper')
param_scheduler = [
    dict(
        begin=0,
        by_epoch=False,
        end=2000,
        start_factor=1e-05,
        type='mmengine.optim.LinearLR'),
    dict(
        T_max=150,
        begin=150,
        by_epoch=True,
        convert_to_iter_based=True,
        end=300,
        eta_min=2.5e-05,
        type='mmengine.optim.CosineAnnealingLR'),
]
quantizer_config = dict(
    bbox_head=dict(
        bbox_coder=dict(type='sscma.models.DistancePointBBoxCoder'),
        head_module=dict(
            act_cfg=dict(inplace=True, type='torch.nn.ReLU6'),
            feat_channels=256,
            featmap_strides=[
                8,
                16,
                32,
            ],
            in_channels=256,
            norm_cfg=dict(type='torch.nn.BatchNorm2d'),
            num_classes=2,
            pred_kernel_size=1,
            share_conv=False,
            stacked_convs=2,
            type='sscma.models.RTMDetSepBNHeadModule',
            widen_factor=0.25),
        loss_bbox=dict(loss_weight=2.0, type='sscma.models.GIoULoss'),
        loss_cls=dict(
            beta=2.0,
            loss_weight=1.0,
            type='sscma.models.QualityFocalLoss',
            use_sigmoid=True),
        prior_generator=dict(
            offset=0,
            strides=[
                8,
                16,
                32,
            ],
            type='sscma.models.MlvlPointGenerator'),
        test_cfg=dict(
            max_per_img=300,
            min_bbox_size=0,
            multi_label=True,
            nms=dict(iou_threshold=0.65, type='torchvision.ops.nms'),
            nms_pre=30000,
            score_thr=0.001),
        train_cfg=dict(
            allowed_border=-1,
            assigner=dict(
                iou_calculator=dict(type='sscma.models.BboxOverlaps2D'),
                num_classes=2,
                topk=13,
                type='sscma.models.BatchDynamicSoftLabelAssigner'),
            debug=False,
            pos_weight=-1),
        type='sscma.models.RTMDetHead'),
    data_preprocessor=dict(
        batch_augments=None,
        bgr_to_rgb=False,
        mean=[
            0,
            0,
            0,
        ],
        std=[
            255,
            255,
            255,
        ],
        type='sscma.datasets.DetDataPreprocessor'),
    type='sscma.quantizer.RtmdetQuantModel')
random_resize_ratio_range = (
    0.5,
    2.0,
)
randomness = dict(deterministic=False, seed=None)
resume = False
stage2_num_epochs = 20
test_cfg = dict(type='mmengine.runner.loops.TestLoop')
test_dataloader = dict(
    batch_size=16,
    dataset=dict(
        ann_file='annotations/instances_val2017.json',
        batch_shapes_cfg=dict(
            batch_size=32,
            extra_pad_ratio=0.5,
            img_size=192,
            size_divisor=32,
            type='sscma.datasets.BatchShapePolicy'),
        data_prefix=dict(img='val2017/'),
        data_root='../datasets/motherboard_led_detection_coco/',
        pipeline=[
            dict(
                backend_args=None,
                imdecode_backend='cv2',
                type='sscma.datasets.transforms.LoadImageFromFile'),
            dict(
                imdecode_backend='cv2',
                type='sscma.datasets.transforms.LoadAnnotations',
                with_bbox=True),
            dict(
                keep_ratio=True,
                scale=(
                    192,
                    192,
                ),
                type='sscma.datasets.transforms.Resize'),
            dict(
                pad_val=dict(img=(
                    114,
                    114,
                    114,
                )),
                size=(
                    192,
                    192,
                ),
                type='sscma.datasets.transforms.Pad'),
            dict(
                meta_keys=(
                    'img_id',
                    'img_path',
                    'ori_shape',
                    'img_shape',
                    'scale_factor',
                ),
                type='sscma.datasets.transforms.PackDetInputs'),
        ],
        test_mode=True,
        type='sscma.datasets.CustomFomoCocoDataset'),
    drop_last=False,
    num_workers=8,
    persistent_workers=True,
    pin_memory=True,
    sampler=dict(
        shuffle=False, type='mmengine.dataset.sampler.DefaultSampler'))
test_evaluator = dict(
    ann_file=
    '../datasets/motherboard_led_detection_coco/annotations/instances_val2017.json',
    backend_args=None,
    format_only=False,
    metric='bbox',
    proposal_nums=(
        100,
        1,
        10,
    ),
    sort_categories=True,
    type='sscma.evaluation.CocoMetric')
test_pipeline = [
    dict(
        backend_args=None,
        imdecode_backend='cv2',
        type='sscma.datasets.transforms.LoadImageFromFile'),
    dict(
        imdecode_backend='cv2',
        type='sscma.datasets.transforms.LoadAnnotations',
        with_bbox=True),
    dict(
        keep_ratio=True,
        scale=(
            192,
            192,
        ),
        type='sscma.datasets.transforms.Resize'),
    dict(
        pad_val=dict(img=(
            114,
            114,
            114,
        )),
        size=(
            192,
            192,
        ),
        type='sscma.datasets.transforms.Pad'),
    dict(
        meta_keys=(
            'img_id',
            'img_path',
            'ori_shape',
            'img_shape',
            'scale_factor',
        ),
        type='sscma.datasets.transforms.PackDetInputs'),
]
train_ann_file = 'annotations/instances_train2017.json'
train_cfg = dict(
    dynamic_intervals=[
        (
            280,
            1,
        ),
    ],
    max_epochs=300,
    type='mmengine.runner.loops.EpochBasedTrainLoop',
    val_interval=10)
train_dataloader = dict(
    batch_sampler=None,
    batch_size=32,
    collate_fn='sscma.datasets.coco_collate',
    dataset=dict(
        ann_file='annotations/instances_train2017.json',
        data_prefix=dict(img='train2017/'),
        data_root='../datasets/motherboard_led_detection_coco/',
        filter_cfg=dict(filter_empty_gt=True, min_size=32),
        pipeline=[
            dict(
                backend_args=None,
                imdecode_backend='cv2',
                type='sscma.datasets.transforms.LoadImageFromFile'),
            dict(
                imdecode_backend='cv2',
                type='sscma.datasets.transforms.LoadAnnotations',
                with_bbox=True),
            dict(
                img_scale=(
                    192,
                    192,
                ),
                max_cached_images=20,
                pad_val=114.0,
                random_pop=False,
                type='sscma.datasets.transforms.Mosaic',
                use_cached=True),
            dict(
                keep_ratio=True,
                ratio_range=(
                    0.8,
                    1.2,
                ),
                resize_type='sscma.datasets.transforms.Resize',
                scale=(
                    384,
                    384,
                ),
                type='sscma.datasets.transforms.RandomResize'),
            dict(
                crop_size=(
                    192,
                    192,
                ),
                type='sscma.datasets.transforms.RandomCrop'),
            dict(type='sscma.datasets.transforms.HSVRandomAug'),
            dict(prob=0.5, type='sscma.datasets.transforms.RandomFlip'),
            dict(
                pad_val=dict(img=(
                    114,
                    114,
                    114,
                )),
                size=(
                    192,
                    192,
                ),
                type='sscma.datasets.transforms.Pad'),
            dict(
                max_cached_images=10,
                prob=0.3,
                random_pop=False,
                type='sscma.datasets.transforms.MixUp',
                use_cached=True),
            dict(type='sscma.datasets.transforms.PackDetInputs'),
        ],
        type='sscma.datasets.CustomFomoCocoDataset'),
    num_workers=16,
    persistent_workers=True,
    pin_memory=True,
    sampler=dict(shuffle=True, type='mmengine.dataset.sampler.DefaultSampler'))
train_img_prefix = 'train2017/'
train_pipeline = [
    dict(
        backend_args=None,
        imdecode_backend='cv2',
        type='sscma.datasets.transforms.LoadImageFromFile'),
    dict(
        imdecode_backend='cv2',
        type='sscma.datasets.transforms.LoadAnnotations',
        with_bbox=True),
    dict(
        img_scale=(
            192,
            192,
        ),
        max_cached_images=20,
        pad_val=114.0,
        random_pop=False,
        type='sscma.datasets.transforms.Mosaic',
        use_cached=True),
    dict(
        keep_ratio=True,
        ratio_range=(
            0.8,
            1.2,
        ),
        resize_type='sscma.datasets.transforms.Resize',
        scale=(
            384,
            384,
        ),
        type='sscma.datasets.transforms.RandomResize'),
    dict(crop_size=(
        192,
        192,
    ), type='sscma.datasets.transforms.RandomCrop'),
    dict(type='sscma.datasets.transforms.HSVRandomAug'),
    dict(prob=0.5, type='sscma.datasets.transforms.RandomFlip'),
    dict(
        pad_val=dict(img=(
            114,
            114,
            114,
        )),
        size=(
            192,
            192,
        ),
        type='sscma.datasets.transforms.Pad'),
    dict(
        max_cached_images=10,
        prob=0.3,
        random_pop=False,
        type='sscma.datasets.transforms.MixUp',
        use_cached=True),
    dict(type='sscma.datasets.transforms.PackDetInputs'),
]
train_pipeline_stage2 = [
    dict(
        backend_args=None,
        imdecode_backend='cv2',
        type='sscma.datasets.transforms.LoadImageFromFile'),
    dict(
        imdecode_backend='cv2',
        type='sscma.datasets.transforms.LoadAnnotations',
        with_bbox=True),
    dict(
        keep_ratio=True,
        ratio_range=(
            0.8,
            1.2,
        ),
        resize_type='sscma.datasets.transforms.Resize',
        scale=(
            384,
            384,
        ),
        type='sscma.datasets.transforms.RandomResize'),
    dict(crop_size=(
        192,
        192,
    ), type='sscma.datasets.transforms.RandomCrop'),
    dict(type='sscma.datasets.transforms.HSVRandomAug'),
    dict(prob=0.5, type='sscma.datasets.transforms.RandomFlip'),
    dict(
        pad_val=dict(img=(
            114,
            114,
            114,
        )),
        size=(
            192,
            192,
        ),
        type='sscma.datasets.transforms.Pad'),
    dict(type='sscma.datasets.transforms.PackDetInputs'),
]
val_ann_file = 'annotations/instances_val2017.json'
val_cfg = dict(type='mmengine.runner.loops.ValLoop')
val_dataloader = dict(
    batch_size=16,
    dataset=dict(
        ann_file='annotations/instances_val2017.json',
        batch_shapes_cfg=dict(
            batch_size=32,
            extra_pad_ratio=0.5,
            img_size=192,
            size_divisor=32,
            type='sscma.datasets.BatchShapePolicy'),
        data_prefix=dict(img='val2017/'),
        data_root='../datasets/motherboard_led_detection_coco/',
        pipeline=[
            dict(
                backend_args=None,
                imdecode_backend='cv2',
                type='sscma.datasets.transforms.LoadImageFromFile'),
            dict(
                imdecode_backend='cv2',
                type='sscma.datasets.transforms.LoadAnnotations',
                with_bbox=True),
            dict(
                keep_ratio=True,
                scale=(
                    192,
                    192,
                ),
                type='sscma.datasets.transforms.Resize'),
            dict(
                pad_val=dict(img=(
                    114,
                    114,
                    114,
                )),
                size=(
                    192,
                    192,
                ),
                type='sscma.datasets.transforms.Pad'),
            dict(
                meta_keys=(
                    'img_id',
                    'img_path',
                    'ori_shape',
                    'img_shape',
                    'scale_factor',
                ),
                type='sscma.datasets.transforms.PackDetInputs'),
        ],
        test_mode=True,
        type='sscma.datasets.CustomFomoCocoDataset'),
    drop_last=False,
    num_workers=8,
    persistent_workers=True,
    pin_memory=True,
    sampler=dict(
        shuffle=False, type='mmengine.dataset.sampler.DefaultSampler'))
val_evaluator = dict(
    ann_file=
    '../datasets/motherboard_led_detection_coco/annotations/instances_val2017.json',
    backend_args=None,
    format_only=False,
    metric='bbox',
    proposal_nums=(
        100,
        1,
        10,
    ),
    sort_categories=True,
    type='sscma.evaluation.CocoMetric')
val_img_prefix = 'val2017/'
vis_backends = [
    dict(type='mmengine.visualization.LocalVisBackend'),
    dict(type='mmengine.visualization.TensorboardVisBackend'),
]
visualizer = dict(
    name='visualizer',
    type='sscma.visualization.DetLocalVisualizer',
    vis_backends=[
        dict(type='mmengine.visualization.LocalVisBackend'),
        dict(type='mmengine.visualization.TensorboardVisBackend'),
    ])
w_factor = 0.25
work_dir = 'work_dirs\\rtmdet_nano_config_motherboard_led_192x192'

